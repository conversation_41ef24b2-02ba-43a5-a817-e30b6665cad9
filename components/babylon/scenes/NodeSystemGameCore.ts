/**
 * Node System 游戏核心类
 * 负责游戏的基础逻辑、场景管理和动态功能注入
 */

import { 
  Engine, 
  Scene, 
  Color3
} from '@babylonjs/core';
import '@babylonjs/loaders/glTF';

// 功能模块接口
export interface GameFeature {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  addedAt: Date;
}

export class NodeSystemGameCore {
  private engine: Engine | null = null;
  private scene: Scene | null = null;
  private canvas: HTMLCanvasElement | null = null;
  
  // 动态功能管理
  private features: Map<string, GameFeature> = new Map();
  private featureCleanups: Map<string, (() => void)> = new Map();
  
  // 状态回调
  private onFeatureUpdateCallback?: (features: GameFeature[]) => void;

  constructor() {
    console.log('[GameCore] 游戏核心初始化');
  }

  /**
   * 初始化游戏
   */
  async initialize(canvas: HTMLCanvasElement): Promise<void> {
    try {
      this.canvas = canvas;
      
      // 创建引擎和场景
      this.engine = new Engine(canvas, true, {
        preserveDrawingBuffer: true,
        stencil: true
      });
      
      this.scene = new Scene(this.engine);
      this.scene.clearColor = Color3.FromHexString('#334155').toColor4(1.0);

      console.log('[GameCore] 游戏初始化完成');
      
    } catch (error) {
      console.error('[GameCore] 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 添加功能模块
   */
  async addFeature(featureData: {
    id: string;
    name: string;
    description: string;
    scriptContent: string;
  }): Promise<boolean> {
    try {
      if (!this.scene) {
        throw new Error('场景未初始化');
      }

      console.log(`[GameCore] 添加功能: ${featureData.name}`);

      // 创建功能执行函数
      const executeFunction = this.createFeatureFunction(featureData.scriptContent);

      // 创建功能对象
      const feature: GameFeature = {
        id: featureData.id,
        name: featureData.name,
        description: featureData.description,
        isActive: false,
        addedAt: new Date()
      };

      // 执行功能并获取清理函数
      const cleanup = executeFunction(this.scene);
      
      // 保存功能和清理函数
      this.features.set(feature.id, { ...feature, isActive: true });
      
      if (cleanup && typeof cleanup === 'function') {
        this.featureCleanups.set(feature.id, cleanup);
      }

      // 通知UI更新
      this.onFeatureUpdateCallback?.(Array.from(this.features.values()));

      console.log(`[GameCore] 功能 "${featureData.name}" 添加成功`);
      return true;

    } catch (error) {
      console.error(`[GameCore] 添加功能失败:`, error);
      return false;
    }
  }

  /**
   * 移除功能模块
   */
  removeFeature(featureId: string): boolean {
    try {
      const feature = this.features.get(featureId);
      if (!feature) {
        console.warn(`[GameCore] 功能不存在: ${featureId}`);
        return false;
      }

      console.log(`[GameCore] 移除功能: ${feature.name}`);

      // 执行清理函数
      const cleanup = this.featureCleanups.get(featureId);
      if (cleanup) {
        cleanup();
        this.featureCleanups.delete(featureId);
      }

      // 移除功能
      this.features.delete(featureId);

      // 通知UI更新
      this.onFeatureUpdateCallback?.(Array.from(this.features.values()));

      console.log(`[GameCore] 功能 "${feature.name}" 移除成功`);
      return true;

    } catch (error) {
      console.error(`[GameCore] 移除功能失败:`, error);
      return false;
    }
  }

  /**
   * 创建功能执行函数
   */
  private createFeatureFunction(scriptContent: string): (scene: Scene) => (() => void) | void {
    try {
      // 处理脚本内容，移除TypeScript语法
      let processedContent = scriptContent;
      
      // 移除import/export语句
      processedContent = processedContent.replace(/import\s+.*?from\s+['"][^'"]*['"];?\s*/g, '');
      processedContent = processedContent.replace(/export\s+(function|const|let|var)/g, '$1');
      processedContent = processedContent.replace(/export\s*{\s*[^}]*\s*}\s*;?\s*/g, '');
      
      // 移除TypeScript类型注解
      processedContent = processedContent.replace(/\(\s*(\w+)\s*:\s*[a-zA-Z0-9_.]+\s*\)/g, '($1)');
      processedContent = processedContent.replace(/(let|const|var)\s+(\w+)\s*:\s*[a-zA-Z0-9_.|<>\[\]]+\s*=/g, '$1 $2 =');
      
      // 返回一个函数，该函数接受scene并执行脚本
      return (scene: Scene) => {
        try {
          // 创建执行环境
          const scriptFunction = new Function(
            'scene',
            `
            // 提供Node系统的Mock实现
            const NodeRegistry = {
              getInstance: () => ({
                getNode: (id) => {
                  const meshes = scene.meshes;
                  let mesh = meshes.find(m => m.name === id || m.id === id);
                  
                  if (!mesh) {
                    // 智能匹配相似节点
                    const similarNames = ['ground', 'box', 'sphere', 'cube', 'plane'];
                    for (const name of similarNames) {
                      mesh = meshes.find(m => m.name.includes(name) || name.includes(m.name));
                      if (mesh) {
                        console.log('[GameCore] 使用相似节点:', mesh.name, '代替', id);
                        break;
                      }
                    }
                  }
                  
                  return mesh;
                },
                getAllNodes: () => scene.meshes,
                addNode: () => {},
                removeNode: () => {}
              })
            };
            
            const NodeCommunicationService = function() {
              this.sendMessage = (msg) => console.log('[GameCore] 消息:', msg);
              this.broadcast = (msg) => console.log('[GameCore] 广播:', msg);
            };
            
            // 执行脚本内容
            ${processedContent}
            
            // 查找并执行executeScript函数
            if (typeof executeScript === 'function') {
              console.log('[GameCore] 执行功能脚本');
              return executeScript(scene);
            } else {
              console.warn('[GameCore] 未找到executeScript函数');
              return null;
            }
            `
          );

          return scriptFunction(scene);

        } catch (error) {
          console.error('[GameCore] 执行脚本失败:', error);
          return undefined;
        }
      };

    } catch (error) {
      console.error('[GameCore] 创建功能函数失败:', error);
      throw error;
    }
  }

  /**
   * 获取场景对象
   */
  getScene(): Scene | null {
    return this.scene;
  }

  /**
   * 获取引擎对象
   */
  getEngine(): Engine | null {
    return this.engine;
  }

  /**
   * 获取所有功能
   */
  getFeatures(): GameFeature[] {
    return Array.from(this.features.values());
  }

  /**
   * 设置功能更新回调
   */
  setFeatureUpdateCallback(callback: (features: GameFeature[]) => void): void {
    this.onFeatureUpdateCallback = callback;
  }

  /**
   * 获取场景上下文信息
   */
  getSceneContext(): Record<string, unknown> {
    if (!this.scene) {
      return {};
    }

    const features = Array.from(this.features.values());

    return {
      features: features.map(f => ({
        id: f.id,
        name: f.name,
        description: f.description,
        isActive: f.isActive
      })),
      sceneInfo: {
        meshCount: this.scene.meshes.length,
        lightCount: this.scene.lights.length,
        cameraCount: this.scene.cameras.length
      }
    };
  }

  /**
   * 销毁游戏
   */
  dispose(): void {
    console.log('[GameCore] 开始销毁游戏');

    // 清理所有功能
    for (const [featureId] of this.features) {
      this.removeFeature(featureId);
    }

    // 销毁场景和引擎
    if (this.scene) {
      this.scene.dispose();
      this.scene = null;
    }

    if (this.engine) {
      this.engine.dispose();
      this.engine = null;
    }

    // 清理引用
    this.canvas = null;

    console.log('[GameCore] 游戏销毁完成');
  }
} 