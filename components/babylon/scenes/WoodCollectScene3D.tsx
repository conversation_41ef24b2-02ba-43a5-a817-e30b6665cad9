"use client";

/**
 * WoodCollectScene3D.tsx
 * 3D木材收集经营游戏场景实现 - 集成3D角色模型版本
 * 基于Cocos Creator项目复刻，使用Babylon.js实现
 * 新增功能：真实3D角色模型、动画状态机、优化的控制系统
 */

import {
  ArcRotateCamera,
  Vector3,
  HemisphericLight,
  DirectionalLight,
  MeshBuilder,
  Scene,
  Mesh,
  StandardMaterial,
  PBRMaterial,
  Texture,
  Color3,
  ShadowGenerator,
  AbstractMesh,
  Quaternion,
  SceneLoader,
  Material,
  Vector2
} from "@babylonjs/core";
import { WaterMaterial } from "@babylonjs/materials";

import { PlayerModelLoader, PlayerModelResult, DEFAULT_PLAYER_CONFIG } from "../../../src/components/3d/PlayerModelLoader";
import { PlayerAnimationController, PlayerAnimationState } from "../../../src/components/3d/PlayerAnimationController";
import { JiabanSceneLoader, createJiabanSceneLoader } from "./JiabanSceneLoader";
import { CocosWaterMaterial } from "../materials/CocosWaterMaterial";
import { FactoryUnlockManager, FactoryUnlockManagerConfig } from "./wood-collect/FactoryUnlockManager";
import { WoodCollectionSystem, WoodCollectionConfig } from "./wood-collect/WoodCollectionSystem";

// 添加类型扩展声明，支持场景自定义属性
declare module '@babylonjs/core' {
  interface Scene {
    shadowGenerator?: ShadowGenerator;
    oceanWaterMaterial?: Material; // 简化为Material类型
    addToWaterRenderList?: () => void; // 添加网格到水面反射列表的函数
  }
}

// 游戏对象引用
let scene: Scene;
let camera: ArcRotateCamera;
let player: AbstractMesh | null = null;
let ground: Mesh;
let playerModelLoader: PlayerModelLoader | null = null;
let animationController: PlayerAnimationController | null = null;
let playerModelResult: PlayerModelResult | null = null;
let jiabanSceneLoader: JiabanSceneLoader | null = null;

// 金币堆叠系统
let coinStackMeshes: AbstractMesh[] = [];
let coinModelTemplate: AbstractMesh | null = null;

// 木材堆叠系统
let woodStackMeshes: AbstractMesh[] = [];
let woodModelTemplate: AbstractMesh | null = null;
let woodCollectionSystem: WoodCollectionSystem | null = null;

// 工厂解锁系统
let factoryUnlockManager: FactoryUnlockManager | null = null;

// 游戏状态
let gameState = {
  coins: 100, // 初始化100金币
  wood: 0,
  customers: 0,
  workers: 0,
  gameStarted: false,
  currentLevel: 1,
  playerPosition: Vector3.Zero()
};

// 玩家移动状态
let playerMovement = {
  direction: { x: 0, y: 0 },
  speed: 5.0,
  isMoving: false,
  velocity: Vector3.Zero(),
  targetQuaternion: Quaternion.Identity(),  // 目标旋转四元数
  currentQuaternion: Quaternion.Identity()  // 当前旋转四元数
};

// 相机参数（基于原Cocos Creator项目配置）
const CAMERA_CONFIG = {
  // 基础相机配置 - 对应原项目43度俯视角
  alpha: Math.PI / 4,   // 45度方位角（对应原项目azimuthAngle）
  beta: Math.PI * 43 / 180,    // 43度极角（对应原项目polarAngle）
  radius: 12,           // 相机到目标的距离（横屏模式）
  target: Vector3.Zero(), // 相机目标点

  // 横竖屏适配参数（基于原游戏精确配置）
  landscape: {
    alpha: Math.PI / 4,        // 45度方位角
    beta: Math.PI * 43 / 180,  // 43度极角，更平缓的俯视角
    radius: 12                 // 12单位距离
  },
  portrait: {
    alpha: Math.PI / 4,        // 45度方位角保持一致
    beta: Math.PI * 60 / 180,  // 60度极角，适合竖屏显示
    radius: 15                 // 15单位距离，更远的视野
  }
};

/**
 * 创建3D木材收集游戏场景 - 3D模型版本
 * 实现45度俯视角、基础环境、3D角色模型
 */
export const onSceneReady = async (babylonScene: Scene) => {
  scene = babylonScene;
  
  console.log('[WoodCollectScene3D] 初始化3D木材收集游戏场景');
  
  try {
    // 启用物理引擎
    enablePhysics();
    
    // 创建45度俯视角相机
    createCamera();
    
    // 创建光照系统
    createLighting();
    
    // 创建基础环境（使用夹板场景）
    await createJiabanEnvironment();
    
    // 加载3D角色模型
    await load3DPlayerModel();

    // 加载金币模型并创建堆叠效果
    await loadCoinModel();
    await createCoinStack();

    // 创建木材模板并初始化堆叠效果
    createWoodTemplate();
    createWoodStack();

    // 初始化木材收集系统
    initializeWoodCollectionSystem();

    // 初始化工厂解锁系统
    await initializeFactoryUnlockSystem();

    // 设置相机跟随
    setupCameraFollow();

    console.log('[WoodCollectScene3D] 场景初始化完成');
  } catch (error) {
    console.error('[WoodCollectScene3D] 场景初始化失败:', error);
    // 回退到胶囊体角色
    createFallbackPlayer();
    setupCameraFollow();
  }
};

/**
 * 启用碰撞检测系统
 * 基于Babylon.js官方文档的碰撞检测最佳实践
 */
function enablePhysics() {
  // 启用场景碰撞检测系统
  scene.collisionsEnabled = true;
  console.log('[WoodCollectScene3D] 场景碰撞检测已启用');
}

/**
 * 创建43度俯视角相机
 * 基于ArcRotateCamera实现固定视角，匹配原Cocos Creator项目
 */
function createCamera() {
  // 检测屏幕方向，使用对应的相机配置
  const isLandscape = window.innerWidth > window.innerHeight;
  const config = isLandscape ? CAMERA_CONFIG.landscape : CAMERA_CONFIG.portrait;
  
  // 创建弧形旋转相机（ArcRotateCamera）
  camera = new ArcRotateCamera(
    "camera",
    config.alpha,
    config.beta,
    config.radius,
    CAMERA_CONFIG.target,
    scene
  );

  // 禁用所有相机控制（实现固定俯视角，匹配原项目体验）
  camera.inputs.clear();

  // 设置相机限制（确保始终保持固定视角）
  camera.lowerAlphaLimit = config.alpha;
  camera.upperAlphaLimit = config.alpha;
  camera.lowerBetaLimit = config.beta;
  camera.upperBetaLimit = config.beta;
  camera.lowerRadiusLimit = config.radius;
  camera.upperRadiusLimit = config.radius;

  // 添加屏幕旋转监听，支持横竖屏切换
  window.addEventListener('resize', handleScreenResize);

  console.log(`[WoodCollectScene3D] 相机创建完成 - ${isLandscape ? '横屏' : '竖屏'}模式`);
  // console.log(`[WoodCollectScene3D] 相机参数: alpha=${config.alpha * 180 / Math.PI}°, beta=${config.beta * 180 / Math.PI}°, radius=${config.radius}`);
}

/**
 * 处理屏幕旋转事件
 * 动态调整相机参数以适配横竖屏
 */
function handleScreenResize() {
  if (!camera) return;
  
  const isLandscape = window.innerWidth > window.innerHeight;
  const config = isLandscape ? CAMERA_CONFIG.landscape : CAMERA_CONFIG.portrait;
  
  // 平滑过渡到新的相机参数
  camera.setTarget(CAMERA_CONFIG.target);
  camera.alpha = config.alpha;
  camera.beta = config.beta;
  camera.radius = config.radius;
  
  // 更新相机限制
  camera.lowerAlphaLimit = config.alpha;
  camera.upperAlphaLimit = config.alpha;
  camera.lowerBetaLimit = config.beta;
  camera.upperBetaLimit = config.beta;
  camera.lowerRadiusLimit = config.radius;
  camera.upperRadiusLimit = config.radius;
  
  // console.log(`[WoodCollectScene3D] 屏幕旋转 - 切换到${isLandscape ? '横屏' : '竖屏'}模式`);
}

/**
 * 创建光照系统
 * 包括环境光和方向光，支持阴影
 */
function createLighting() {
  // 环境光（提供基础照明）
  const hemisphericLight = new HemisphericLight(
    "hemisphericLight",
    new Vector3(0, 1, 0),
    scene
  );
  hemisphericLight.intensity = 0.7;
  hemisphericLight.diffuse = new Color3(1, 0.95, 0.8); // 暖色调
  hemisphericLight.specular = new Color3(0.1, 0.1, 0.1);

  // 主方向光（模拟太阳光，用于阴影）
  const directionalLight = new DirectionalLight(
    "directionalLight",
    new Vector3(-0.5, -1, -0.5), // 45度角照射
    scene
  );
  directionalLight.intensity = 1.2;
  directionalLight.diffuse = new Color3(1, 0.9, 0.7); // 温暖的阳光色
  directionalLight.specular = new Color3(0.3, 0.3, 0.2);

  // 设置阴影范围
  directionalLight.position = new Vector3(20, 30, 20);

  // 创建阴影生成器
  const shadowGenerator = new ShadowGenerator(2048, directionalLight);
  shadowGenerator.useBlurExponentialShadowMap = true;
  shadowGenerator.blurKernel = 64;
  shadowGenerator.blurScale = 2;
  shadowGenerator.setDarkness(0.3); // 阴影不要太深

  // 存储阴影生成器供其他对象使用
  scene.shadowGenerator = shadowGenerator;

  console.log('[WoodCollectScene3D] 光照系统创建完成');
}

/**
 * 创建夹板场景环境
 * 使用真实的夹板3D模型替换几何体地面
 */
async function createJiabanEnvironment() {
  console.log('[WoodCollectScene3D] 开始创建夹板场景环境...');

  try {
    // 创建夹板场景加载器
    jiabanSceneLoader = createJiabanSceneLoader(scene, {
      modelPath: "/models/jiaban/jiaban.glb",
      texturesPath: "/models/jiaban/textures",
      scale: new Vector3(1, 1, 1),
      position: new Vector3(0, 0, 0),
      rotation: new Vector3(0, 0, 0)
    });

    // 加载夹板场景
    const jiabanMeshes = await jiabanSceneLoader.loadScene();

    // 分析夹板模型的实际边界
    analyzeDeckBounds(jiabanMeshes);

    // 设置夹板场景网格接收阴影
    jiabanMeshes.forEach(mesh => {
      if (mesh.name.includes("root") || mesh.name === "__root__") {
        return;
      }
      mesh.receiveShadows = true;

      // 添加到阴影投射列表
      const shadowGenerator = scene.shadowGenerator;
      if (shadowGenerator) {
        shadowGenerator.getShadowMap()?.renderList?.push(mesh);
      }
    });

    // 创建一个不可见的地面用于碰撞检测（保持原有的游戏逻辑）
    ground = MeshBuilder.CreateGround("ground", { width: 60, height: 60 }, scene);
    ground.isVisible = false; // 隐藏几何体地面
    ground.checkCollisions = true; // 保持碰撞检测

    // 创建夹板边界碰撞体，限制角色只能在夹板区域移动
    createDeckBoundaries();

    // 创建海水填充区域，模拟原Cocos Creator项目的水面效果
    await createOceanWater();

    console.log('[WoodCollectScene3D] 夹板场景环境创建完成');

  } catch (error) {
    console.error('[WoodCollectScene3D] 夹板场景加载失败，回退到基础环境:', error);

    // 回退到原有的几何体地面
    ground = MeshBuilder.CreateGround("ground", { width: 60, height: 60 }, scene);
    const groundMaterial = new StandardMaterial("groundMaterial", scene);
    groundMaterial.diffuseColor = new Color3(0.3, 0.5, 0.2);
    ground.material = groundMaterial;
    ground.receiveShadows = true;
  }
}

/**
 * 分析夹板模型的实际边界
 * 用于确定准确的碰撞边界尺寸
 */
function analyzeDeckBounds(meshes: AbstractMesh[]) {
  console.log('[WoodCollectScene3D] 开始分析夹板模型边界...');

  let minX = Infinity, maxX = -Infinity;
  let minZ = Infinity, maxZ = -Infinity;
  let minY = Infinity, maxY = -Infinity;

  // 遍历所有网格，计算包围盒
  meshes.forEach(mesh => {
    if (mesh.name.includes("root") || mesh.name === "__root__") {
      return;
    }

    // 获取网格的包围盒信息
    const boundingInfo = mesh.getBoundingInfo();
    const min = boundingInfo.minimum;
    const max = boundingInfo.maximum;

    // 转换到世界坐标
    const worldMatrix = mesh.getWorldMatrix();
    const minWorld = Vector3.TransformCoordinates(min, worldMatrix);
    const maxWorld = Vector3.TransformCoordinates(max, worldMatrix);

    // 更新总体边界
    minX = Math.min(minX, minWorld.x, maxWorld.x);
    maxX = Math.max(maxX, minWorld.x, maxWorld.x);
    minZ = Math.min(minZ, minWorld.z, maxWorld.z);
    maxZ = Math.max(maxZ, minWorld.z, maxWorld.z);
    minY = Math.min(minY, minWorld.y, maxWorld.y);
    maxY = Math.max(maxY, minWorld.y, maxWorld.y);

    // console.log(`[WoodCollectScene3D] 网格 ${mesh.name}: 边界 (${minWorld.x.toFixed(2)}, ${minWorld.y.toFixed(2)}, ${minWorld.z.toFixed(2)}) 到 (${maxWorld.x.toFixed(2)}, ${maxWorld.y.toFixed(2)}, ${maxWorld.z.toFixed(2)})`);
  });

  const deckWidth = maxX - minX;
  const deckDepth = maxZ - minZ;
  const deckHeight = maxY - minY;

  console.log('[WoodCollectScene3D] === 夹板模型分析结果 ===');
  console.log(`总体边界: X(${minX.toFixed(2)} 到 ${maxX.toFixed(2)}), Z(${minZ.toFixed(2)} 到 ${maxZ.toFixed(2)}), Y(${minY.toFixed(2)} 到 ${maxY.toFixed(2)})`);
  console.log(`夹板尺寸: 宽度=${deckWidth.toFixed(2)}, 深度=${deckDepth.toFixed(2)}, 高度=${deckHeight.toFixed(2)}`);
  console.log(`建议边界参数: deckWidth=${Math.ceil(deckWidth * 0.8)}, deckDepth=${Math.ceil(deckDepth * 0.8)}`);
  console.log('================================');

  // 存储分析结果供边界创建使用
  const deckAnalysis = {
    width: deckWidth,
    depth: deckDepth,
    height: deckHeight,
    bounds: { minX, maxX, minZ, maxZ, minY, maxY }
  };

  // 存储到全局变量供调试使用
  (globalThis as { deckAnalysis?: typeof deckAnalysis }).deckAnalysis = deckAnalysis;
}

/**
 * 创建夹板边界碰撞体
 * 基于Babylon.js官方文档的碰撞检测最佳实践
 * 创建不可见的墙体来限制角色只能在夹板区域移动
 */
function createDeckBoundaries() {
  console.log('[WoodCollectScene3D] 开始创建夹板边界碰撞体...');

  // 夹板可行走区域参数（根据实际夹板模型调整）
  // 基于坐标轴澄清：X轴=屏幕上下方向，Z轴=屏幕左右方向
  const deckWidth = 10;   // 夹板宽度（X轴方向，屏幕上下方向，边界位置合适）
  const deckDepth = 11;   // 夹板深度（Z轴方向，屏幕左右方向，收缩以紧贴边缘）
  const wallHeight = 8;   // 边界墙高度
  const wallThickness = 0.5; // 边界墙厚度（更薄的墙体）

  // 创建四面边界墙，围绕夹板区域
  const boundaries = [
    // 北墙 (Z轴正方向)
    {
      name: "northWall",
      size: { width: deckWidth + wallThickness * 2, height: wallHeight, depth: wallThickness },
      position: { x: 0, y: wallHeight / 2, z: deckDepth / 2 + wallThickness / 2 }
    },
    // 南墙 (Z轴负方向)
    {
      name: "southWall",
      size: { width: deckWidth + wallThickness * 2, height: wallHeight, depth: wallThickness },
      position: { x: 0, y: wallHeight / 2, z: -(deckDepth / 2 + wallThickness / 2) }
    },
    // 东墙 (X轴正方向)
    {
      name: "eastWall",
      size: { width: wallThickness, height: wallHeight, depth: deckDepth },
      position: { x: deckWidth / 2 + wallThickness / 2, y: wallHeight / 2, z: 0 }
    },
    // 西墙 (X轴负方向)
    {
      name: "westWall",
      size: { width: wallThickness, height: wallHeight, depth: deckDepth },
      position: { x: -(deckWidth / 2 + wallThickness / 2), y: wallHeight / 2, z: 0 }
    }
  ];

  // 创建边界墙网格
  boundaries.forEach(boundary => {
    const wall = MeshBuilder.CreateBox(boundary.name, boundary.size, scene);
    wall.position = new Vector3(boundary.position.x, boundary.position.y, boundary.position.z);

    // 设置碰撞属性
    wall.checkCollisions = true;
    wall.isVisible = false; // 不可见的碰撞体
    wall.isPickable = false; // 不可拾取

    // console.log(`[WoodCollectScene3D] 创建边界墙: ${boundary.name}, 位置: (${boundary.position.x}, ${boundary.position.y}, ${boundary.position.z})`);
  });

  console.log(`[WoodCollectScene3D] 夹板边界创建完成 - 可行走区域: ${deckWidth}x${deckDepth}`);
  console.log(`[WoodCollectScene3D] 边界坐标系: X轴(-${deckWidth/2} 到 ${deckWidth/2}), Z轴(-${deckDepth/2} 到 ${deckDepth/2})`);
  console.log('[WoodCollectScene3D] 调整说明: Z轴方向边界收缩至紧贴夹板边缘，消除海水间隙');
}

/**
 * 调试功能：显示/隐藏边界可视化
 * 用于测试和调试边界碰撞效果
 */
export const toggleBoundaryVisualization = (visible: boolean = true) => {
  const boundaryNames = ["northWall", "southWall", "eastWall", "westWall"];

  boundaryNames.forEach(name => {
    const wall = scene.getMeshByName(name);
    if (wall) {
      wall.isVisible = visible;

      // 如果显示边界，应用半透明红色材质
      if (visible && !wall.material) {
        const debugMaterial = new StandardMaterial(`${name}_debug`, scene);
        debugMaterial.diffuseColor = new Color3(1, 0, 0); // 红色
        debugMaterial.alpha = 0.3; // 半透明
        wall.material = debugMaterial;
      }
    }
  });

  console.log(`[WoodCollectScene3D] 边界可视化: ${visible ? '显示' : '隐藏'}`);
};

/**
 * 获取夹板分析结果
 * 用于调试和验证边界设置
 */
export const getDeckAnalysis = () => {
  const analysis = (globalThis as { deckAnalysis?: any }).deckAnalysis;
  if (analysis) {
    console.log('[WoodCollectScene3D] === 夹板分析结果 ===');
    console.log(`夹板尺寸: ${analysis.width.toFixed(2)} x ${analysis.depth.toFixed(2)} x ${analysis.height.toFixed(2)}`);
    console.log(`边界范围: X(${analysis.bounds.minX.toFixed(2)} 到 ${analysis.bounds.maxX.toFixed(2)}), Z(${analysis.bounds.minZ.toFixed(2)} 到 ${analysis.bounds.maxZ.toFixed(2)})`);
    console.log('================================');
    return analysis;
  } else {
    console.log('[WoodCollectScene3D] 夹板分析结果未找到，请确保场景已加载');
    return null;
  }
};

/**
 * 调试功能：检查角色位置和边界范围
 * 用于验证坐标系对齐和边界检测逻辑
 */
export const debugPlayerPosition = () => {
  if (!player) {
    console.log('[WoodCollectScene3D] 角色未加载');
    return;
  }

  const pos = player.position;
  const deckWidth = 25;
  const deckDepth = 35;

  console.log('[WoodCollectScene3D] === 角色位置调试信息 ===');
  console.log(`角色当前位置: (${pos.x.toFixed(2)}, ${pos.y.toFixed(2)}, ${pos.z.toFixed(2)})`);
  console.log(`夹板边界范围: X轴(${-deckWidth/2} 到 ${deckWidth/2}), Z轴(${-deckDepth/2} 到 ${deckDepth/2})`);

  // 检查是否在边界内
  const inBoundsX = pos.x >= -deckWidth/2 && pos.x <= deckWidth/2;
  const inBoundsZ = pos.z >= -deckDepth/2 && pos.z <= deckDepth/2;
  const inBounds = inBoundsX && inBoundsZ;

  console.log(`边界检查: X轴${inBoundsX ? '✓' : '✗'}, Z轴${inBoundsZ ? '✓' : '✗'}, 总体${inBounds ? '在边界内' : '超出边界'}`);

  // 详细碰撞信息（暂时禁用以减少控制台输出）
  // if (player.ellipsoid) {
  //   console.log(`碰撞椭球体: ${player.ellipsoid.toString()}`);
  // }
  // if (player.ellipsoidOffset) {
  //   console.log(`椭球体偏移: ${player.ellipsoidOffset.toString()}`);
  // }
  // console.log(`碰撞检测启用: ${player.checkCollisions ? '是' : '否'}`);
  console.log('================================');
};

/**
 * 创建海水效果，填充夹板外围区域
 * 基于原Cocos Creator项目的水面材质，使用Babylon.js WaterMaterial实现
 * 包含真实的波浪动画、反射和折射效果
 */
async function createOceanWater(): Promise<void> {
  console.log('[WoodCollectScene3D] 开始创建海水效果...');

  try {
    // 创建一个大的海水平面，覆盖整个可视区域
    // 确保海水在相机视野内且不被夹板遮挡
    const waterPlanes: Mesh[] = [];

    // 创建一个大的海水平面，覆盖更大的区域
    const waterPlane = MeshBuilder.CreateGround(`ocean_water`, {
      width: 200,  // 更大的覆盖范围
      height: 200, // 更大的覆盖范围
      subdivisions: 32 // 增加细分以支持波浪效果
    }, scene);

    // 将海水放置在稍微低于地面的位置，但不要太低
    waterPlane.position = new Vector3(0, -0.5, 0); // 降低到-0.5确保在夹板下方
    waterPlanes.push(waterPlane);

    // console.log('[WoodCollectScene3D] 海水平面位置:', waterPlane.position);
    // console.log('[WoodCollectScene3D] 海水平面尺寸: 200x200');

    console.log(`[WoodCollectScene3D] 创建了 ${waterPlanes.length} 个海水区域`);

    // 现在基本显示正常，使用自定义海水材质实现焦散效果
    console.log('[WoodCollectScene3D] 创建CocosWaterMaterial海水材质...');

    try {
      // 创建自定义海水材质，精确还原原Cocos Creator项目效果
      const cocosWaterMaterial = new CocosWaterMaterial("cocosOceanWater", scene);

      // 应用材质到所有海水平面
      waterPlanes.forEach((plane) => {
        plane.material = cocosWaterMaterial;
        plane.isVisible = true; // 确保可见
      });

      console.log('[WoodCollectScene3D] CocosWaterMaterial海水效果创建完成');
      // console.log('[WoodCollectScene3D] 海水参数配置:');
      // console.log(`  - 基础水色: rgb(${Math.round(cocosWaterMaterial.shallowColor.r*255)}, ${Math.round(cocosWaterMaterial.shallowColor.g*255)}, ${Math.round(cocosWaterMaterial.shallowColor.b*255)})`);
      // console.log(`  - 焦散颜色: rgb(${Math.round(cocosWaterMaterial.causticColor.r*255)}, ${Math.round(cocosWaterMaterial.causticColor.g*255)}, ${Math.round(cocosWaterMaterial.causticColor.b*255)})`);
      // console.log(`  - 焦散强度: ${cocosWaterMaterial.causticStrength}`);
      // console.log(`  - 焦散缩放: ${cocosWaterMaterial.causticScale}`);
      // console.log(`  - 焦散速度: ${cocosWaterMaterial.causticSpeed}`);
      // console.log(`  - RGB分离: ${cocosWaterMaterial.causticRGBSplit}`);

      // 存储水面材质引用
      scene.oceanWaterMaterial = cocosWaterMaterial;

    } catch (error) {
      console.error('[WoodCollectScene3D] CocosWaterMaterial创建失败，使用备用材质:', error);

      // 备用：使用改进的StandardMaterial
      const fallbackWaterMaterial = new StandardMaterial("fallbackWaterMaterial", scene);
      fallbackWaterMaterial.diffuseColor = new Color3(27/255, 145/255, 211/255); // 原项目颜色
      fallbackWaterMaterial.specularColor = new Color3(0.5, 0.8, 1.0);
      fallbackWaterMaterial.alpha = 0.85;
      fallbackWaterMaterial.backFaceCulling = false;

      // 添加简单的动画效果
      let time = 0;
      scene.registerBeforeRender(() => {
        time += scene.getEngine().getDeltaTime() / 1000;
        const wave = Math.sin(time * 2) * 0.1 + 0.9;
        fallbackWaterMaterial.emissiveColor = new Color3(0.05 * wave, 0.2 * wave, 0.4 * wave);
      });

      waterPlanes.forEach((plane) => {
        plane.material = fallbackWaterMaterial;
        plane.isVisible = true;
      });

      scene.oceanWaterMaterial = fallbackWaterMaterial;
    }

  } catch (error) {
    console.error('[WoodCollectScene3D] 海水创建失败，回退到简单水面:', error);

    // 回退到简单的水面实现 - 同样围绕夹板创建
    const fallbackWaterPositions = [
      { x: 0, z: 50 }, // 北侧
      { x: 0, z: -50 }, // 南侧
      { x: 50, z: 0 }, // 东侧
      { x: -50, z: 0 }, // 西侧
    ];

    fallbackWaterPositions.forEach((pos, index) => {
      const fallbackWater = MeshBuilder.CreateGround(`fallback_water_${index}`, { width: 60, height: 60 }, scene);
      fallbackWater.position = new Vector3(pos.x, -0.2, pos.z);

      const fallbackMaterial = new StandardMaterial(`fallback_water_material_${index}`, scene);
      fallbackMaterial.diffuseColor = new Color3(27/255, 145/255, 211/255);
      fallbackMaterial.specularColor = new Color3(0.5, 0.8, 1.0);
      fallbackMaterial.alpha = 0.7;
      fallbackWater.material = fallbackMaterial;
    });
  }
}

/**
 * 加载3D角色模型
 */
async function load3DPlayerModel() {
  console.log('[WoodCollectScene3D] 开始加载3D角色模型...');

  try {
    // 创建模型加载器
    playerModelLoader = new PlayerModelLoader(scene);

    // 配置模型参数
    const modelConfig = {
      ...DEFAULT_PLAYER_CONFIG,
      scale: 1.0,
      position: new Vector3(0, 0.5, 0) // 抬高0.5单位，确保完全避开夹板地面
    };

    // 加载模型
    playerModelResult = await playerModelLoader.loadPlayerModel(modelConfig);
    player = playerModelResult.mesh;

    // 设置角色碰撞属性（基于Babylon.js官方文档）
    setupPlayerCollision(player);

    // 设置阴影
    const shadowGenerator = scene.shadowGenerator;
    if (shadowGenerator && player) {
      shadowGenerator.getShadowMap()?.renderList?.push(player);

      // 为所有子网格添加阴影
      player.getChildMeshes().forEach(childMesh => {
        shadowGenerator.getShadowMap()?.renderList?.push(childMesh);
      });
    }

    // 添加玩家到水面反射列表
    if (scene.addToWaterRenderList) {
      scene.addToWaterRenderList();
      // console.log('[WoodCollectScene3D] 玩家模型已添加到水面反射列表');
    }

    // 创建动画控制器
    const animationGroups = new Map();
    Object.entries(playerModelResult.animations).forEach(([name, animGroup]) => {
      if (animGroup) {
        animationGroups.set(name as PlayerAnimationState, animGroup);
      }
    });

    animationController = new PlayerAnimationController(scene, animationGroups);

    // 监听动画状态变化（暂时禁用以减少控制台输出）
    // animationController.onStateChange((from, to) => {
    //   console.log(`[WoodCollectScene3D] 动画状态切换: ${from} -> ${to}`);
    // });

    // 更新游戏状态中的玩家位置
    gameState.playerPosition = player.position.clone();

    console.log('[WoodCollectScene3D] 3D角色模型加载完成');
  } catch (error) {
    console.error('[WoodCollectScene3D] 3D角色模型加载失败:', error);
    throw error;
  }
}

/**
 * 设置角色碰撞属性
 * 基于Babylon.js官方文档的碰撞检测最佳实践
 */
function setupPlayerCollision(playerMesh: AbstractMesh) {
  console.log('[WoodCollectScene3D] 设置角色碰撞属性...');

  // 设置碰撞椭球体（定义角色的碰撞体积）
  // 椭球体尺寸应该略小于角色模型，确保合理的碰撞检测
  playerMesh.ellipsoid = new Vector3(0.5, 1.0, 0.5); // 宽度、高度、深度

  // 设置椭球体偏移（调整碰撞体积相对于角色中心的位置）
  playerMesh.ellipsoidOffset = new Vector3(0, 1.0, 0); // 向上偏移1单位，适配角色高度

  // 启用碰撞检测
  playerMesh.checkCollisions = true;

  console.log('[WoodCollectScene3D] 角色碰撞属性设置完成');
  // console.log(`[WoodCollectScene3D] 碰撞椭球体: ${playerMesh.ellipsoid.toString()}`);
  // console.log(`[WoodCollectScene3D] 椭球体偏移: ${playerMesh.ellipsoidOffset.toString()}`);
}

/**
 * 创建回退玩家角色（胶囊体）
 * 当3D模型加载失败时使用
 */
function createFallbackPlayer() {
  console.log('[WoodCollectScene3D] 创建回退玩家角色...');

  // 创建玩家胶囊体
  player = MeshBuilder.CreateCapsule("player", {
    radius: 0.5,
    height: 2
  }, scene);

  player.position = new Vector3(0, 1.5, 0); // 抬高到1.5，确保角色完全站在地面上

  // 设置角色碰撞属性
  setupPlayerCollision(player);

  // 玩家材质
  const playerMaterial = new StandardMaterial("playerMaterial", scene);
  playerMaterial.diffuseColor = new Color3(0.2, 0.4, 0.8); // 蓝色
  playerMaterial.specularColor = new Color3(0.3, 0.3, 0.3);
  player.material = playerMaterial;

  // 添加到阴影投射列表
  const shadowGenerator = scene.shadowGenerator;
  if (shadowGenerator) {
    shadowGenerator.getShadowMap()?.renderList?.push(player);
  }

  // 更新游戏状态中的玩家位置
  gameState.playerPosition = player.position.clone();

  console.log('[WoodCollectScene3D] 回退玩家角色创建完成');
}

/**
 * 加载金币3D模型
 */
async function loadCoinModel() {
  console.log('[WoodCollectScene3D] 开始加载金币模型...');

  try {
    const result = await SceneLoader.ImportMeshAsync(
      "", // 加载所有网格
      "", // 根路径为空，直接使用完整路径
      "/models/coin/coin.glb", // 完整文件路径
      scene
    );

    if (result.meshes.length > 0) {
      coinModelTemplate = result.meshes[0];

      // 设置金币模型属性
      coinModelTemplate.scaling = new Vector3(0.3, 0.3, 0.3); // 缩小金币
      coinModelTemplate.setEnabled(false); // 模板不显示

      // 应用金币材质和贴图
      await applyCoinMaterial();

      // 添加到阴影投射列表
      const shadowGenerator = scene.shadowGenerator;
      if (shadowGenerator) {
        shadowGenerator.getShadowMap()?.renderList?.push(coinModelTemplate);
      }

      console.log('[WoodCollectScene3D] 金币模型加载完成');
    } else {
      throw new Error('金币模型文件中没有找到网格');
    }
  } catch (error) {
    console.error('[WoodCollectScene3D] 金币模型加载失败:', error);
    console.error('[WoodCollectScene3D] 尝试加载的路径:', "/models/coin/coin.glb");
    // 创建回退金币模型（简单的圆柱体）
    createFallbackCoinModel();
  }
}

/**
 * 为金币模型应用PBR材质和贴图
 * 使用原Cocos Creator项目的金币贴图文件
 */
async function applyCoinMaterial() {
  if (!coinModelTemplate) return;

  console.log('[WoodCollectScene3D] 开始应用金币材质和贴图...');

  try {
    // 获取金币模型的所有网格
    const allMeshes = [coinModelTemplate, ...coinModelTemplate.getChildMeshes()];

    // 金币贴图路径
    const texturesPath = '/models/coin/textures/';

    // 为每个网格应用金币材质
    for (const mesh of allMeshes) {
      if (!mesh.material) continue;

      // 创建PBR材质
      const coinMaterial = new PBRMaterial(`coinMaterial_${mesh.name}`, scene);

      // 设置金币的基础PBR属性 - 匹配原项目的亮金黄色
      coinMaterial.albedoColor = new Color3(1.0, 0.95, 0.4); // 更接近原项目的亮金黄色
      coinMaterial.metallic = 0.05; // 适中的金属度
      coinMaterial.roughness = 0.5; // 非常低的粗糙度，增强光泽
      coinMaterial.emissiveColor = new Color3(0.15, 0.12, 0.03); // 增强自发光，提升亮度

      // 应用Albedo贴图（主要颜色贴图）
      try {
        const albedoTexture = new Texture(`${texturesPath}coin_alb.png`, scene);
        albedoTexture.isBlocking = false;
        coinMaterial.albedoTexture = albedoTexture;
        // console.log('[WoodCollectScene3D] ✓ 应用金币Albedo贴图');
      } catch (error) {
        console.warn('[WoodCollectScene3D] 金币Albedo贴图加载失败:', error);
      }

      // 应用金属度/粗糙度贴图
      try {
        const metallicTexture = new Texture(`${texturesPath}coin_mtl.png`, scene);
        metallicTexture.isBlocking = false;
        coinMaterial.metallicTexture = metallicTexture;
        // console.log('[WoodCollectScene3D] ✓ 应用金币金属度贴图');
      } catch (error) {
        console.warn('[WoodCollectScene3D] 金币金属度贴图加载失败:', error);
      }

      // 应用AO/Roughness/Metallic贴图（如果存在）
      try {
        const armTexture = new Texture(`${texturesPath}coin_arm.png`, scene);
        armTexture.isBlocking = false;
        // ARM贴图通常包含AO(R)、Roughness(G)、Metallic(B)
        coinMaterial.metallicTexture = armTexture;
        // console.log('[WoodCollectScene3D] ✓ 应用金币ARM贴图');
      } catch (error) {
        console.warn('[WoodCollectScene3D] 金币ARM贴图加载失败:', error);
      }

      // 应用材质到网格
      mesh.material = coinMaterial;
    }

    console.log('[WoodCollectScene3D] ✓ 金币材质和贴图应用完成');
  } catch (error) {
    console.error('[WoodCollectScene3D] 金币材质应用失败:', error);
  }
}

/**
 * 创建回退金币模型（当GLB加载失败时使用）
 */
function createFallbackCoinModel() {
  console.log('[WoodCollectScene3D] 创建回退金币模型...');

  coinModelTemplate = MeshBuilder.CreateCylinder("coinTemplate", {
    height: 0.1,
    diameter: 0.6
  }, scene);

  // 金币材质 - 与主金币材质保持一致
  const coinMaterial = new PBRMaterial("coinMaterial", scene);
  coinMaterial.albedoColor = new Color3(1.0, 0.95, 0.4); // 更接近原项目的亮金黄色
  coinMaterial.metallic = 0.2; // 适中的金属度
  coinMaterial.roughness = 0.1; // 非常低的粗糙度，增强光泽
  coinMaterial.emissiveColor = new Color3(0.15, 0.12, 0.03); // 增强自发光，提升亮度
  coinModelTemplate.material = coinMaterial;

  coinModelTemplate.setEnabled(false); // 模板不显示

  console.log('[WoodCollectScene3D] 回退金币模型创建完成');
}

/**
 * 初始化工厂解锁系统
 */
async function initializeFactoryUnlockSystem() {
  console.log('[WoodCollectScene3D] 初始化工厂解锁系统...');

  try {
    // 配置工厂解锁管理器
    // 甲板边界：X轴(-5 到 5)，Z轴(-5.5 到 5.5)
    // 将触发器放在甲板左侧安全区域内
    const factoryConfig: FactoryUnlockManagerConfig = {
      triggerPosition: new Vector3(-2, 0, 0), // 甲板左侧安全位置
      factoryPosition: new Vector3(-4.5, 0, 0), // 工厂生产位置（更靠近海边，避免站在木材上）
      requiredCoins: 100, // 需要100金币解锁
      productionRate: 3, // 每秒生产3个木材
      maxWoodCount: 999999, // 移除数量限制，允许无限堆叠
      onUnlockStart: () => {
        console.log('[WoodCollectScene3D] 工厂解锁开始');
      },
      onUnlockComplete: () => {
        console.log('[WoodCollectScene3D] 工厂解锁完成！');
      },
      onStackUpdate: () => {
        // 金币动画完成后重新创建金币堆叠
        console.log('[WoodCollectScene3D] 更新金币堆叠显示');
        if (coinModelTemplate) {
          createCoinStack();
        }
      }
    };

    // 创建工厂解锁管理器
    factoryUnlockManager = new FactoryUnlockManager(scene, factoryConfig);

    // 设置金币模板用于动画
    if (coinModelTemplate) {
      factoryUnlockManager.setCoinTemplate(coinModelTemplate);
    }

    // 设置金币堆叠引用
    factoryUnlockManager.setCoinStackMeshes(coinStackMeshes);

    console.log('[WoodCollectScene3D] 工厂解锁系统初始化完成');
  } catch (error) {
    console.error('[WoodCollectScene3D] 工厂解锁系统初始化失败:', error);
  }
}

/**
 * 创建金币堆叠效果
 * 根据当前金币数量创建堆叠的金币模型
 */
function createCoinStack() {
  console.log('[WoodCollectScene3D] 开始创建金币堆叠效果...');

  if (!coinModelTemplate) {
    console.warn('[WoodCollectScene3D] 金币模型模板未加载，跳过创建堆叠效果');
    return;
  }

  // 清理现有的金币堆叠
  clearCoinStack();

  // 计算需要显示的金币数量（每5个金币单位显示1个物理模型）
  const physicalCoinsCount = Math.floor(gameState.coins / 5);

  console.log(`[WoodCollectScene3D] 创建 ${physicalCoinsCount} 个物理金币模型 (总金币: ${gameState.coins})`);
  // console.log(`[WoodCollectScene3D] 金币模型模板状态:`, coinModelTemplate ? '已加载' : '未加载');

  // 堆叠参数
  const stackSpacing = 0.15; // 金币之间的垂直间距
  const baseHeight = 0.05; // 基础高度

  for (let i = 0; i < physicalCoinsCount; i++) {
    // 克隆金币模型
    const coinMesh = coinModelTemplate.clone(`coin_${i}`, null);
    if (!coinMesh) continue;

    coinMesh.setEnabled(true);

    // 设置金币位置（堆叠在玩家背后，与移动时距离保持一致）
    const stackHeight = baseHeight + (i * stackSpacing);

    // 使用与移动跟随相同的计算方式
    const playerPosition = player?.position || Vector3.Zero();
    const playerRotation = player?.rotationQuaternion || Quaternion.Identity();
    const backwardDirection = new Vector3(0, 0, -0.5); // 与移动时相同的距离，避免重叠
    const rotatedDirection = new Vector3();
    backwardDirection.rotateByQuaternionToRef(playerRotation, rotatedDirection);

    const targetPosition = playerPosition.add(rotatedDirection);
    targetPosition.y = stackHeight;
    coinMesh.position = targetPosition;

    // 设置金币旋转：平躺堆叠（像硬币一样）
    // 根据截图效果，使用X轴90度旋转让金币平躺
    coinMesh.rotation = new Vector3(Math.PI / 2, 0, 0); // X轴90度，让金币平躺

    // 添加轻微的随机旋转，让堆叠看起来更自然
    const randomRotation = (Math.random() - 0.5) * 0.1;
    coinMesh.rotation.y += randomRotation;

    // console.log(`[WoodCollectScene3D] 金币 ${i} 统一旋转设置: x=${coinMesh.rotation.x.toFixed(2)}, y=${coinMesh.rotation.y.toFixed(2)}, z=${coinMesh.rotation.z.toFixed(2)}`);

    // 添加到阴影投射列表
    const shadowGenerator = scene.shadowGenerator;
    if (shadowGenerator) {
      shadowGenerator.getShadowMap()?.renderList?.push(coinMesh);
    }

    coinStackMeshes.push(coinMesh);
  }

  // 添加金币到水面反射列表
  if (scene.addToWaterRenderList) {
    scene.addToWaterRenderList();
    // console.log('[WoodCollectScene3D] 金币堆叠已添加到水面反射列表');
  }

  console.log('[WoodCollectScene3D] 金币堆叠效果创建完成');
}

/**
 * 清理现有的金币堆叠
 */
function clearCoinStack() {
  coinStackMeshes.forEach(coinMesh => {
    coinMesh.dispose();
  });
  coinStackMeshes = [];
}

/**
 * 设置相机跟随玩家
 */
function setupCameraFollow() {
  // 相机跟随玩家，保持固定偏移
  scene.registerBeforeRender(() => {
    if (player && camera) {
      // 更新相机目标位置为玩家位置
      camera.setTarget(player.position);

      // 更新游戏状态中的玩家位置
      gameState.playerPosition = player.position.clone();
    }
  });

  // console.log('[WoodCollectScene3D] 相机跟随设置完成');
}

/**
 * 渲染循环回调
 */
export const onRender = () => {
  // 更新玩家移动
  updatePlayerPosition();

  // 更新动画状态
  updatePlayerAnimation();

  // 更新金币堆叠位置
  updateCoinStackPosition();

  // 更新木材堆叠位置
  updateWoodStackPosition();

  // 更新木材收集系统
  updateWoodCollectionSystem();

  // 更新工厂解锁系统
  updateFactoryUnlockSystem();

  // 这里可以添加其他每帧更新的逻辑
  // 例如：AI更新、物理模拟等
};

/**
 * 更新玩家位置
 */
function updatePlayerPosition() {
  if (!player) {
    playerMovement.velocity = Vector3.Zero();
    return;
  }

  const deltaTime = scene.getEngine().getDeltaTime() / 1000; // 转换为秒

  if (!playerMovement.isMoving) {
    playerMovement.velocity = Vector3.Zero();
    // 即使不移动也要更新旋转（平滑过渡到目标角度）
    updatePlayerRotation(deltaTime);
    return;
  }

  const moveDistance = playerMovement.speed * deltaTime;

  // 计算移动向量（考虑相机角度）
  const moveVector = new Vector3(
    playerMovement.direction.x * moveDistance,
    0,
    playerMovement.direction.y * moveDistance
  );

  // 计算角色朝向（基于原Cocos Creator项目的逻辑）
  if (Math.abs(playerMovement.direction.x) > 0.1 || Math.abs(playerMovement.direction.y) > 0.1) {
    // 计算目标旋转角度（Y轴旋转）
    // 注意：Babylon.js的坐标系与Cocos Creator不同，需要调整角度计算
    const angleY = Math.atan2(playerMovement.direction.x, playerMovement.direction.y);
    playerMovement.targetQuaternion = Quaternion.FromEulerAngles(0, angleY, 0);
  }

  // 使用moveWithCollisions进行碰撞检测移动（基于Babylon.js官方文档）
  player.moveWithCollisions(moveVector);

  // 更新角色旋转
  updatePlayerRotation(deltaTime);

  // 更新速度向量（用于动画控制）
  playerMovement.velocity = moveVector.scale(1 / deltaTime);

  // 更新游戏状态中的玩家位置
  gameState.playerPosition = player.position.clone();

  // 确保玩家在地面上，考虑夹板地面的高度
  // 移除了原有的数学边界限制，现在由碰撞系统处理边界
  player.position.y = Math.max(0.5, player.position.y);
}

/**
 * 更新玩家旋转（平滑转身）
 * 使用Quaternion.Slerp实现平滑的角色转身效果
 */
function updatePlayerRotation(deltaTime: number) {
  if (!player) return;

  // 确保player有rotationQuaternion属性
  if (!player.rotationQuaternion) {
    player.rotationQuaternion = Quaternion.Identity();
    playerMovement.currentQuaternion = Quaternion.Identity();
  }

  // 计算插值因子（转身速度）
  const rotationSpeed = 8.0; // 转身速度，可以调整
  const lerpFactor = Math.min(1.0, rotationSpeed * deltaTime);

  // 使用球面线性插值进行平滑旋转
  playerMovement.currentQuaternion = Quaternion.Slerp(
    playerMovement.currentQuaternion,
    playerMovement.targetQuaternion,
    lerpFactor
  );

  // 应用旋转到角色
  player.rotationQuaternion = playerMovement.currentQuaternion.clone();

  // 调试输出（可选）
  // console.log(`[WoodCollectScene3D] 角色旋转更新: lerpFactor=${lerpFactor.toFixed(3)}`);
}

/**
 * 更新玩家动画状态
 */
function updatePlayerAnimation() {
  if (!animationController) return;

  // 根据移动速度自动切换动画
  animationController.updateMovementAnimation(playerMovement.velocity);
}

/**
 * 更新金币堆叠位置
 * 让金币堆叠跟随玩家移动
 */
function updateCoinStackPosition() {
  if (!player || coinStackMeshes.length === 0) return;

  // 计算玩家背后的位置
  const playerPosition = player.position;
  const playerRotation = player.rotationQuaternion || Quaternion.Identity();

  // 计算玩家背后的方向向量
  const backwardDirection = new Vector3(0, 0, -0.5); // 背后0.5单位，避免重叠
  const rotatedDirection = new Vector3();
  backwardDirection.rotateByQuaternionToRef(playerRotation, rotatedDirection);

  // 更新每个金币的位置
  coinStackMeshes.forEach((coinMesh, index) => {
    const stackHeight = 0.05 + (index * 0.15); // 基础高度 + 堆叠高度
    const targetPosition = playerPosition.add(rotatedDirection);
    targetPosition.y = stackHeight;

    // 平滑移动到目标位置
    coinMesh.position = Vector3.Lerp(coinMesh.position, targetPosition, 0.1);
  });
}

/**
 * 更新工厂解锁系统
 */
function updateFactoryUnlockSystem() {
  if (!factoryUnlockManager) return;

  // 检查是否需要消耗金币进行解锁
  const status = factoryUnlockManager.getStatus();
  if (status.isUnlocking && !status.isUnlocked && gameState.coins >= status.requiredCoins) {
    // 消耗金币
    gameState.coins -= status.requiredCoins;
    console.log(`[WoodCollectScene3D] 消耗 ${status.requiredCoins} 金币解锁工厂，剩余金币: ${gameState.coins}`);

    // 注意：不要立即重建金币堆叠，让动画系统处理金币的逐个移除
    // 动画完成后会自动更新堆叠显示
  }

  // 更新工厂解锁系统状态
  factoryUnlockManager.update({
    coins: gameState.coins,
    wood: gameState.wood,
    playerPosition: gameState.playerPosition
  });

  // 更新金币堆叠引用（确保动画系统有最新的引用）
  if (factoryUnlockManager && coinStackMeshes.length > 0) {
    factoryUnlockManager.setCoinStackMeshes(coinStackMeshes);
  }
}

/**
 * 触发拾取动画
 */
export const triggerPickupAnimation = () => {
  if (animationController) {
    animationController.triggerPickup();
    // console.log('[WoodCollectScene3D] 触发拾取动画');
  }
};

/**
 * 重置游戏状态
 */
export const resetGame = () => {
  gameState = {
    coins: 100, // 重置时也给100金币
    wood: 0,
    customers: 0,
    workers: 0,
    gameStarted: false,
    currentLevel: 1,
    playerPosition: Vector3.Zero()
  };

  // 重置玩家位置
  if (player) {
    player.position = new Vector3(0, 1.5, 0); // 抬高到1.5，确保角色完全站在地面上
  }

  // 重置动画状态
  if (animationController) {
    animationController.changeState(PlayerAnimationState.IDLE, true);
  }

  // 重新创建金币堆叠（如果模型已加载）
  if (coinModelTemplate) {
    createCoinStack();
  }

  console.log('[WoodCollectScene3D] 游戏状态已重置');
};

/**
 * 获取当前游戏状态
 */
export const getGameState = () => {
  return { ...gameState };
};

/**
 * 更新游戏状态
 */
export const updateGameState = (newState: Partial<typeof gameState>) => {
  const oldCoins = gameState.coins;
  const oldWood = gameState.wood;
  gameState = { ...gameState, ...newState };

  // 如果金币数量发生变化，更新金币堆叠（如果模型已加载）
  if (newState.coins !== undefined && newState.coins !== oldCoins && coinModelTemplate) {
    createCoinStack();
  }

  // 如果木材数量发生变化，更新木材堆叠（如果模型已加载）
  if (newState.wood !== undefined && newState.wood !== oldWood && woodModelTemplate) {
    createWoodStack();
  }

  // console.log('[WoodCollectScene3D] 游戏状态已更新:', gameState);
};

/**
 * 更新玩家移动方向
 * 由虚拟摇杆调用，正确映射摇杆方向到世界坐标
 */
export const updatePlayerMovement = (direction: { x: number; y: number }) => {
  // 相机角度分析：
  // 相机alpha = Math.PI/4 (45度方位角)，从右上方俯视
  // 相机beta = Math.PI*43/180 (43度极角)，俯视角度
  
  // 屏幕方向到世界坐标的映射（基于45度俯视角）
  // 从玩家视角看：
  // - 摇杆向右 -> 角色在屏幕上向右移动 -> 世界坐标纯X轴正方向
  // - 摇杆向上 -> 角色在屏幕上向上移动 -> 世界坐标纯Z轴负方向（远离相机）
  
  // 45度相机视角的坐标转换矩阵
  // 由于相机在45度角位置，需要进行旋转变换
  const cameraAngle = Math.PI / 4; // 修正：使用正45度角
  const cos45 = Math.cos(cameraAngle); // ≈ 0.707
  const sin45 = Math.sin(cameraAngle); // ≈ 0.707
  
  // 应用旋转矩阵，将屏幕坐标转换为世界坐标（完全反转）
  const correctedDirection = {
    // 世界X坐标 = -(摇杆X*cos45 + 摇杆Y*sin45)
    x: -(direction.x * cos45 + direction.y * sin45),
    // 世界Z坐标 = -(摇杆Y*cos45 - 摇杆X*sin45)
    y: -(direction.y * cos45 - direction.x * sin45)
  };
  
  // 归一化方向向量以保持一致的移动速度
  const length = Math.sqrt(correctedDirection.x * correctedDirection.x + correctedDirection.y * correctedDirection.y);
  if (length > 0) {
    correctedDirection.x /= length;
    correctedDirection.y /= length;
  }
  
  playerMovement.direction.x = correctedDirection.x;
  playerMovement.direction.y = correctedDirection.y;  // 这里的y对应世界坐标的z轴
  
  // 判断是否在移动
  const inputLength = Math.sqrt(direction.x * direction.x + direction.y * direction.y);
  playerMovement.isMoving = inputLength > 0.2;  // 移动阈值，基于原项目配置
  
  // 调试输出（暂时禁用以减少控制台输出）
  // if (playerMovement.isMoving) {
  //   console.log(`[WoodCollectScene3D] 摇杆输入: (${direction.x.toFixed(2)}, ${direction.y.toFixed(2)}), 世界方向: (${correctedDirection.x.toFixed(2)}, ${correctedDirection.y.toFixed(2)}), 长度: ${length.toFixed(2)}`);
  // }
};

/**
 * 获取当前动画状态
 */
export const getCurrentAnimationState = (): string => {
  if (animationController) {
    return animationController.getCurrentState();
  }
  return 'idle';
};

/**
 * 测试金币堆叠功能
 * 添加或减少金币数量来测试堆叠效果
 */
export const testCoinStack = (coinChange: number) => {
  const newCoins = Math.max(0, gameState.coins + coinChange);
  updateGameState({ coins: newCoins });
  // console.log(`[WoodCollectScene3D] 测试金币堆叠: ${gameState.coins} -> ${newCoins}`);
};

/**
 * 创建木材模板（使用程序生成的几何体）
 */
function createWoodTemplate() {
  console.log('[WoodCollectScene3D] 创建木材模板...');

  woodModelTemplate = MeshBuilder.CreateBox("woodTemplate", {
    width: 0.25,  // 宽度（X轴）
    height: 0.12, // 高度（Y轴）
    depth: 0.6    // 深度（Z轴）- 长度增加为原来的2倍
  }, scene);

  // 木材材质 - 使用与工厂相同的StandardMaterial和颜色
  const woodMaterial = new StandardMaterial("woodMaterial", scene);
  woodMaterial.diffuseColor = new Color3(0.7, 0.5, 0.2); // 与工厂木材相同的颜色
  woodMaterial.specularColor = new Color3(0.1, 0.1, 0.1); // 与工厂木材相同的镜面反射
  woodModelTemplate.material = woodMaterial;

  // 设置木材横向摆放（参考截图，木材应该横着摆放）
  woodModelTemplate.rotation.y = Math.PI / 2; // 绕Y轴旋转90度

  woodModelTemplate.setEnabled(false); // 模板不显示

  console.log('[WoodCollectScene3D] 木材模板创建完成');
}

/**
 * 创建木材堆叠效果
 * 根据当前木材数量创建堆叠的木材模型
 */
function createWoodStack() {
  console.log('[WoodCollectScene3D] 开始创建木材堆叠效果...');

  if (!woodModelTemplate) {
    console.warn('[WoodCollectScene3D] 木材模型模板未创建，跳过创建堆叠效果');
    return;
  }

  // 清理现有的木材堆叠
  clearWoodStack();

  // 根据当前木材数量创建物理模型
  const physicalWoodsCount = gameState.wood;

  console.log(`[WoodCollectScene3D] 创建 ${physicalWoodsCount} 个物理木材模型`);

  // 堆叠参数
  const stackSpacing = 0.15; // 木材之间的垂直间距
  const baseHeight = 0.1; // 基础高度

  for (let i = 0; i < physicalWoodsCount; i++) {
    // 克隆木材模型
    const woodMesh = woodModelTemplate.clone(`wood_${i}`, null);
    if (!woodMesh) continue;

    woodMesh.setEnabled(true);

    // 设置木材位置（堆叠在玩家背后，与金币堆叠类似但位置稍有不同）
    const stackHeight = baseHeight + (i * stackSpacing);

    // 使用与移动跟随相同的计算方式，但位置稍有偏移
    const playerPosition = player?.position || Vector3.Zero();
    const playerRotation = player?.rotationQuaternion || Quaternion.Identity();
    const backwardDirection = new Vector3(-0.3, 0, -0.5); // 稍微偏左一点，避免与金币重叠
    const rotatedDirection = new Vector3();
    backwardDirection.rotateByQuaternionToRef(playerRotation, rotatedDirection);

    const targetPosition = playerPosition.add(rotatedDirection);
    targetPosition.y = stackHeight;
    woodMesh.position = targetPosition;

    // 设置木材旋转：横向摆放
    woodMesh.rotation = new Vector3(0, 0,0); // Y轴90度，让木材横向摆放

    // 添加轻微的随机旋转，让堆叠看起来更自然
    const randomRotation = (Math.random() - 0.5) * 0.1;
    woodMesh.rotation.y += randomRotation;

    // 添加到阴影投射列表
    const shadowGenerator = scene.shadowGenerator;
    if (shadowGenerator) {
      shadowGenerator.getShadowMap()?.renderList?.push(woodMesh);
    }

    woodStackMeshes.push(woodMesh);
  }

  // 添加木材到水面反射列表
  if (scene.addToWaterRenderList) {
    scene.addToWaterRenderList();
  }

  console.log('[WoodCollectScene3D] 木材堆叠效果创建完成');
}

/**
 * 清理现有的木材堆叠
 */
function clearWoodStack() {
  woodStackMeshes.forEach(woodMesh => {
    woodMesh.dispose();
  });
  woodStackMeshes = [];
}

/**
 * 初始化木材收集系统
 */
function initializeWoodCollectionSystem() {
  console.log('[WoodCollectScene3D] 初始化木材收集系统...');

  woodCollectionSystem = new WoodCollectionSystem(scene);

  // 设置木材模板
  if (woodModelTemplate) {
    woodCollectionSystem.setWoodTemplate(woodModelTemplate);
  }

  // 设置木材堆叠引用
  woodCollectionSystem.setWoodStackMeshes(woodStackMeshes);

  console.log('[WoodCollectScene3D] 木材收集系统初始化完成');
}

/**
 * 更新木材堆叠位置
 * 让木材堆叠跟随玩家移动
 */
function updateWoodStackPosition() {
  if (!player || woodStackMeshes.length === 0) return;

  // 计算玩家背后的位置（稍微偏左，避免与金币重叠）
  const playerPosition = player.position;
  const playerRotation = player.rotationQuaternion || Quaternion.Identity();

  // 计算玩家背后的方向向量（稍微偏左）
  const backwardDirection = new Vector3(-0.3, 0, -0.5); // 偏左0.3单位，背后0.5单位
  const rotatedDirection = new Vector3();
  backwardDirection.rotateByQuaternionToRef(playerRotation, rotatedDirection);

  // 更新每个木材的位置
  woodStackMeshes.forEach((woodMesh, index) => {
    const stackHeight = 0.1 + (index * 0.15); // 基础高度 + 堆叠高度
    const targetPosition = playerPosition.add(rotatedDirection);
    targetPosition.y = stackHeight;

    // 平滑移动到目标位置
    woodMesh.position = Vector3.Lerp(woodMesh.position, targetPosition, 0.1);
  });
}

/**
 * 更新木材收集系统
 * 检测玩家与工厂木材的距离，自动收集木材
 */
function updateWoodCollectionSystem() {
  if (!woodCollectionSystem || !factoryUnlockManager || !player) return;

  // 检查工厂是否已解锁且正在生产
  const factoryStatus = factoryUnlockManager.getStatus();
  if (!factoryStatus.isUnlocked || !factoryStatus.isProducing) return;

  // 获取工厂实例
  const woodFactory = factoryUnlockManager.getWoodFactory();
  if (!woodFactory) return;

  // 检查工厂是否有木材可收集
  const availableWoodCount = woodFactory.getWoodCount();
  if (availableWoodCount === 0) return;

  // 计算玩家与工厂的距离
  const factoryPosition = factoryUnlockManager.getFactoryPosition();
  const playerPosition = player.position;
  const distance = Vector3.Distance(playerPosition, factoryPosition);

  // 收集距离阈值（玩家需要靠近工厂才能收集）
  const collectionDistance = 2.0;

  if (distance <= collectionDistance) {
    // 玩家在收集范围内，开始收集木材
    startWoodCollection(woodFactory);
  }
}

// 木材收集状态
let isCollectingWood = false;
let lastWoodCollectionTime = 0;

/**
 * 开始木材收集
 */
function startWoodCollection(woodFactory: import('./wood-collect/WoodFactory').WoodFactory) {
  const currentTime = Date.now();

  // 每100毫秒收集1个木材（符合需求）
  if (!isCollectingWood || currentTime - lastWoodCollectionTime >= 100) {
    isCollectingWood = true;
    lastWoodCollectionTime = currentTime;

    // 从工厂收集1个木材
    const collectedWoods = woodFactory.collectWood(1);

    if (collectedWoods.length > 0) {
      // 播放木材收集动画
      playWoodCollectionAnimation(collectedWoods[0]);

      // 更新游戏状态
      gameState.wood += 1;

      // 触发拾取动画
      if (animationController) {
        animationController.triggerPickup();
      }

      console.log(`[WoodCollectScene3D] 收集了1个木材，当前木材数量: ${gameState.wood}`);
    }
  }
}

/**
 * 播放木材收集动画
 */
function playWoodCollectionAnimation(collectedWood: AbstractMesh) {
  if (!woodCollectionSystem || !player) return;

  // 计算动画起始和结束位置
  const startPosition = collectedWood.position.clone();
  const endPosition = player.position.clone();
  endPosition.y += 1.0; // 稍微抬高终点位置

  // 配置木材收集动画
  const collectionConfig: WoodCollectionConfig = {
    startPosition: startPosition,
    endPosition: endPosition,
    woodCount: 1,
    duration: 0.25, // 0.25秒飞行时间（符合需求）
    onComplete: () => {
      // 动画完成后更新木材堆叠显示
      createWoodStack();
    },
    onWoodReachTarget: (woodIndex) => {
      console.log(`[WoodCollectScene3D] 木材 ${woodIndex} 已到达玩家位置`);
    },
    onStackUpdate: () => {
      // 更新木材堆叠引用
      if (woodCollectionSystem) {
        woodCollectionSystem.setWoodStackMeshes(woodStackMeshes);
      }
    }
  };

  // 播放收集动画
  woodCollectionSystem.playWoodCollectionAnimation(collectionConfig);
}

/**
 * 测试木材堆叠功能
 * 添加或减少木材数量来测试堆叠效果
 */
export const testWoodStack = (woodChange: number) => {
  const newWood = Math.max(0, gameState.wood + woodChange);
  updateGameState({ wood: newWood });
  console.log(`[WoodCollectScene3D] 测试木材堆叠: ${gameState.wood} -> ${newWood}`);
};

/**
 * 销毁场景资源
 */
export const disposeScene = () => {
  console.log('[WoodCollectScene3D] 开始销毁场景资源...');

  // 移除屏幕旋转监听器
  window.removeEventListener('resize', handleScreenResize);

  // 销毁动画控制器
  if (animationController) {
    animationController.dispose();
    animationController = null;
  }

  // 销毁模型加载器
  if (playerModelLoader) {
    playerModelLoader.dispose();
    playerModelLoader = null;
  }

  // 销毁模型结果
  if (playerModelResult) {
    playerModelResult.dispose();
    playerModelResult = null;
  }

  // 清理金币堆叠
  clearCoinStack();

  // 销毁金币模板
  if (coinModelTemplate) {
    coinModelTemplate.dispose();
    coinModelTemplate = null;
  }

  // 销毁工厂解锁系统
  if (factoryUnlockManager) {
    factoryUnlockManager.dispose();
    factoryUnlockManager = null;
  }

  // 清理引用
  player = null;
  scene = null as unknown as Scene;
  camera = null as unknown as ArcRotateCamera;
  ground = null as unknown as Mesh;

  console.log('[WoodCollectScene3D] 场景资源销毁完成');
};
