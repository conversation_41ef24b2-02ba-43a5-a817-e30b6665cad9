/**
 * 夹板场景加载器组件
 * 基于Context7查询的最新Babylon.js文档实现
 * 负责加载夹板场景GLB模型并应用PBR材质
 */

import { Scene, Vector3, AbstractMesh, PBRMaterial, Texture, Color3 } from "@babylonjs/core";
import "@babylonjs/loaders/glTF";

// 夹板场景配置接口
interface JiabanSceneConfig {
  modelPath: string;
  texturesPath: string;
  scale?: Vector3;
  position?: Vector3;
  rotation?: Vector3;
}

// 材质配置接口（基于原Cocos Creator项目分析）
interface MaterialConfig {
  name: string;
  mainColor: Color3;
  occlusion: number;
  roughness: number;
  metallic: number;
  specularIntensity: number;
  normalStrength: number;
  textures: {
    albedo: string;
    normal: string;
    pbrMap: string;
  };
}

/**
 * 夹板场景加载器类
 */
export class JiabanSceneLoader {
  private scene: Scene;
  private config: JiabanSceneConfig;
  private loadedMeshes: AbstractMesh[] = [];
  private materials: Map<string, PBRMaterial> = new Map();

  // 材质配置（基于原项目A.mtl和B.mtl分析）
  private readonly materialConfigs: MaterialConfig[] = [
    {
      name: "jiaban_material_A",
      mainColor: new Color3(180/255, 175/255, 184/255), // RGB(180,175,184)
      occlusion: 0.555,
      roughness: 0.461,
      metallic: 0.06,
      specularIntensity: 0.494,
      normalStrength: 0.565,
      textures: {
        albedo: "jiaabn_A_Albedo.jpg",
        normal: "jiaabn_A_Normal.jpg",
        pbrMap: "jiaabn_A_PBRMap.jpg"
      }
    },
    {
      name: "jiaban_material_B", 
      mainColor: new Color3(180/255, 175/255, 184/255), // RGB(180,175,184)
      occlusion: 0.557,
      roughness: 0.244,
      metallic: 0.16,
      specularIntensity: 0.626,
      normalStrength: 0.565,
      textures: {
        albedo: "jiaabn_B_Albedo.jpg",
        normal: "jiaabn_B_Normal.jpg",
        pbrMap: "jiaabn_B_PBRMap.jpg"
      }
    }
  ];

  constructor(scene: Scene, config: JiabanSceneConfig) {
    this.scene = scene;
    this.config = {
      scale: new Vector3(1, 1, 1),
      position: new Vector3(0, 0, 0),
      rotation: new Vector3(0, 0, 0),
      ...config
    };
  }

  /**
   * 异步加载夹板场景
   */
  async loadScene(): Promise<AbstractMesh[]> {
    try {
      console.log('[JiabanSceneLoader] 开始加载夹板场景...');
      
      // 1. 创建PBR材质
      await this.createPBRMaterials();
      
      // 2. 加载GLB模型
      const meshes = await this.loadGLBModel();
      
      // 3. 应用材质到网格
      this.applyMaterialsToMeshes(meshes);
      
      // 4. 设置变换
      this.applyTransforms(meshes);
      
      this.loadedMeshes = meshes;
      console.log('[JiabanSceneLoader] 夹板场景加载完成');
      
      return meshes;
      
    } catch (error) {
      console.error('[JiabanSceneLoader] 夹板场景加载失败:', error);
      throw error;
    }
  }

  /**
   * 创建PBR材质
   */
  private async createPBRMaterials(): Promise<void> {
    console.log('[JiabanSceneLoader] 创建PBR材质...');
    
    for (const materialConfig of this.materialConfigs) {
      const material = new PBRMaterial(materialConfig.name, this.scene);
      
      // 设置基础颜色（确保颜色值正确应用）
      material.baseColor = materialConfig.mainColor;
      console.log(`[JiabanSceneLoader] 设置基础颜色: ${materialConfig.name} - R:${materialConfig.mainColor.r.toFixed(3)}, G:${materialConfig.mainColor.g.toFixed(3)}, B:${materialConfig.mainColor.b.toFixed(3)}`);
      
      // 设置PBR属性
      material.roughness = materialConfig.roughness;
      material.metallic = materialConfig.metallic;
      
      // 加载并应用贴图
      await this.loadTexturesForMaterial(material, materialConfig);
      
      this.materials.set(materialConfig.name, material);
      console.log(`[JiabanSceneLoader] 创建材质: ${materialConfig.name}`);
    }
  }

  /**
   * 为材质加载贴图
   */
  private async loadTexturesForMaterial(material: PBRMaterial, config: MaterialConfig): Promise<void> {
    const texturesPath = this.config.texturesPath;

    console.log(`[JiabanSceneLoader] 开始加载材质贴图: ${config.name}`);
    console.log(`[JiabanSceneLoader] 贴图路径: ${texturesPath}`);

    try {
      // 加载Albedo贴图（漫反射）
      const albedoTexture = new Texture(`${texturesPath}/${config.textures.albedo}`, this.scene);
      material.baseTexture = albedoTexture;
      console.log(`[JiabanSceneLoader] Albedo贴图加载: ${texturesPath}/${config.textures.albedo}`);

      // 加载Normal贴图（法线）
      const normalTexture = new Texture(`${texturesPath}/${config.textures.normal}`, this.scene);
      material.bumpTexture = normalTexture;
      material.bumpTexture.level = config.normalStrength;
      console.log(`[JiabanSceneLoader] Normal贴图加载: ${texturesPath}/${config.textures.normal}`);

      // 加载PBR贴图（金属度/粗糙度/AO）
      const pbrTexture = new Texture(`${texturesPath}/${config.textures.pbrMap}`, this.scene);
      material.metallicTexture = pbrTexture;
      console.log(`[JiabanSceneLoader] PBR贴图加载: ${texturesPath}/${config.textures.pbrMap}`);

      // 设置PBR贴图通道使用（基于Babylon.js最新文档）
      material.useRoughnessFromMetallicTextureGreen = true;
      material.useMetallnessFromMetallicTextureBlue = true;
      material.useAmbientOcclusionFromMetallicTextureRed = true;

      // 添加贴图加载完成和错误监听（安全检查）
      if (albedoTexture.onLoadObservable) {
        albedoTexture.onLoadObservable.add(() => {
          console.log(`[JiabanSceneLoader] Albedo贴图加载完成: ${config.textures.albedo}`);
        });
      }

      if (albedoTexture.onErrorObservable) {
        albedoTexture.onErrorObservable.add(() => {
          console.error(`[JiabanSceneLoader] Albedo贴图加载失败: ${texturesPath}/${config.textures.albedo}`);
        });
      }

      if (normalTexture.onLoadObservable) {
        normalTexture.onLoadObservable.add(() => {
          console.log(`[JiabanSceneLoader] Normal贴图加载完成: ${config.textures.normal}`);
        });
      }

      if (normalTexture.onErrorObservable) {
        normalTexture.onErrorObservable.add(() => {
          console.error(`[JiabanSceneLoader] Normal贴图加载失败: ${texturesPath}/${config.textures.normal}`);
        });
      }

      if (pbrTexture.onLoadObservable) {
        pbrTexture.onLoadObservable.add(() => {
          console.log(`[JiabanSceneLoader] PBR贴图加载完成: ${config.textures.pbrMap}`);
        });
      }

      if (pbrTexture.onErrorObservable) {
        pbrTexture.onErrorObservable.add(() => {
          console.error(`[JiabanSceneLoader] PBR贴图加载失败: ${texturesPath}/${config.textures.pbrMap}`);
        });
      }

      console.log(`[JiabanSceneLoader] 材质贴图配置完成: ${config.name}`);

    } catch (error) {
      console.error(`[JiabanSceneLoader] 材质贴图加载失败: ${config.name}`, error);
      throw error;
    }
  }

  /**
   * 加载GLB模型
   */
  private async loadGLBModel(): Promise<AbstractMesh[]> {
    console.log('[JiabanSceneLoader] 加载GLB模型...');
    
    return new Promise((resolve, reject) => {
      // 使用最新的ImportMeshAsync API
      import("@babylonjs/core/Loading/sceneLoader").then(({ SceneLoader }) => {
        SceneLoader.ImportMeshAsync("", "", this.config.modelPath, this.scene)
          .then((result) => {
            console.log(`[JiabanSceneLoader] GLB模型加载成功，包含 ${result.meshes.length} 个网格`);
            resolve(result.meshes);
          })
          .catch((error) => {
            console.error('[JiabanSceneLoader] GLB模型加载失败:', error);
            reject(error);
          });
      });
    });
  }

  /**
   * 应用材质到网格
   */
  private applyMaterialsToMeshes(meshes: AbstractMesh[]): void {
    console.log('[JiabanSceneLoader] 应用材质到网格...');
    
    const materialA = this.materials.get("jiaban_material_A");
    const materialB = this.materials.get("jiaban_material_B");
    
    meshes.forEach((mesh, index) => {
      if (mesh.name.includes("root") || mesh.name === "__root__") {
        return; // 跳过根节点
      }
      
      // 根据网格索引分配材质（可以根据实际需要调整分配逻辑）
      const material = index % 2 === 0 ? materialA : materialB;
      if (material) {
        mesh.material = material;
        console.log(`[JiabanSceneLoader] 网格 ${mesh.name} 应用材质: ${material.name}`);
      }
    });
  }

  /**
   * 应用变换
   */
  private applyTransforms(meshes: AbstractMesh[]): void {
    console.log('[JiabanSceneLoader] 应用变换...');
    
    meshes.forEach((mesh) => {
      if (mesh.name.includes("root") || mesh.name === "__root__") {
        // 对根节点应用变换
        if (this.config.scale) {
          mesh.scaling = this.config.scale;
        }
        if (this.config.position) {
          mesh.position = this.config.position;
        }
        if (this.config.rotation) {
          mesh.rotation = this.config.rotation;
        }
      }
    });
  }

  /**
   * 获取加载的网格
   */
  getLoadedMeshes(): AbstractMesh[] {
    return this.loadedMeshes;
  }

  /**
   * 获取创建的材质
   */
  getMaterials(): Map<string, PBRMaterial> {
    return this.materials;
  }

  /**
   * 清理资源
   */
  dispose(): void {
    console.log('[JiabanSceneLoader] 清理夹板场景资源...');
    
    // 清理网格
    this.loadedMeshes.forEach(mesh => {
      mesh.dispose();
    });
    this.loadedMeshes = [];
    
    // 清理材质
    this.materials.forEach(material => {
      material.dispose();
    });
    this.materials.clear();
  }
}

/**
 * 创建夹板场景加载器的便捷函数
 */
export const createJiabanSceneLoader = (scene: Scene, config: Partial<JiabanSceneConfig> = {}): JiabanSceneLoader => {
  const defaultConfig: JiabanSceneConfig = {
    modelPath: "/models/jiaban/jiaban.glb",
    texturesPath: "/models/jiaban/textures",
    scale: new Vector3(1, 1, 1),
    position: new Vector3(0, 0, 0),
    rotation: new Vector3(0, 0, 0)
  };

  return new JiabanSceneLoader(scene, { ...defaultConfig, ...config });
};
