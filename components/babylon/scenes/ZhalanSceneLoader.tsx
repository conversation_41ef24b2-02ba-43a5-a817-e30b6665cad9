/**
 * 围栏场景加载器组件
 * 基于Context7查询的最新Babylon.js文档实现
 * 负责加载围栏场景GLB模型并应用PBR材质
 */

import { Scene, Vector3, AbstractMesh, PBRMaterial, Texture, Color3 } from "@babylonjs/core";
import "@babylonjs/loaders/glTF";

// 围栏场景配置接口
interface ZhalanSceneConfig {
  modelPath: string;
  texturesPath: string;
  scale?: Vector3;
  position?: Vector3;
  rotation?: Vector3;
}

// 围栏材质配置接口（基于原Cocos Creator项目分析）
interface ZhalanMaterialConfig {
  name: string;
  occlusion: number;
  roughness: number;
  specularIntensity: number;
  normalStrength: number;
  textures: {
    albedo: string;
    normal: string;
    pbrMap: string;
  };
}

/**
 * 围栏场景加载器类
 * 实现围栏3D模型的加载、材质配置和场景集成
 */
export class ZhalanSceneLoader {
  private scene: Scene;
  private config: Required<ZhalanSceneConfig>;
  private materials: Map<string, PBRMaterial> = new Map();
  private loadedMeshes: AbstractMesh[] = [];

  // 围栏材质配置（基于原项目zhuixingmzdb.mtl分析）
  private readonly materialConfig: ZhalanMaterialConfig = {
    name: "zhalan_material",
    occlusion: 0.252,
    roughness: 0.357,
    specularIntensity: 0.708,
    normalStrength: 1.498,
    textures: {
      albedo: "ZT_Albedo.jpg",
      normal: "ZT_Normal.jpg",
      pbrMap: "ZT_PBRMap.jpg"
    }
  };

  constructor(scene: Scene, config: ZhalanSceneConfig) {
    this.scene = scene;
    this.config = {
      scale: new Vector3(1, 1, 1),
      position: new Vector3(0, 0, 0),
      rotation: new Vector3(0, 0, 0),
      ...config
    };
  }

  /**
   * 异步加载围栏场景
   */
  async loadScene(): Promise<AbstractMesh[]> {
    try {
      console.log('[ZhalanSceneLoader] 开始加载围栏场景...');
      
      // 1. 创建PBR材质
      await this.createPBRMaterial();
      
      // 2. 加载GLB模型
      const meshes = await this.loadGLBModel();
      
      // 3. 应用材质到网格
      this.applyMaterialToMeshes(meshes);
      
      // 4. 设置变换
      this.applyTransforms(meshes);
      
      // 5. 设置碰撞检测
      this.setupCollisions(meshes);
      
      this.loadedMeshes = meshes;
      console.log('[ZhalanSceneLoader] 围栏场景加载完成');
      
      return meshes;
      
    } catch (error) {
      console.error('[ZhalanSceneLoader] 围栏场景加载失败:', error);
      throw error;
    }
  }

  /**
   * 创建围栏PBR材质
   */
  private async createPBRMaterial(): Promise<void> {
    console.log('[ZhalanSceneLoader] 创建围栏PBR材质...');

    const config = this.materialConfig;
    const material = new PBRMaterial(config.name, this.scene);

    // 设置基础PBR属性（基于原材质文件分析）
    material.roughness = config.roughness;
    material.metallic = 0.0; // 围栏是木质材料，非金属

    // 设置基础颜色为白色，让贴图主导颜色
    material.baseColor = new Color3(1.0, 1.0, 1.0);

    // 启用PBR特性
    material.usePhysicalLightFalloff = true;

    // 加载并应用贴图
    await this.loadTexturesForMaterial(material, config);

    this.materials.set(config.name, material);
    console.log(`[ZhalanSceneLoader] 围栏材质创建完成: ${config.name}`);
  }

  /**
   * 为围栏材质加载贴图
   */
  private async loadTexturesForMaterial(material: PBRMaterial, config: ZhalanMaterialConfig): Promise<void> {
    const texturesPath = this.config.texturesPath;

    console.log(`[ZhalanSceneLoader] 开始加载围栏材质贴图: ${config.name}`);
    console.log(`[ZhalanSceneLoader] 围栏贴图路径: ${texturesPath}`);

    try {
      // 加载Albedo贴图（漫反射）
      const albedoTexture = new Texture(`${texturesPath}/${config.textures.albedo}`, this.scene);
      material.baseTexture = albedoTexture;
      console.log(`[ZhalanSceneLoader] 围栏Albedo贴图加载: ${texturesPath}/${config.textures.albedo}`);

      // 加载Normal贴图（法线）
      const normalTexture = new Texture(`${texturesPath}/${config.textures.normal}`, this.scene);
      material.bumpTexture = normalTexture;
      material.bumpTexture.level = config.normalStrength;
      console.log(`[ZhalanSceneLoader] 围栏Normal贴图加载: ${texturesPath}/${config.textures.normal}`);

      // 加载PBR贴图（金属度/粗糙度/AO）
      const pbrTexture = new Texture(`${texturesPath}/${config.textures.pbrMap}`, this.scene);
      material.metallicTexture = pbrTexture;
      console.log(`[ZhalanSceneLoader] 围栏PBR贴图加载: ${texturesPath}/${config.textures.pbrMap}`);

      // 设置PBR贴图通道使用（基于Babylon.js最新文档）
      material.useRoughnessFromMetallicTextureGreen = true;
      material.useMetallnessFromMetallicTextureBlue = true;
      material.useAmbientOcclusionFromMetallicTextureRed = true;

      // 添加贴图加载完成和错误监听（安全检查）
      if (albedoTexture.onLoadObservable) {
        albedoTexture.onLoadObservable.add(() => {
          console.log(`[ZhalanSceneLoader] 围栏Albedo贴图加载完成: ${config.textures.albedo}`);
        });
      }

      if (albedoTexture.onErrorObservable) {
        albedoTexture.onErrorObservable.add(() => {
          console.error(`[ZhalanSceneLoader] 围栏Albedo贴图加载失败: ${texturesPath}/${config.textures.albedo}`);
        });
      }

      if (normalTexture.onLoadObservable) {
        normalTexture.onLoadObservable.add(() => {
          console.log(`[ZhalanSceneLoader] 围栏Normal贴图加载完成: ${config.textures.normal}`);
        });
      }

      if (normalTexture.onErrorObservable) {
        normalTexture.onErrorObservable.add(() => {
          console.error(`[ZhalanSceneLoader] 围栏Normal贴图加载失败: ${texturesPath}/${config.textures.normal}`);
        });
      }

      if (pbrTexture.onLoadObservable) {
        pbrTexture.onLoadObservable.add(() => {
          console.log(`[ZhalanSceneLoader] 围栏PBR贴图加载完成: ${config.textures.pbrMap}`);
        });
      }

      if (pbrTexture.onErrorObservable) {
        pbrTexture.onErrorObservable.add(() => {
          console.error(`[ZhalanSceneLoader] 围栏PBR贴图加载失败: ${texturesPath}/${config.textures.pbrMap}`);
        });
      }

      console.log(`[ZhalanSceneLoader] 围栏材质贴图配置完成: ${config.name}`);

    } catch (error) {
      console.error(`[ZhalanSceneLoader] 围栏材质贴图加载失败: ${config.name}`, error);
      throw error;
    }
  }

  /**
   * 加载围栏GLB模型
   */
  private async loadGLBModel(): Promise<AbstractMesh[]> {
    console.log('[ZhalanSceneLoader] 加载围栏GLB模型...');
    
    return new Promise((resolve, reject) => {
      // 使用最新的ImportMeshAsync API
      import("@babylonjs/core/Loading/sceneLoader").then(({ SceneLoader }) => {
        SceneLoader.ImportMeshAsync("", "", this.config.modelPath, this.scene)
          .then((result) => {
            console.log(`[ZhalanSceneLoader] 围栏GLB模型加载成功，包含 ${result.meshes.length} 个网格`);
            resolve(result.meshes);
          })
          .catch((error) => {
            console.error('[ZhalanSceneLoader] 围栏GLB模型加载失败:', error);
            reject(error);
          });
      });
    });
  }

  /**
   * 应用材质到围栏网格
   */
  private applyMaterialToMeshes(meshes: AbstractMesh[]): void {
    console.log('[ZhalanSceneLoader] 应用材质到围栏网格...');
    
    const material = this.materials.get(this.materialConfig.name);
    
    meshes.forEach((mesh) => {
      if (mesh.name.includes("root") || mesh.name === "__root__") {
        return; // 跳过根节点
      }
      
      if (material) {
        mesh.material = material;
        console.log(`[ZhalanSceneLoader] 围栏网格 ${mesh.name} 应用材质: ${material.name}`);
      }
    });
  }

  /**
   * 应用变换到围栏网格
   */
  private applyTransforms(meshes: AbstractMesh[]): void {
    console.log('[ZhalanSceneLoader] 应用变换到围栏网格...');
    
    meshes.forEach((mesh) => {
      if (mesh.name.includes("root") || mesh.name === "__root__") {
        return; // 跳过根节点
      }
      
      // 应用缩放
      mesh.scaling = this.config.scale.clone();
      
      // 应用位置
      mesh.position = this.config.position.clone();
      
      // 应用旋转
      mesh.rotation = this.config.rotation.clone();
    });
  }

  /**
   * 设置围栏碰撞检测
   */
  private setupCollisions(meshes: AbstractMesh[]): void {
    console.log('[ZhalanSceneLoader] 设置围栏碰撞检测...');
    
    meshes.forEach((mesh) => {
      if (mesh.name.includes("root") || mesh.name === "__root__") {
        return; // 跳过根节点
      }
      
      // 启用碰撞检测，防止玩家穿越围栏
      mesh.checkCollisions = true;
      
      // 设置接收阴影
      mesh.receiveShadows = true;
      
      // 添加到阴影投射列表
      const shadowGenerator = (this.scene as any).shadowGenerator;
      if (shadowGenerator) {
        shadowGenerator.getShadowMap()?.renderList?.push(mesh);
      }
    });
  }

  /**
   * 获取已加载的网格
   */
  getLoadedMeshes(): AbstractMesh[] {
    return this.loadedMeshes;
  }

  /**
   * 清理围栏场景资源
   */
  dispose(): void {
    console.log('[ZhalanSceneLoader] 清理围栏场景资源...');
    
    // 清理网格
    this.loadedMeshes.forEach(mesh => {
      mesh.dispose();
    });
    this.loadedMeshes = [];
    
    // 清理材质
    this.materials.forEach(material => {
      material.dispose();
    });
    this.materials.clear();
  }
}

/**
 * 创建围栏场景加载器的便捷函数
 */
export const createZhalanSceneLoader = (scene: Scene, config: Partial<ZhalanSceneConfig> = {}): ZhalanSceneLoader => {
  const defaultConfig: ZhalanSceneConfig = {
    modelPath: "/models/zhalan/zhalan.glb",
    texturesPath: "/models/zhalan/textures",
    scale: new Vector3(1, 1, 1),
    position: new Vector3(0, 0, 0),
    rotation: new Vector3(0, 0, 0)
  };

  return new ZhalanSceneLoader(scene, { ...defaultConfig, ...config });
};
