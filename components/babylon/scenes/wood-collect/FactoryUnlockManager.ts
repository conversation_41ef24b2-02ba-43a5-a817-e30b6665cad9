/**
 * FactoryUnlockManager.ts
 * 工厂解锁管理器
 * 
 * 功能：
 * - 统一管理工厂解锁流程
 * - 协调触发器、动画和工厂系统
 * - 处理玩家交互和状态管理
 * - 提供完整的解锁体验
 */

import { Scene, Vector3, AbstractMesh } from "@babylonjs/core";
import { FactoryUnlockTrigger, FactoryUnlockTriggerConfig } from "./FactoryUnlockTrigger";
import { CoinAnimationSystem, CoinAnimationConfig } from "./CoinAnimationSystem";
import { WoodFactory, WoodFactoryConfig } from "./WoodFactory";

export interface FactoryUnlockManagerConfig {
  triggerPosition: Vector3;
  factoryPosition: Vector3;
  requiredCoins: number;
  productionRate: number; // 每秒生产木材数量
  maxWoodCount: number;
  onUnlockComplete?: () => void;
  onUnlockStart?: () => void;
  onStackUpdate?: () => void; // 金币堆叠更新回调
}

export interface GameState {
  coins: number;
  wood: number;
  playerPosition: Vector3;
}

export class FactoryUnlockManager {
  private scene: Scene;
  private config: FactoryUnlockManagerConfig;
  private trigger: FactoryUnlockTrigger | null = null;
  private coinAnimationSystem: CoinAnimationSystem | null = null;
  private woodFactory: WoodFactory | null = null;
  private isUnlocked: boolean = false;
  private isUnlocking: boolean = false;

  constructor(scene: Scene, config: FactoryUnlockManagerConfig) {
    this.scene = scene;
    this.config = config;
    this.initialize();
  }

  /**
   * 初始化解锁管理器
   */
  private initialize(): void {
    // 创建触发器
    this.createTrigger();
    
    // 创建金币动画系统
    this.coinAnimationSystem = new CoinAnimationSystem(this.scene);

    console.log('[FactoryUnlockManager] 工厂解锁管理器已初始化');
  }

  /**
   * 创建解锁触发器
   */
  private createTrigger(): void {
    const triggerConfig: FactoryUnlockTriggerConfig = {
      position: this.config.triggerPosition,
      size: 1.5, // 缩小为1.5x1.5单位，创建小正方形区域
      requiredCoins: this.config.requiredCoins,
      onPlayerEnter: (trigger) => {
        console.log('[FactoryUnlockManager] 玩家进入解锁区域');
      },
      onPlayerExit: (trigger) => {
        console.log('[FactoryUnlockManager] 玩家离开解锁区域');
      },
      onUnlock: (trigger) => {
        this.onTriggerUnlock();
      }
    };

    this.trigger = new FactoryUnlockTrigger(this.scene, triggerConfig);
  }

  /**
   * 触发器解锁回调
   */
  private onTriggerUnlock(): void {
    console.log('[FactoryUnlockManager] 触发器解锁，开始创建工厂');
    this.createWoodFactory();
  }

  /**
   * 创建木材工厂
   */
  private createWoodFactory(): void {
    const factoryConfig: WoodFactoryConfig = {
      position: this.config.factoryPosition,
      productionRate: this.config.productionRate,
      maxWoodCount: this.config.maxWoodCount,
      onWoodProduced: (wood) => {
        console.log('[FactoryUnlockManager] 工厂生产了新木材');
      },
      onFactoryFull: () => {
        console.log('[FactoryUnlockManager] 工厂已满');
      }
    };

    this.woodFactory = new WoodFactory(this.scene, factoryConfig);
    
    // 开始生产
    this.woodFactory.startProduction();
    
    this.isUnlocked = true;
    
    if (this.config.onUnlockComplete) {
      this.config.onUnlockComplete();
    }

    console.log('[FactoryUnlockManager] 木材工厂创建完成并开始生产');
  }

  /**
   * 更新系统（每帧调用）
   */
  public update(gameState: GameState): void {
    if (!this.trigger || this.isUnlocked) return;

    // 检查玩家碰撞
    const isPlayerInTrigger = this.trigger.checkPlayerCollision(gameState.playerPosition);
    
    // 更新UI位置
    this.trigger.updateUI();

    // 如果玩家在触发器内且有足够金币，尝试解锁
    if (isPlayerInTrigger && !this.isUnlocking && gameState.coins >= this.config.requiredCoins) {
      this.startUnlockProcess(gameState);
    }
  }

  /**
   * 开始解锁流程
   */
  private startUnlockProcess(gameState: GameState): void {
    if (this.isUnlocking || this.isUnlocked) return;

    console.log('[FactoryUnlockManager] 开始解锁流程');
    this.isUnlocking = true;

    if (this.config.onUnlockStart) {
      this.config.onUnlockStart();
    }

    // 播放金币飞行动画
    this.playUnlockAnimation(gameState);
  }

  /**
   * 播放解锁动画
   */
  private playUnlockAnimation(gameState: GameState): void {
    if (!this.coinAnimationSystem || !this.trigger) return;

    const animationConfig: CoinAnimationConfig = {
      startPosition: gameState.playerPosition,
      endPosition: this.trigger.getPosition(),
      coinCount: this.config.requiredCoins,
      duration: 1.5, // 增加动画持续时间，让效果更明显
      onComplete: () => {
        console.log('[FactoryUnlockManager] 金币动画完成，解锁工厂');
        this.completeUnlock();
      },
      onCoinReachTarget: (coinIndex) => {
        // 金币到达目标位置，无需额外特效（金币会自动消失）
        console.log(`[FactoryUnlockManager] 金币 ${coinIndex} 已到达解锁区域`);
      },
      onStackUpdate: () => {
        // 通知外部更新金币堆叠显示
        if (this.config.onStackUpdate) {
          this.config.onStackUpdate();
        }
      }
    };

    this.coinAnimationSystem.playCoinFlyAnimation(animationConfig);
  }

  /**
   * 完成解锁
   */
  private completeUnlock(): void {
    if (!this.trigger) return;

    // 尝试解锁触发器
    const unlocked = this.trigger.tryUnlock(this.config.requiredCoins);
    
    if (unlocked) {
      console.log('[FactoryUnlockManager] 解锁成功');
    } else {
      console.error('[FactoryUnlockManager] 解锁失败');
      this.isUnlocking = false;
    }
  }

  /**
   * 手动触发解锁（用于测试）
   */
  public forceUnlock(): void {
    if (this.isUnlocked) return;

    console.log('[FactoryUnlockManager] 强制解锁工厂');
    this.createWoodFactory();
  }

  /**
   * 检查是否已解锁
   */
  public isFactoryUnlocked(): boolean {
    return this.isUnlocked;
  }

  /**
   * 检查是否正在解锁
   */
  public isUnlockInProgress(): boolean {
    return this.isUnlocking;
  }

  /**
   * 获取工厂实例
   */
  public getWoodFactory(): WoodFactory | null {
    return this.woodFactory;
  }

  /**
   * 获取触发器实例
   */
  public getTrigger(): FactoryUnlockTrigger | null {
    return this.trigger;
  }

  /**
   * 设置金币模板（用于动画）
   */
  public setCoinTemplate(coinTemplate: AbstractMesh): void {
    if (this.coinAnimationSystem) {
      this.coinAnimationSystem.setCoinTemplate(coinTemplate);
    }
  }

  /**
   * 设置金币堆叠网格引用
   */
  public setCoinStackMeshes(meshes: AbstractMesh[]): void {
    if (this.coinAnimationSystem) {
      this.coinAnimationSystem.setCoinStackMeshes(meshes);
    }
  }

  /**
   * 获取工厂位置
   */
  public getFactoryPosition(): Vector3 {
    return this.config.factoryPosition.clone();
  }

  /**
   * 获取当前状态信息
   */
  public getStatus(): {
    isUnlocked: boolean;
    isUnlocking: boolean;
    requiredCoins: number;
    woodCount: number;
    isProducing: boolean;
  } {
    return {
      isUnlocked: this.isUnlocked,
      isUnlocking: this.isUnlocking,
      requiredCoins: this.config.requiredCoins,
      woodCount: this.woodFactory ? this.woodFactory.getWoodCount() : 0,
      isProducing: this.woodFactory ? this.woodFactory.isFactoryProducing() : false
    };
  }

  /**
   * 销毁管理器
   */
  public dispose(): void {
    if (this.trigger) {
      this.trigger.dispose();
      this.trigger = null;
    }

    if (this.coinAnimationSystem) {
      this.coinAnimationSystem.dispose();
      this.coinAnimationSystem = null;
    }

    if (this.woodFactory) {
      this.woodFactory.dispose();
      this.woodFactory = null;
    }

    console.log('[FactoryUnlockManager] 工厂解锁管理器已销毁');
  }
}
