/**
 * WoodCollectionSystem.ts
 * 木材收集动画系统
 * 
 * 功能：
 * - 贝塞尔曲线木材飞行动画
 * - 木材收集和堆叠管理
 * - 动画队列和批量处理
 * - 性能优化的对象池
 */

import {
  Scene,
  Vector3,
  AbstractMesh,
  Animation,
  AnimationGroup,
  BezierCurveEase,
  EasingFunction,
  Mesh,
  MeshBuilder,
  StandardMaterial,
  Color3
} from "@babylonjs/core";

export interface WoodCollectionConfig {
  startPosition: Vector3;
  endPosition: Vector3;
  woodCount: number;
  duration: number; // 动画持续时间（秒）
  onComplete?: () => void;
  onWoodReachTarget?: (woodIndex: number) => void;
  onStackUpdate?: () => void; // 堆叠更新回调
}

export class WoodCollectionSystem {
  private scene: Scene;
  private woodTemplate: AbstractMesh | null = null;
  private woodStackMeshes: AbstractMesh[] = [];
  private animatingWoods: AbstractMesh[] = [];
  private animationGroups: AnimationGroup[] = [];

  constructor(scene: Scene) {
    this.scene = scene;
    console.log('[WoodCollectionSystem] 木材收集动画系统已初始化');
  }

  /**
   * 设置木材模板
   */
  public setWoodTemplate(template: AbstractMesh): void {
    this.woodTemplate = template;
    console.log('[WoodCollectionSystem] 木材模板已设置');
  }

  /**
   * 设置木材堆叠网格引用
   */
  public setWoodStackMeshes(stackMeshes: AbstractMesh[]): void {
    this.woodStackMeshes = stackMeshes;
    console.log(`[WoodCollectionSystem] 木材堆叠引用已设置，当前堆叠数量: ${stackMeshes.length}`);
  }

  /**
   * 从工厂木材堆中移除指定数量的木材
   */
  public removeWoodFromFactory(factoryWoodStack: AbstractMesh[], count: number): AbstractMesh[] {
    const removedWoods: AbstractMesh[] = [];
    
    for (let i = 0; i < count && factoryWoodStack.length > 0; i++) {
      const woodMesh = factoryWoodStack.pop(); // 从顶部移除
      if (woodMesh) {
        removedWoods.push(woodMesh);
        woodMesh.setEnabled(false); // 隐藏而不是立即销毁，避免动画中断
      }
    }
    
    console.log(`[WoodCollectionSystem] 从工厂移除了 ${removedWoods.length} 个木材`);
    return removedWoods;
  }

  /**
   * 播放木材收集动画（从工厂飞向玩家背后）
   */
  public playWoodCollectionAnimation(config: WoodCollectionConfig): void {
    if (!this.woodTemplate) {
      console.error('[WoodCollectionSystem] 木材模板未设置');
      return;
    }

    console.log(`[WoodCollectionSystem] 开始播放 ${config.woodCount} 个木材的收集动画`);

    // 逐个收集木材，每个木材间隔100ms启动（符合需求：每100毫秒收集1个）
    for (let i = 0; i < config.woodCount; i++) {
      setTimeout(() => {
        this.animateSingleWoodCollection(config, i);
      }, i * 100); // 每个木材间隔100ms启动
    }
  }

  /**
   * 单个木材收集动画
   */
  private animateSingleWoodCollection(config: WoodCollectionConfig, woodIndex: number): void {
    if (!this.woodTemplate) return;

    console.log(`[WoodCollectionSystem] 开始收集第 ${woodIndex + 1} 个木材`);

    // 创建动画木材
    const animWood = this.woodTemplate.clone(`animWood_${woodIndex}_${Date.now()}`);
    if (!animWood) return;

    animWood.setEnabled(true);
    animWood.position = config.startPosition.clone();

    // 确保木材保持正确的尺寸和朝向（横向摆放）
    animWood.scaling = this.woodTemplate.scaling.clone();
    // 设置飞行时的横向旋转，与最终堆叠状态保持一致
    animWood.rotation = new Vector3(0, 0, 0);

    this.animatingWoods.push(animWood);

    // 计算贝塞尔曲线路径点
    const controlPoint = this.calculateBezierControlPoint(config.startPosition, config.endPosition);

    // 创建贝塞尔曲线飞行动画
    this.createBezierFlightAnimation(animWood, config.startPosition, controlPoint, config.endPosition, woodIndex, config);
  }

  /**
   * 计算贝塞尔曲线控制点
   */
  private calculateBezierControlPoint(start: Vector3, end: Vector3): Vector3 {
    // 计算中点
    const midPoint = Vector3.Lerp(start, end, 0.5);
    
    // 添加高度偏移，创建弧形路径
    const arcHeight = 1.5; // 飞行高度
    midPoint.y += arcHeight;
    
    return midPoint;
  }

  /**
   * 创建贝塞尔曲线飞行动画
   */
  private createBezierFlightAnimation(
    wood: AbstractMesh,
    startPos: Vector3,
    controlPos: Vector3,
    endPos: Vector3,
    woodIndex: number,
    config: WoodCollectionConfig
  ): void {
    const frameRate = 60;
    const totalFrames = Math.floor(config.duration * frameRate);

    // 创建位置动画关键帧
    const positionKeys = [];
    for (let frame = 0; frame <= totalFrames; frame++) {
      const t = frame / totalFrames;
      
      // 贝塞尔曲线插值计算
      const pos1 = Vector3.Lerp(startPos, controlPos, t);
      const pos2 = Vector3.Lerp(controlPos, endPos, t);
      const finalPos = Vector3.Lerp(pos1, pos2, t);
      
      positionKeys.push({
        frame: frame,
        value: finalPos
      });
    }

    const positionAnimation = new Animation(
      `woodFlight_${woodIndex}`,
      "position",
      frameRate,
      Animation.ANIMATIONTYPE_VECTOR3,
      Animation.ANIMATIONLOOPMODE_CONSTANT
    );
    positionAnimation.setKeys(positionKeys);

    // 使用backIn缓动函数
    const easingFunction = new BezierCurveEase(0.6, -0.28, 0.735, 0.045); // backIn效果
    positionAnimation.setEasingFunction(easingFunction);

    // 设置动画到木材
    wood.animations = [positionAnimation];

    // 启动动画
    this.scene.beginAnimation(wood, 0, totalFrames, false, 1, () => {
      // 飞行动画完成，添加到玩家背后的堆叠
      this.addWoodToPlayerStack(wood, woodIndex, config);
    });
  }

  /**
   * 将木材添加到玩家背后的堆叠
   */
  private addWoodToPlayerStack(wood: AbstractMesh, woodIndex: number, config: WoodCollectionConfig): void {
    // 从动画列表中移除
    const animIndex = this.animatingWoods.indexOf(wood);
    if (animIndex > -1) {
      this.animatingWoods.splice(animIndex, 1);
    }

    // 添加到玩家木材堆叠
    this.woodStackMeshes.push(wood);

    // 触发单个木材到达回调
    if (config.onWoodReachTarget) {
      config.onWoodReachTarget(woodIndex);
    }

    // 检查是否所有木材都完成了动画
    if (this.animatingWoods.length === 0) {
      if (config.onComplete) {
        config.onComplete();
      }
      if (config.onStackUpdate) {
        config.onStackUpdate();
      }
    }

    console.log(`[WoodCollectionSystem] 木材 ${woodIndex} 已添加到玩家堆叠，当前堆叠数量: ${this.woodStackMeshes.length}`);
  }

  /**
   * 停止所有动画
   */
  public stopAllAnimations(): void {
    // 停止所有动画组
    this.animationGroups.forEach(group => {
      group.stop();
      group.dispose();
    });
    this.animationGroups = [];

    // 清理所有动画中的木材
    this.animatingWoods.forEach(wood => {
      wood.dispose();
    });
    this.animatingWoods = [];

    console.log('[WoodCollectionSystem] 所有木材收集动画已停止');
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    this.stopAllAnimations();
    
    // 清理木材堆叠
    this.woodStackMeshes.forEach(wood => {
      wood.dispose();
    });
    this.woodStackMeshes = [];

    this.woodTemplate = null;
    
    console.log('[WoodCollectionSystem] 木材收集系统已销毁');
  }

  /**
   * 获取当前木材堆叠数量
   */
  public getWoodStackCount(): number {
    return this.woodStackMeshes.length;
  }

  /**
   * 获取木材堆叠网格
   */
  public getWoodStackMeshes(): AbstractMesh[] {
    return [...this.woodStackMeshes];
  }
}
