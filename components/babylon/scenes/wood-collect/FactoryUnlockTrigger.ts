/**
 * FactoryUnlockTrigger.ts
 * 木材工厂解锁触发器组件
 * 
 * 功能：
 * - 创建3x3单位的触发区域
 * - 视觉边框和发光效果
 * - 碰撞检测和玩家交互
 * - UI提示显示管理
 */

import {
  Scene,
  Mesh,
  MeshBuilder,
  StandardMaterial,
  Color3,
  Vector3,
  AbstractMesh,
  Animation,
  AnimationGroup,
  DynamicTexture,
  Texture
} from "@babylonjs/core";
import {
  AdvancedDynamicTexture,
  Rectangle,
  TextBlock,
  Control
} from "@babylonjs/gui";

export interface FactoryUnlockTriggerConfig {
  position: Vector3;
  size: number;
  requiredCoins: number;
  onPlayerEnter?: (trigger: FactoryUnlockTrigger) => void;
  onPlayerExit?: (trigger: FactoryUnlockTrigger) => void;
  onUnlock?: (trigger: FactoryUnlockTrigger) => void;
}

export class FactoryUnlockTrigger {
  private scene: Scene;
  private config: FactoryUnlockTriggerConfig;
  private triggerMesh: Mesh | null = null;
  private borderMesh: Mesh | null = null;
  private groundMesh: Mesh | null = null; // 地面标记网格
  private cannonMesh: Mesh | null = null; // 大炮图标网格
  private glowAnimation: AnimationGroup | null = null;
  private uiTexture: AdvancedDynamicTexture | null = null;
  private uiPanel: Rectangle | null = null;
  private isPlayerInside = false;
  private isUnlocked = false;

  constructor(scene: Scene, config: FactoryUnlockTriggerConfig) {
    this.scene = scene;
    this.config = config;
    this.createTrigger();
    this.createUI();
    this.startGlowAnimation();
  }

  /**
   * 创建触发器区域
   */
  private createTrigger(): void {
    // 缩小触发器尺寸，使其更接近原版
    const triggerSize = 1.5; // 从3缩小到1.5，创建小正方形区域

    // 创建不可见的触发器碰撞体
    this.triggerMesh = MeshBuilder.CreateBox("factoryUnlockTrigger", {
      width: triggerSize,
      height: 0.1,
      depth: triggerSize
    }, this.scene);

    // 调整位置，往左移动更贴近甲板边缘
    const adjustedPosition = this.config.position.clone();
    adjustedPosition.x -= 1; // 往左移动1个单位，更贴近甲板边缘

    this.triggerMesh.position = adjustedPosition;
    this.triggerMesh.position.y += 0.05; // 稍微抬高避免z-fighting

    // 设置为不可见但参与碰撞检测
    this.triggerMesh.visibility = 0;
    this.triggerMesh.checkCollisions = true;

    // 创建视觉效果（使用原游戏图片）
    this.createVisualElements(adjustedPosition, triggerSize);

    console.log(`[FactoryUnlockTrigger] 触发器已创建，位置: ${adjustedPosition.toString()}`);
  }

  /**
   * 创建视觉元素（使用原游戏图片）
   */
  private createVisualElements(position: Vector3, size: number): void {
    // 创建地面背景（使用sjk.png纹理）
    this.groundMesh = MeshBuilder.CreateGround("factoryUnlockGround", {
      width: size,
      height: size
    }, this.scene);

    this.groundMesh.position = position.clone();
    this.groundMesh.position.y += 0.1; // 提高高度避免被甲板遮挡

    // 创建地面材质，使用原游戏的sjk.png纹理
    const groundMaterial = new StandardMaterial("factoryUnlockGroundMat", this.scene);
    const sjkTexture = new Texture("/textures/sjk.png", this.scene);
    sjkTexture.hasAlpha = true; // 启用透明度支持
    groundMaterial.diffuseTexture = sjkTexture;
    groundMaterial.useAlphaFromDiffuseTexture = true; // 使用纹理的透明度
    groundMaterial.backFaceCulling = false; // 双面渲染
    this.groundMesh.material = groundMaterial;

    // 设置渲染层级确保可见
    this.groundMesh.renderingGroupId = 1;

    // 创建大炮图标（使用cannon.png纹理）
    this.cannonMesh = MeshBuilder.CreateGround("factoryUnlockCannon", {
      width: size * 0.6, // 大炮图标稍小一些
      height: size * 0.6
    }, this.scene);

    this.cannonMesh.position = position.clone();
    this.cannonMesh.position.y += 0.12; // 稍微高于背景

    // 创建大炮材质
    const cannonMaterial = new StandardMaterial("factoryUnlockCannonMat", this.scene);
    const cannonTexture = new Texture("/textures/cannon.png", this.scene);
    cannonTexture.hasAlpha = true;
    cannonMaterial.diffuseTexture = cannonTexture;
    cannonMaterial.useAlphaFromDiffuseTexture = true;
    cannonMaterial.backFaceCulling = false;
    this.cannonMesh.material = cannonMaterial;

    // 设置渲染层级
    this.cannonMesh.renderingGroupId = 2; // 高于背景

    console.log('[FactoryUnlockTrigger] 视觉元素已创建（使用原游戏纹理）');
  }

  /**
   * 创建UI提示
   */
  private createUI(): void {
    // 创建3D UI纹理
    this.uiTexture = AdvancedDynamicTexture.CreateFullscreenUI("factoryUnlockUI");

    // 创建UI面板
    this.uiPanel = new Rectangle("factoryUnlockPanel");
    this.uiPanel.widthInPixels = 300;
    this.uiPanel.heightInPixels = 80;
    this.uiPanel.cornerRadius = 10;
    this.uiPanel.color = "white";
    this.uiPanel.thickness = 2;
    this.uiPanel.background = "rgba(0, 0, 0, 0.7)";
    this.uiPanel.isVisible = false;

    // 创建文字提示
    const textBlock = new TextBlock("factoryUnlockText");
    textBlock.text = `木材工厂\n需要 ${this.config.requiredCoins} 金币解锁`;
    textBlock.color = "white";
    textBlock.fontSize = 16;
    textBlock.fontFamily = "Arial";
    textBlock.textHorizontalAlignment = Control.HORIZONTAL_ALIGNMENT_CENTER;
    textBlock.textVerticalAlignment = Control.VERTICAL_ALIGNMENT_CENTER;

    this.uiPanel.addControl(textBlock);
    this.uiTexture.addControl(this.uiPanel);

    console.log('[FactoryUnlockTrigger] UI提示已创建');
  }

  /**
   * 开始发光动画
   */
  private startGlowAnimation(): void {
    if (!this.groundMesh) return;

    // 创建背景发光动画
    const glowAnimation = Animation.CreateAndStartAnimation(
      "factoryUnlockGlow",
      this.groundMesh,
      "visibility",
      30, // fps
      120, // 总帧数 (4秒循环)
      0.7, // 起始值
      1.0, // 结束值
      Animation.ANIMATIONLOOPMODE_CYCLE
    );

    // 如果有大炮图标，也添加发光效果
    if (this.cannonMesh) {
      Animation.CreateAndStartAnimation(
        "factoryUnlockCannonGlow",
        this.cannonMesh,
        "visibility",
        30, // fps
        120, // 总帧数 (4秒循环)
        0.8, // 起始值
        1.0, // 结束值
        Animation.ANIMATIONLOOPMODE_CYCLE
      );
    }

    console.log('[FactoryUnlockTrigger] 发光动画已启动');
  }

  /**
   * 检查玩家是否在触发器内
   */
  public checkPlayerCollision(playerPosition: Vector3): boolean {
    if (!this.triggerMesh || this.isUnlocked) return false;

    const distance = Vector3.Distance(playerPosition, this.triggerMesh.position);
    const triggerRadius = 1.5 / 2; // 使用实际的触发器尺寸
    const isInside = distance <= triggerRadius;

    // 处理进入/离开事件
    if (isInside && !this.isPlayerInside) {
      this.onPlayerEnter();
    } else if (!isInside && this.isPlayerInside) {
      this.onPlayerExit();
    }

    this.isPlayerInside = isInside;
    return isInside;
  }

  /**
   * 玩家进入触发器
   */
  private onPlayerEnter(): void {
    console.log('[FactoryUnlockTrigger] 玩家进入触发器');
    this.showUI();
    
    if (this.config.onPlayerEnter) {
      this.config.onPlayerEnter(this);
    }
  }

  /**
   * 玩家离开触发器
   */
  private onPlayerExit(): void {
    console.log('[FactoryUnlockTrigger] 玩家离开触发器');
    this.hideUI();
    
    if (this.config.onPlayerExit) {
      this.config.onPlayerExit(this);
    }
  }

  /**
   * 显示UI提示
   */
  private showUI(): void {
    if (this.uiPanel) {
      this.uiPanel.isVisible = true;
      
      // 计算UI位置（屏幕坐标）
      const camera = this.scene.activeCamera;
      if (camera && this.triggerMesh) {
        const worldPos = this.triggerMesh.position.clone();
        worldPos.y += 2; // UI显示在触发器上方
        
        const screenPos = Vector3.Project(
          worldPos,
          camera.getWorldMatrix(),
          camera.getProjectionMatrix(),
          camera.viewport.toGlobal(this.scene.getEngine().getRenderWidth(), this.scene.getEngine().getRenderHeight())
        );

        this.uiPanel.leftInPixels = screenPos.x - 150; // 居中
        this.uiPanel.topInPixels = screenPos.y - 40;
      }
    }
  }

  /**
   * 隐藏UI提示
   */
  private hideUI(): void {
    if (this.uiPanel) {
      this.uiPanel.isVisible = false;
    }
  }

  /**
   * 尝试解锁工厂
   */
  public tryUnlock(playerCoins: number): boolean {
    if (this.isUnlocked || !this.isPlayerInside) return false;

    if (playerCoins >= this.config.requiredCoins) {
      this.unlock();
      return true;
    }

    console.log(`[FactoryUnlockTrigger] 金币不足，需要 ${this.config.requiredCoins}，当前 ${playerCoins}`);
    return false;
  }

  /**
   * 解锁工厂
   */
  private unlock(): void {
    console.log('[FactoryUnlockTrigger] 工厂解锁成功！');
    this.isUnlocked = true;
    this.hideUI();

    // 停止发光动画
    if (this.glowAnimation) {
      this.glowAnimation.stop();
    }

    // 隐藏触发器视觉效果
    if (this.groundMesh) {
      this.groundMesh.dispose();
      this.groundMesh = null;
    }

    // 隐藏大炮图标
    if (this.cannonMesh) {
      this.cannonMesh.dispose();
      this.cannonMesh = null;
    }

    if (this.config.onUnlock) {
      this.config.onUnlock(this);
    }
  }

  /**
   * 更新UI位置（每帧调用）
   */
  public updateUI(): void {
    if (this.uiPanel && this.uiPanel.isVisible && this.triggerMesh) {
      this.showUI(); // 重新计算位置
    }
  }

  /**
   * 获取触发器位置
   */
  public getPosition(): Vector3 {
    return this.config.position.clone();
  }

  /**
   * 检查是否已解锁
   */
  public isFactoryUnlocked(): boolean {
    return this.isUnlocked;
  }

  /**
   * 销毁触发器
   */
  public dispose(): void {
    if (this.triggerMesh) {
      this.triggerMesh.dispose();
      this.triggerMesh = null;
    }

    if (this.groundMesh) {
      this.groundMesh.dispose();
      this.groundMesh = null;
    }

    if (this.cannonMesh) {
      this.cannonMesh.dispose();
      this.cannonMesh = null;
    }

    if (this.glowAnimation) {
      this.glowAnimation.stop();
      this.glowAnimation = null;
    }

    if (this.uiTexture) {
      this.uiTexture.dispose();
      this.uiTexture = null;
    }

    console.log('[FactoryUnlockTrigger] 触发器已销毁');
  }
}
