/**
 * CoinAnimationSystem.ts
 * 金币动画系统
 * 
 * 功能：
 * - 贝塞尔曲线金币飞行动画
 * - 金币消耗和堆叠管理
 * - 动画队列和批量处理
 * - 性能优化的对象池
 */

import {
  Scene,
  Vector3,
  AbstractMesh,
  Animation,
  AnimationGroup,
  BezierCurveEase,
  EasingFunction,
  Mesh,
  MeshBuilder,
  StandardMaterial,
  Color3
} from "@babylonjs/core";

export interface CoinAnimationConfig {
  startPosition: Vector3;
  endPosition: Vector3;
  coinCount: number;
  duration: number; // 动画持续时间（秒）
  onComplete?: () => void;
  onCoinReachTarget?: (coinIndex: number) => void;
  onStackUpdate?: () => void; // 堆叠更新回调
}

export class CoinAnimationSystem {
  private scene: Scene;
  private coinTemplate: AbstractMesh | null = null;
  private animatingCoins: Mesh[] = [];
  private animationGroups: AnimationGroup[] = [];
  private coinStackMeshes: AbstractMesh[] = []; // 金币堆叠网格的引用

  constructor(scene: Scene, coinTemplate?: AbstractMesh) {
    this.scene = scene;
    this.coinTemplate = coinTemplate;

    // 如果没有提供模板，创建默认金币
    if (!this.coinTemplate) {
      this.createDefaultCoinTemplate();
    }

    console.log('[CoinAnimationSystem] 金币动画系统已初始化');
  }

  /**
   * 设置金币堆叠网格引用
   */
  public setCoinStackMeshes(meshes: AbstractMesh[]): void {
    this.coinStackMeshes = meshes;
  }

  /**
   * 从金币堆叠中移除指定数量的金币
   */
  public removeCoinFromStack(count: number): void {
    for (let i = 0; i < count && this.coinStackMeshes.length > 0; i++) {
      const coinMesh = this.coinStackMeshes.pop(); // 从顶部移除
      if (coinMesh) {
        coinMesh.setEnabled(false); // 隐藏而不是立即销毁，避免动画中断
        setTimeout(() => {
          coinMesh.dispose(); // 延迟销毁
        }, 100);
      }
    }
  }

  /**
   * 创建默认金币模板
   */
  private createDefaultCoinTemplate(): void {
    this.coinTemplate = MeshBuilder.CreateCylinder("coinTemplate", {
      height: 0.1,
      diameter: 0.3
    }, this.scene);

    const material = new StandardMaterial("coinMaterial", this.scene);
    material.diffuseColor = new Color3(1, 0.8, 0); // 金色
    material.specularColor = new Color3(1, 1, 0.5);
    this.coinTemplate.material = material;

    // 设置金币水平躺放（与主场景中的设置一致）
    this.coinTemplate.rotation = new Vector3(Math.PI / 2, 0, 0);

    // 隐藏模板
    this.coinTemplate.setEnabled(false);

    console.log('[CoinAnimationSystem] 默认金币模板已创建');
  }

  /**
   * 播放金币消耗动画（重新实现，严格按照原版规范）
   */
  public playCoinFlyAnimation(config: CoinAnimationConfig): void {
    if (!this.coinTemplate) {
      console.error('[CoinAnimationSystem] 金币模板未设置');
      return;
    }

    console.log(`[CoinAnimationSystem] 开始播放 ${config.coinCount} 个金币的消耗动画`);

    // 从堆叠顶部开始逐个消耗金币
    for (let i = 0; i < config.coinCount; i++) {
      setTimeout(() => {
        this.animateSingleCoinConsumption(config, i);
      }, i * 50); // 每个金币间隔50ms启动
    }
  }

  /**
   * 单个金币消耗动画（按原版规范实现）
   */
  private animateSingleCoinConsumption(config: CoinAnimationConfig, coinIndex: number): void {
    if (!this.coinTemplate) return;

    // 从堆叠顶部获取金币
    const stackCoinIndex = this.coinStackMeshes.length - 1 - coinIndex;
    if (stackCoinIndex < 0 || !this.coinStackMeshes[stackCoinIndex]) {
      console.warn(`[CoinAnimationSystem] 堆叠中没有足够的金币进行动画`);
      return;
    }

    const stackCoin = this.coinStackMeshes[stackCoinIndex];
    const startPosition = stackCoin.position.clone();

    // 隐藏堆叠中的原金币
    stackCoin.setEnabled(false);

    // 创建动画金币
    const animCoin = this.coinTemplate.clone(`animCoin_${coinIndex}_${Date.now()}`);
    animCoin.setEnabled(true);
    animCoin.position = startPosition.clone();

    // 确保金币保持正确的尺寸和朝向
    // 使用堆叠金币的缩放比例（从模板继承）
    animCoin.scaling = stackCoin.scaling.clone();

    // 确保金币保持水平躺放状态（与堆叠金币一致）
    animCoin.rotation = stackCoin.rotation.clone();

    this.animatingCoins.push(animCoin);

    // 计算贝塞尔曲线路径点
    const controlPoint = this.calculateBezierControlPoint(startPosition, config.endPosition);

    // 创建贝塞尔曲线飞行动画
    this.createBezierFlightAnimation(animCoin, startPosition, controlPoint, config.endPosition, coinIndex, config);
  }

  /**
   * 计算贝塞尔曲线控制点（金币飞过角色头部）
   */
  private calculateBezierControlPoint(startPos: Vector3, endPos: Vector3): Vector3 {
    // 控制点位于起点和终点的中间，但高度更高（飞过角色头部）
    const midPoint = Vector3.Lerp(startPos, endPos, 0.5);
    midPoint.y += 2.5; // 飞行高度2.5单位，确保飞过角色头部
    return midPoint;
  }

  /**
   * 创建贝塞尔曲线飞行动画（按原版规范）
   */
  private createBezierFlightAnimation(
    coin: AbstractMesh,
    startPos: Vector3,
    controlPoint: Vector3,
    endPos: Vector3,
    coinIndex: number,
    config: CoinAnimationConfig
  ): void {
    const duration = 0.25; // 飞行时间0.25秒
    const frameRate = 60;
    const totalFrames = Math.floor(duration * frameRate);

    // 创建贝塞尔曲线位置动画
    const positionKeys = [];
    for (let frame = 0; frame <= totalFrames; frame++) {
      const t = frame / totalFrames;

      // 二次贝塞尔曲线公式: B(t) = (1-t)²P0 + 2(1-t)tP1 + t²P2
      const oneMinusT = 1 - t;
      const position = startPos.scale(oneMinusT * oneMinusT)
        .add(controlPoint.scale(2 * oneMinusT * t))
        .add(endPos.scale(t * t));

      positionKeys.push({
        frame: frame,
        value: position
      });
    }

    const positionAnimation = new Animation(
      `coinFlight_${coinIndex}`,
      "position",
      frameRate,
      Animation.ANIMATIONTYPE_VECTOR3,
      Animation.ANIMATIONLOOPMODE_CONSTANT
    );
    positionAnimation.setKeys(positionKeys);

    // 使用backIn缓动函数
    const easingFunction = new BezierCurveEase(0.6, -0.28, 0.735, 0.045); // backIn效果
    positionAnimation.setEasingFunction(easingFunction);

    // 移除缩放动画 - 金币在飞行过程中应该保持正常大小
    // 根据用户要求，金币尺寸应该始终保持与堆叠金币相同（scaling应该始终为Vector3(1,1,1)）
    // 不再添加缩放动画，让金币保持原始大小

    // 设置动画到金币（只使用位置动画，不改变尺寸）
    coin.animations = [positionAnimation];

    // 启动动画
    this.scene.beginAnimation(coin, 0, totalFrames, false, 1, () => {
      // 飞行动画完成，开始消失动画
      this.createCoinDisappearAnimation(coin, coinIndex, config);
    });
  }

  /**
   * 创建金币消失动画（到达解锁区域后）
   */
  private createCoinDisappearAnimation(coin: AbstractMesh, coinIndex: number, config: CoinAnimationConfig): void {
    // 消失动画：从当前缩放 → 0.0 (0.2秒)
    // 使用金币当前的缩放值作为起始值，确保动画连贯
    const currentScaling = coin.scaling.clone();
    const disappearAnimation = Animation.CreateAndStartAnimation(
      `coinDisappear_${coinIndex}`,
      coin,
      "scaling",
      60,
      12, // 0.2秒 * 60fps = 12帧
      currentScaling, // 使用当前缩放值而不是固定的(1,1,1)
      new Vector3(0, 0, 0),
      Animation.ANIMATIONLOOPMODE_CONSTANT
    );

    if (disappearAnimation) {
      disappearAnimation.onAnimationEndObservable.add(() => {
        // 从动画列表中移除
        const animIndex = this.animatingCoins.indexOf(coin);
        if (animIndex > -1) {
          this.animatingCoins.splice(animIndex, 1);
        }

        // 从堆叠中移除对应的金币
        if (this.coinStackMeshes.length > 0) {
          const stackCoin = this.coinStackMeshes.pop();
          if (stackCoin) {
            stackCoin.dispose();
          }
        }

        // 销毁动画金币
        coin.dispose();

        // 触发单个金币到达回调
        if (config.onCoinReachTarget) {
          config.onCoinReachTarget(coinIndex);
        }

        // 检查是否所有金币都完成了动画
        if (this.animatingCoins.length === 0) {
          if (config.onComplete) {
            config.onComplete();
          }
          if (config.onStackUpdate) {
            config.onStackUpdate();
          }
        }

        console.log(`[CoinAnimationSystem] 金币 ${coinIndex} 消失动画完成`);
      });
    }
  }

  /**
   * 批量播放金币收集动画（从目标位置飞向玩家）
   */
  public playCollectCoinsAnimation(
    sourcePosition: Vector3,
    targetPosition: Vector3,
    coinCount: number,
    onComplete?: () => void
  ): void {
    const config: CoinAnimationConfig = {
      startPosition: sourcePosition,
      endPosition: targetPosition,
      coinCount: coinCount,
      duration: 0.4, // 收集动画稍快
      onComplete: onComplete
    };

    this.playCoinFlyAnimation(config);
  }

  /**
   * 停止所有动画
   */
  public stopAllAnimations(): void {
    // 停止所有动画组
    this.animationGroups.forEach(group => {
      group.stop();
      group.dispose();
    });
    this.animationGroups = [];

    // 清理所有动画中的金币
    this.animatingCoins.forEach(coin => {
      coin.dispose();
    });
    this.animatingCoins = [];

    console.log('[CoinAnimationSystem] 所有动画已停止');
  }

  /**
   * 获取当前动画中的金币数量
   */
  public getAnimatingCoinCount(): number {
    return this.animatingCoins.length;
  }

  /**
   * 设置金币模板
   */
  public setCoinTemplate(template: AbstractMesh): void {
    this.coinTemplate = template;
    console.log('[CoinAnimationSystem] 金币模板已更新');
  }

  /**
   * 销毁动画系统
   */
  public dispose(): void {
    this.stopAllAnimations();
    
    if (this.coinTemplate) {
      this.coinTemplate.dispose();
      this.coinTemplate = null;
    }

    console.log('[CoinAnimationSystem] 金币动画系统已销毁');
  }
}
