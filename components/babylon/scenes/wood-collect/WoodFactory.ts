/**
 * WoodFactory.ts
 * 木材工厂生产系统
 * 
 * 功能：
 * - 每秒生产3个木材
 * - 2x12网格堆叠系统
 * - 自动堆叠管理
 * - 工厂建筑3D模型
 */

import {
  Scene,
  Vector3,
  AbstractMesh,
  Mesh,
  MeshBuilder,
  StandardMaterial,
  Color3,
  Animation
} from "@babylonjs/core";

export interface WoodFactoryConfig {
  position: Vector3;
  productionRate: number; // 每秒生产数量
  maxWoodCount: number; // 最大木材数量
  onWoodProduced?: (wood: AbstractMesh) => void;
  onFactoryFull?: () => void;
}

export interface WoodStackConfig {
  rows: number; // 排数
  columns: number; // 列数
  xSpacing: number; // X轴间距
  zSpacing: number; // Z轴间距
  layerHeight: number; // 层高
}

export class WoodFactory {
  private scene: Scene;
  private config: WoodFactoryConfig;
  private stackConfig: WoodStackConfig;
  private factoryBuilding: Mesh | null = null;
  private woodTemplate: AbstractMesh | null = null;
  private woodStack: AbstractMesh[] = [];
  private isProducing: boolean = false;
  private productionInterval: NodeJS.Timeout | null = null;

  constructor(scene: Scene, config: WoodFactoryConfig) {
    this.scene = scene;
    this.config = config;
    
    // 堆叠配置（根据截图优化）
    // 2排木材，每排12个，沿Z轴负方向延伸
    this.stackConfig = {
      rows: 2,        // 2排（沿X轴方向）
      columns: 12,    // 每排12个（沿Z轴方向）- 改为12个，能被3整除
      xSpacing: 0.8,  // X轴间距（2排之间的距离）- 进一步增加，明显分开两排
      zSpacing: -0.35, // Z轴间距（保持前后间距，效果很好）
      layerHeight: 0.23 // 层高（垂直堆叠时使用）
    };

    this.createFactory();
    this.createWoodTemplate();
    
    console.log('[WoodFactory] 木材工厂已创建');
  }

  /**
   * 创建工厂（无建筑物，仅初始化生产系统）
   */
  private createFactory(): void {
    // 根据需求，不创建任何3D建筑物
    // 工厂将直接在触发器位置生产木材，无需视觉建筑
    this.factoryBuilding = null;

    console.log('[WoodFactory] 工厂生产系统已初始化（无建筑物）');
  }

  /**
   * 创建木材模板
   * 根据截图调整木材尺寸和外观
   */
  private createWoodTemplate(): void {
    this.woodTemplate = MeshBuilder.CreateBox("woodTemplate", {
      width: 0.25,  // 宽度（X轴）
      height: 0.12, // 高度（Y轴）
      depth: 0.6    // 深度（Z轴）- 长度增加为原来的2倍，提升木材视觉效果
    }, this.scene);

    const woodMaterial = new StandardMaterial("woodMaterial", this.scene);
    woodMaterial.diffuseColor = new Color3(0.7, 0.5, 0.2); // 更深的木材颜色，符合截图
    woodMaterial.specularColor = new Color3(0.1, 0.1, 0.1);
    this.woodTemplate.material = woodMaterial;

    // 设置模板的横向摆放（参考截图，木材应该横着摆放）
    this.woodTemplate.rotation.y = Math.PI / 2; // 绕Y轴旋转90度

    // 隐藏模板
    this.woodTemplate.setEnabled(false);

    console.log('[WoodFactory] 木材模板已创建');
  }

  /**
   * 开始生产
   */
  public startProduction(): void {
    if (this.isProducing) return;

    this.isProducing = true;
    // 改为每秒批量生产，而不是按间隔单个生产
    const batchInterval = 1000; // 每1秒生产一批

    this.productionInterval = setInterval(() => {
      this.produceBatchWood();
    }, batchInterval);

    console.log(`[WoodFactory] 开始生产，每秒生产 ${this.config.productionRate} 个木材`);
  }

  /**
   * 停止生产
   */
  public stopProduction(): void {
    if (!this.isProducing) return;

    this.isProducing = false;
    if (this.productionInterval) {
      clearInterval(this.productionInterval);
      this.productionInterval = null;
    }

    console.log('[WoodFactory] 停止生产');
  }

  /**
   * 批量生产木材（每秒生产3个，使用贝塞尔曲线飞行动画）
   */
  private produceBatchWood(): void {
    // 移除高度限制，允许无限堆叠
    if (!this.woodTemplate) return;

    // 批量生产木材数量（通常是3个）
    const batchSize = this.config.productionRate;
    const actualBatchSize = batchSize; // 不再限制数量

    console.log(`[WoodFactory] 开始批量生产 ${actualBatchSize} 个木材`);

    // 生产起始位置（工厂上方）
    const factoryPosition = this.config.position.clone();
    const startPosition = new Vector3(
      factoryPosition.x,
      factoryPosition.y + 3, // 从工厂上方3单位高度开始
      factoryPosition.z - 2  // 稍微向前一点
    );

    // 批量创建木材，3个同时飞入（作为一组）
    for (let i = 0; i < actualBatchSize; i++) {
      // 移除setTimeout，让所有木材同时开始飞行
      this.createFlyingWood(startPosition, i);
    }
  }

  /**
   * 创建单个飞行木材
   */
  private createFlyingWood(baseStartPosition: Vector3, index: number): void {
    if (!this.woodTemplate) return;
    // 移除数量限制，允许无限生产

    // 创建新木材
    const wood = this.woodTemplate.clone(`wood_${Date.now()}_${index}`, null);
    if (!wood) return;

    wood.setEnabled(true);

    // 设置木材横向摆放（参考截图，木材应该横着摆放）
    wood.rotation.y = Math.PI / 2; // 绕Y轴旋转90度，让木材横向摆放

    // 为每个木材计算不同的起始位置，形成扇形飞入效果
    const adjustedStartPosition = this.calculateBatchStartPosition(baseStartPosition, index);
    wood.position = adjustedStartPosition.clone();

    // 计算目标堆叠位置
    const stackPosition = this.calculateStackPosition(this.woodStack.length);

    // 添加到堆叠数组
    this.woodStack.push(wood);

    // 创建贝塞尔曲线飞行动画
    this.createWoodFlyAnimation(wood, adjustedStartPosition, stackPosition, index);

    // 触发生产回调
    if (this.config.onWoodProduced) {
      this.config.onWoodProduced(wood);
    }
  }

  /**
   * 计算批量木材的起始位置
   * 让3个木材从不同位置同时飞入，形成扇形效果
   */
  private calculateBatchStartPosition(basePosition: Vector3, index: number): Vector3 {
    const position = basePosition.clone();

    // 根据索引调整起始位置，形成扇形分布
    switch (index) {
      case 0:
        // 第一个木材：稍微向左
        position.x -= 1;
        position.z -= 0.5;
        break;
      case 1:
        // 第二个木材：中间位置（保持原位）
        break;
      case 2:
        // 第三个木材：稍微向右
        position.x += 1;
        position.z += 0.5;
        break;
      default:
        // 更多木材的情况，继续扩展扇形
        const offset = (index - 1) * 0.8;
        position.x += offset * Math.cos(index * 0.5);
        position.z += offset * Math.sin(index * 0.5);
        break;
    }

    return position;
  }

  /**
   * 创建木材贝塞尔曲线飞行动画
   * 参考原版Cocos Creator项目的动画效果
   */
  private createWoodFlyAnimation(
    wood: AbstractMesh,
    startPos: Vector3,
    endPos: Vector3,
    index: number
  ): void {
    const duration = 0.5; // 飞行时间0.5秒，比金币稍慢一些
    const frameRate = 60;
    const totalFrames = Math.floor(duration * frameRate);

    // 创建贝塞尔曲线位置动画
    const positionKeys = [];
    for (let frame = 0; frame <= totalFrames; frame++) {
      const t = frame / totalFrames;

      // 三次贝塞尔曲线公式，参考原版XTweenUtils.bezierTo实现
      const oneMinusT = 1 - t;
      const oneMinusT2 = oneMinusT * oneMinusT;
      const oneMinusT3 = oneMinusT2 * oneMinusT;
      const t2 = t * t;
      const t3 = t2 * t;

      // 计算控制点（参考原版的0.25和0.75比例）
      const cp1 = new Vector3(
        startPos.x + 0.25 * (endPos.x - startPos.x),
        Math.max(startPos.y, endPos.y) + 1.5 * 0.8, // 高度参数
        startPos.z + 0.25 * (endPos.z - startPos.z)
      );
      const cp2 = new Vector3(
        startPos.x + 0.75 * (endPos.x - startPos.x),
        Math.max(startPos.y, endPos.y) + 1.5 * 0.4, // 高度参数
        startPos.z + 0.75 * (endPos.z - startPos.z)
      );

      // 三次贝塞尔曲线计算
      const position = new Vector3(
        oneMinusT3 * startPos.x + 3 * oneMinusT2 * t * cp1.x + 3 * oneMinusT * t2 * cp2.x + t3 * endPos.x,
        oneMinusT3 * startPos.y + 3 * oneMinusT2 * t * cp1.y + 3 * oneMinusT * t2 * cp2.y + t3 * endPos.y,
        oneMinusT3 * startPos.z + 3 * oneMinusT2 * t * cp1.z + 3 * oneMinusT * t2 * cp2.z + t3 * endPos.z
      );

      positionKeys.push({
        frame: frame,
        value: position
      });
    }

    const positionAnimation = new Animation(
      `woodFlight_${index}`,
      "position",
      frameRate,
      Animation.ANIMATIONTYPE_VECTOR3,
      Animation.ANIMATIONLOOPMODE_CONSTANT
    );
    positionAnimation.setKeys(positionKeys);

    // 设置动画到木材
    wood.animations = [positionAnimation];

    // 启动动画
    this.scene.beginAnimation(wood, 0, totalFrames, false, 1, () => {
      console.log(`[WoodFactory] 木材 ${index} 飞行动画完成`);
    });
  }



  /**
   * 计算堆叠位置
   * 根据截图优化：木材沿Z轴负方向延伸，形成2x12网格
   */
  private calculateStackPosition(woodIndex: number): Vector3 {
    const woodsPerLayer = this.stackConfig.rows * this.stackConfig.columns;
    const layerIndex = Math.floor(woodIndex / woodsPerLayer);
    const positionInLayer = woodIndex % woodsPerLayer;

    const row = Math.floor(positionInLayer / this.stackConfig.columns);
    const col = positionInLayer % this.stackConfig.columns;

    const position = this.config.position.clone();

    // 计算X位置（2排，居中排列）
    // row=0是第一排，row=1是第二排
    const totalRowWidth = (this.stackConfig.rows - 1) * Math.abs(this.stackConfig.xSpacing);
    position.x += row * this.stackConfig.xSpacing - totalRowWidth / 2;

    // 计算Z位置（12列，沿Z轴负方向延伸）
    // col=0是最前面，col=11是最后面
    const totalColDepth = (this.stackConfig.columns - 1) * Math.abs(this.stackConfig.zSpacing);
    position.z += col * this.stackConfig.zSpacing + totalColDepth / 2;

    // 计算Y位置（层高）
    // 确保木材在夹板表面之上，基础高度设为0.5（夹板厚度 + 木材高度的一半）
    position.y += layerIndex * this.stackConfig.layerHeight + 0.5;

    return position;
  }

  /**
   * 收集木材（玩家交互）
   */
  public collectWood(count: number = 1): AbstractMesh[] {
    const collectedWood: AbstractMesh[] = [];
    
    for (let i = 0; i < count && this.woodStack.length > 0; i++) {
      const wood = this.woodStack.pop();
      if (wood) {
        collectedWood.push(wood);
      }
    }

    console.log(`[WoodFactory] 收集了 ${collectedWood.length} 个木材，剩余 ${this.woodStack.length} 个`);
    return collectedWood;
  }

  /**
   * 获取当前木材数量
   */
  public getWoodCount(): number {
    return this.woodStack.length;
  }

  /**
   * 检查是否可以收集木材
   */
  public canCollectWood(): boolean {
    return this.woodStack.length > 0;
  }

  /**
   * 获取工厂位置
   */
  public getPosition(): Vector3 {
    return this.config.position.clone();
  }

  /**
   * 检查是否正在生产
   */
  public isFactoryProducing(): boolean {
    return this.isProducing;
  }

  /**
   * 设置生产速率
   */
  public setProductionRate(rate: number): void {
    this.config.productionRate = rate;
    
    // 如果正在生产，重启生产以应用新速率
    if (this.isProducing) {
      this.stopProduction();
      this.startProduction();
    }

    console.log(`[WoodFactory] 生产速率已设置为 ${rate}/秒`);
  }

  /**
   * 获取工厂建筑网格
   */
  public getFactoryMesh(): Mesh | null {
    return this.factoryBuilding;
  }

  /**
   * 销毁工厂
   */
  public dispose(): void {
    this.stopProduction();

    // 清理所有木材
    this.woodStack.forEach(wood => {
      wood.dispose();
    });
    this.woodStack = [];

    // 清理工厂建筑
    if (this.factoryBuilding) {
      this.factoryBuilding.dispose();
      this.factoryBuilding = null;
    }

    // 清理木材模板
    if (this.woodTemplate) {
      this.woodTemplate.dispose();
      this.woodTemplate = null;
    }

    console.log('[WoodFactory] 木材工厂已销毁');
  }
}
