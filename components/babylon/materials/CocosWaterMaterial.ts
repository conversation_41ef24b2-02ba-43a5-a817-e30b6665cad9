import {
  Scene,
  StandardMaterial,
  Color3,
  Vector2,
  Texture,
  Effect,
  ShaderMaterial,
  Vector3,
  Engine,
  Vector4,
  RawTexture
} from '@babylonjs/core';

/**
 * 自定义海水材质，精确还原Cocos Creator项目的海水效果
 * 基于原项目的mywater.effect着色器实现
 */
export class CocosWaterMaterial extends ShaderMaterial {
  // 原项目参数
  public shallowColor: Color3;
  public causticColor: Color3;
  public causticStrength: number;
  public causticScale: number;
  public causticSpeed: number;
  public causticRGBSplit: number;
  public causticDepth: number;
  public causticTexture: Texture | null = null;

  constructor(name: string, scene: Scene) {
    // 注册着色器
    CocosWaterMaterial.registerShaders();

    super(name, scene, 'cocosWater', {
      attributes: ['position', 'normal', 'uv'],
      uniforms: [
        'world', 'worldView', 'worldViewProjection', 'view', 'projection',
        'shallowColor', 'causticColor', 'causticParams1', 'causticParams2', 'time'
      ],
      samplers: ['causticTexture']
    });

    // 设置原项目的默认参数值，调整为更明显的效果
    this.shallowColor = new Color3(27/255, 145/255, 211/255);
    this.causticColor = new Color3(69/255, 236/255, 216/255);
    this.causticStrength = 1.2;  // 增强焦散强度
    this.causticScale = 8.0;     // 调整焦散缩放
    this.causticSpeed = 0.8;     // 加快动画速度
    this.causticRGBSplit = 5.0;  // 增强RGB分离效果
    this.causticDepth = 2.4;

    // 加载焦散纹理
    this.loadCausticTexture(scene);

    // 设置材质属性
    this.backFaceCulling = false;
    this.setFloat('time', 0);
    
    // 更新uniforms
    this.updateUniforms();

    // 启动动画循环
    this.startAnimation(scene);
  }

  private static registerShaders(): void {
    if (!Effect.ShadersStore['cocosWaterVertexShader']) {
      // 顶点着色器
      Effect.ShadersStore['cocosWaterVertexShader'] = `
        precision highp float;

        attribute vec3 position;
        attribute vec3 normal;
        attribute vec2 uv;

        uniform mat4 worldViewProjection;
        uniform mat4 world;

        varying vec2 v_uv;
        varying vec3 v_position;
        varying vec3 v_worldPosition;

        void main() {
          vec4 worldPos = world * vec4(position, 1.0);
          v_worldPosition = worldPos.xyz;
          v_position = position;
          v_uv = uv;
          
          gl_Position = worldViewProjection * vec4(position, 1.0);
        }
      `;

      // 片段着色器 - 简化调试版本，先确保基本显示工作
      Effect.ShadersStore['cocosWaterFragmentShader'] = `
        precision highp float;

        #define PI 3.14159265359

        varying vec2 v_uv;
        varying vec3 v_position;
        varying vec3 v_worldPosition;

        uniform vec4 shallowColor;
        uniform vec4 causticColor;
        uniform vec4 causticParams1; // x: strength, y: scale, z: speed, w: rgbSplit
        uniform vec4 causticParams2; // w: depth
        uniform float time;
        uniform sampler2D causticTexture;

        void main() {
          // 基础水色
          vec4 waterColor = shallowColor;

          // 获取焦散参数
          float causticStrength = causticParams1.x;
          float causticScale = causticParams1.y;
          float causticSpeed = causticParams1.z;
          float causticRGBSplit = causticParams1.w;
          float causticDepth = causticParams2.w;

          // 创建动态UV坐标用于焦散效果
          vec2 causticUV1 = v_uv * causticScale + time * causticSpeed * vec2(0.1, 0.15);
          vec2 causticUV2 = v_uv * causticScale * 1.3 + time * causticSpeed * vec2(-0.12, 0.08);

          // 采样焦散纹理（两层叠加产生复杂效果）
          float caustic1 = texture2D(causticTexture, causticUV1).r;
          float caustic2 = texture2D(causticTexture, causticUV2).r;

          // 组合焦散效果
          float causticEffect = (caustic1 * caustic2) * causticStrength;

          // RGB分离效果（模拟水下光线折射）
          vec2 rgbOffset = vec2(causticRGBSplit / 1000.0, 0.0);
          float causticR = texture2D(causticTexture, causticUV1 + rgbOffset).r;
          float causticG = texture2D(causticTexture, causticUV1).g;
          float causticB = texture2D(causticTexture, causticUV1 - rgbOffset).b;

          vec3 causticRGB = vec3(causticR, causticG, causticB) * causticStrength;

          // 波纹效果
          float wave1 = sin(v_uv.x * 20.0 + time * 2.0) * 0.02;
          float wave2 = cos(v_uv.y * 15.0 + time * 1.5) * 0.02;
          float waveEffect = wave1 + wave2;

          // 组合所有效果
          vec3 finalColor = waterColor.rgb;

          // 添加焦散光影
          finalColor += causticColor.rgb * causticEffect;
          finalColor += causticRGB * 0.3;

          // 添加波纹反射
          finalColor += vec3(0.2, 0.4, 0.6) * waveEffect;

          // 时间变化的整体亮度调制
          float timeModulation = sin(time * 0.8) * 0.1 + 0.9;
          finalColor *= timeModulation;

          // 深度渐变效果
          float depthFactor = 1.0 - (v_uv.x + v_uv.y) * 0.1;
          finalColor = mix(finalColor, finalColor * 0.7, depthFactor);

          gl_FragColor = vec4(finalColor, 0.85);
        }
      `;
    }
  }

  private async loadCausticTexture(scene: Scene): Promise<void> {
    try {
      // 尝试加载原项目的焦散纹理
      this.causticTexture = new Texture('/models/water/Caustics.png', scene);
      this.setTexture('causticTexture', this.causticTexture);
      console.log('[CocosWaterMaterial] 焦散纹理加载成功');
    } catch (error) {
      console.warn('[CocosWaterMaterial] 焦散纹理加载失败，使用默认纹理:', error);
      // 创建默认的焦散纹理
      this.createDefaultCausticTexture(scene);
    }
  }

  private createDefaultCausticTexture(scene: Scene): void {
    // 创建程序化焦散纹理
    const size = 256;
    const data = new Uint8Array(size * size * 4);
    
    for (let i = 0; i < size; i++) {
      for (let j = 0; j < size; j++) {
        const index = (i * size + j) * 4;
        const x = (i / size) * 2 - 1;
        const y = (j / size) * 2 - 1;
        
        // 创建类似焦散的图案
        const dist = Math.sqrt(x * x + y * y);
        const angle = Math.atan2(y, x);
        const caustic = Math.sin(dist * 10 + angle * 3) * 0.5 + 0.5;
        
        const value = Math.floor(caustic * 255);
        data[index] = value;     // R
        data[index + 1] = value; // G
        data[index + 2] = value; // B
        data[index + 3] = 255;   // A
      }
    }

    this.causticTexture = RawTexture.CreateRGBATexture(data, size, size, scene);
    if (this.causticTexture) {
      this.setTexture('causticTexture', this.causticTexture);
    }
  }

  private updateUniforms(): void {
    // 设置颜色
    this.setVector4('shallowColor', new Vector4(this.shallowColor.r, this.shallowColor.g, this.shallowColor.b, 1.0));
    this.setVector4('causticColor', new Vector4(this.causticColor.r, this.causticColor.g, this.causticColor.b, 1.0));

    // 设置焦散参数 - 与原项目完全一致
    this.setVector4('causticParams1', new Vector4(
      this.causticStrength,  // x: strength
      this.causticScale,     // y: scale
      this.causticSpeed,     // z: speed
      this.causticRGBSplit   // w: rgbSplit
    ));

    this.setVector4('causticParams2', new Vector4(0, 0, 0, this.causticDepth)); // w: depth
  }

  private startAnimation(scene: Scene): void {
    let currentTime = 0;
    scene.registerBeforeRender(() => {
      const deltaTime = scene.getEngine().getDeltaTime() / 1000;
      currentTime += deltaTime;
      this.setFloat('time', currentTime);
    });
  }

  // 公共方法用于调整参数
  public updateShallowColor(color: Color3): void {
    this.shallowColor = color;
    this.setVector4('shallowColor', new Vector4(color.r, color.g, color.b, 1.0));
  }

  public updateCausticColor(color: Color3): void {
    this.causticColor = color;
    this.setVector4('causticColor', new Vector4(color.r, color.g, color.b, 1.0));
  }

  public updateCausticStrength(strength: number): void {
    this.causticStrength = strength;
    this.updateUniforms();
  }

  public updateCausticScale(scale: number): void {
    this.causticScale = scale;
    this.updateUniforms();
  }

  public updateCausticSpeed(speed: number): void {
    this.causticSpeed = speed;
    this.updateUniforms();
  }

  public dispose(): void {
    if (this.causticTexture) {
      this.causticTexture.dispose();
    }
    super.dispose();
  }
}
