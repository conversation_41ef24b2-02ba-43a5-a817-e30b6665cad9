# crewAI多智能体Playable广告生成系统 - MVP版本

基于crewAI框架的多智能体协作Playable广告自动生成系统，通过AI智能体协作实现从需求分析到代码生成的全流程自动化。

## 🎯 项目概述

本项目是一个创新的AI驱动的Playable广告生成系统，利用crewAI框架实现多个专业AI智能体的协作，能够根据用户需求自动生成高质量的HTML5 Playable广告。

### 核心特性

- **多智能体协作**: 5个专业AI智能体分工协作
- **全流程自动化**: 从需求分析到代码生成的完整自动化流程
- **高质量输出**: 内置质量保证机制，确保生成代码的质量
- **框架支持**: 支持Phaser.js、PixiJS等主流游戏框架
- **RESTful API**: 完整的API接口，支持集成和扩展
- **实时监控**: 项目进度实时跟踪和状态监控

## 🏗️ 系统架构

### 智能体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    项目协调员 (Project Coordinator)              │
│                        统筹管理整个流程                          │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
        ▼             ▼             ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│  产品经理     │ │  技术架构师   │ │  代码生成器   │
│ 需求分析     │ │ 架构设计     │ │ 代码实现     │
└─────────────┘ └─────────────┘ └─────────────┘
                      │
                      ▼
              ┌─────────────┐
              │ 质量保证工程师 │
              │  质量控制    │
              └─────────────┘
```

### 技术栈

- **AI框架**: crewAI + OpenAI GPT-4
- **后端框架**: FastAPI + Python 3.8+
- **前端框架**: Phaser.js / PixiJS
- **数据存储**: SQLite (可扩展)
- **部署**: Docker + Uvicorn

## 🚀 快速开始

### 环境要求

- Python 3.8+
- OpenAI API密钥
- 8GB+ 内存推荐

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd mvp_implementation
```

2. **安装依赖**
```bash
cd backend
python start.py install
```

3. **配置环境**
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑.env文件，设置OpenAI API密钥
vim .env
```

必须配置的环境变量：
```env
OPENAI_API_KEY="your-openai-api-key-here"
SECRET_KEY="your-secret-key-here"
```

4. **检查环境**
```bash
python start.py check
```

5. **启动服务**
```bash
# 开发模式（热重载）
python start.py start --reload

# 生产模式
python start.py start
```

### 验证安装

启动成功后，访问以下地址验证：

- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **系统状态**: http://localhost:8000/api/system/info

## 📖 使用指南

### API接口

#### 1. 快速生成（推荐）

一键生成Playable广告：

```bash
curl -X POST "http://localhost:8000/api/generate" \
     -H "Content-Type: application/json" \
     -d '{
       "user_input": "创建一个贪吃蛇游戏，使用绿色背景，蛇身为蓝色，食物为红色圆点",
       "project_type": "standard_playable_ad"
     }'
```

#### 2. 分步骤生成

**步骤1: 创建项目**
```bash
curl -X POST "http://localhost:8000/api/projects" \
     -H "Content-Type: application/json" \
     -d '{
       "user_input": "创建一个简单的点击游戏",
       "project_type": "standard_playable_ad"
     }'
```

**步骤2: 执行生成**
```bash
curl -X POST "http://localhost:8000/api/projects/{project_id}/execute"
```

**步骤3: 获取结果**
```bash
curl "http://localhost:8000/api/projects/{project_id}/deliverables"
```

### 支持的项目类型

- `standard_playable_ad`: 标准Playable广告
- `rapid_prototype`: 快速原型

### 输入示例

```json
{
  "user_input": "创建一个贪吃蛇游戏，要求：\n1. 使用Phaser.js框架\n2. 游戏区域800x600像素\n3. 蛇身为蓝色方块\n4. 食物为红色圆点\n5. 包含得分显示\n6. 游戏结束后显示重新开始按钮",
  "project_type": "standard_playable_ad",
  "options": {
    "framework": "phaser",
    "target_size": "800x600"
  }
}
```

## 🔧 配置说明

### 环境变量配置

| 变量名 | 必填 | 默认值 | 说明 |
|--------|------|--------|------|
| `OPENAI_API_KEY` | ✅ | - | OpenAI API密钥 |
| `SECRET_KEY` | ✅ | - | 应用密钥 |
| `OPENAI_MODEL` | ❌ | gpt-4 | OpenAI模型 |
| `HOST` | ❌ | 0.0.0.0 | 服务器地址 |
| `PORT` | ❌ | 8000 | 服务器端口 |
| `DEBUG` | ❌ | false | 调试模式 |

### 智能体配置

可以在 `app/config.py` 中调整智能体的行为参数：

```python
AGENT_CONFIGS = {
    "product_manager": {
        "max_iter": 5,
        "memory": True,
        "verbose": True
    },
    # ... 其他智能体配置
}
```

## 📊 监控和日志

### 日志查看

```bash
# 查看实时日志
tail -f app.log

# 通过API查看日志
curl "http://localhost:8000/api/debug/logs?lines=100"
```

### 项目状态监控

```bash
# 查看所有项目
curl "http://localhost:8000/api/projects"

# 查看特定项目状态
curl "http://localhost:8000/api/projects/{project_id}/status"
```

### 系统健康检查

```bash
curl "http://localhost:8000/health"
```

## 🧪 测试

### 运行测试套件

```bash
# 运行所有测试
python start.py test

# 运行特定测试
pytest tests/test_agents.py -v

# 生成覆盖率报告
pytest --cov=app --cov-report=html
```

### 代码质量检查

```bash
# 代码格式化
python start.py format

# 代码质量检查
python start.py lint
```

## 📁 项目结构

```
mvp_implementation/
├── backend/
│   ├── app/
│   │   ├── agents/              # AI智能体实现
│   │   │   ├── product_manager.py
│   │   │   ├── tech_architect.py
│   │   │   ├── code_generator.py
│   │   │   ├── quality_assurance.py
│   │   │   └── project_coordinator.py
│   │   ├── main.py              # FastAPI应用主文件
│   │   └── config.py            # 配置管理
│   ├── tests/                   # 测试文件
│   ├── templates/               # 代码模板
│   ├── static/                  # 静态文件
│   ├── requirements.txt         # Python依赖
│   ├── start.py                 # 启动脚本
│   ├── .env.example             # 环境变量模板
│   └── README.md                # 项目文档
├── frontend/                    # 前端界面（可选）
└── docs/                        # 详细文档
```

## 🎮 生成示例

### 贪吃蛇游戏示例

**输入需求**:
```
创建一个贪吃蛇游戏，使用Phaser.js框架，游戏区域800x600像素，
蛇身为蓝色方块，食物为红色圆点，包含得分显示和游戏结束重新开始功能。
```

**生成输出**:
- `index.html` - 游戏主页面
- `game.js` - 游戏逻辑代码
- `style.css` - 样式文件
- 质量评估报告
- 使用说明文档

### 点击游戏示例

**输入需求**:
```
创建一个简单的点击游戏，屏幕上随机出现目标，
玩家点击目标得分，包含倒计时和最终得分显示。
```

**生成特性**:
- 响应式设计
- 移动端适配
- 音效支持
- 动画效果

## 🔍 故障排除

### 常见问题

**1. OpenAI API调用失败**
```
错误: OpenAI API key not found
解决: 检查.env文件中的OPENAI_API_KEY配置
```

**2. 依赖包安装失败**
```
错误: pip install失败
解决: 升级pip版本，使用虚拟环境
```

**3. 端口占用**
```
错误: Port 8000 already in use
解决: 修改.env中的PORT配置或停止占用进程
```

**4. 内存不足**
```
错误: Out of memory
解决: 减少并发项目数量，增加系统内存
```

### 调试模式

启用调试模式获取详细日志：

```bash
# 设置环境变量
export DEBUG=true

# 启动服务
python start.py start --reload
```

### 日志级别

在`.env`文件中调整日志级别：

```env
LOG_LEVEL="DEBUG"  # DEBUG, INFO, WARNING, ERROR
```

## 🚀 部署指南

### Docker部署

```bash
# 构建镜像
docker build -t crewai-playable-ads .

# 运行容器
docker run -p 8000:8000 \
  -e OPENAI_API_KEY="your-key" \
  -e SECRET_KEY="your-secret" \
  crewai-playable-ads
```

### 生产环境配置

```env
ENVIRONMENT="production"
DEBUG=false
WORKERS=4
LOG_LEVEL="INFO"
```

## 📈 性能优化

### 系统配置

- **并发项目数**: 根据服务器性能调整`MAX_CONCURRENT_PROJECTS`
- **超时设置**: 调整`PROJECT_TIMEOUT`避免长时间等待
- **缓存策略**: 启用Redis缓存提升响应速度

### 监控指标

- 项目生成成功率
- 平均生成时间
- 系统资源使用率
- API响应时间

## 🤝 贡献指南

### 开发环境设置

```bash
# 克隆项目
git clone <repository-url>
cd mvp_implementation/backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装开发依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt

# 安装pre-commit钩子
pre-commit install
```

### 代码规范

- 使用Black进行代码格式化
- 使用isort排序导入语句
- 使用flake8进行代码检查
- 编写单元测试覆盖新功能

### 提交流程

1. Fork项目
2. 创建功能分支
3. 编写代码和测试
4. 运行测试套件
5. 提交Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 📞 支持

- **文档**: [项目Wiki](wiki-url)
- **问题反馈**: [GitHub Issues](issues-url)
- **讨论**: [GitHub Discussions](discussions-url)

## 🗺️ 路线图

### v1.1 计划功能

- [ ] 支持更多游戏框架（Three.js、Babylon.js）
- [ ] 可视化项目管理界面
- [ ] 批量生成功能
- [ ] 模板市场

### v1.2 计划功能

- [ ] 多语言支持
- [ ] 云端部署支持
- [ ] 高级自定义选项
- [ ] 性能分析工具

---

**crewAI多智能体Playable广告生成系统** - 让AI为您的创意赋能！ 🚀