#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
crewAI多智能体Playable广告生成系统 - 启动脚本
提供一键启动、测试、部署等功能
"""

import os
import sys
import argparse
import subprocess
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
backend_path = project_root / 'backend'
sys.path.insert(0, str(backend_path))

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        sys.exit(1)
    print(f"✅ Python版本检查通过: {sys.version.split()[0]}")

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'crewai',
        'openai',
        'jinja2',
        'pydantic',
        'python-dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包检查通过")
    return True

def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    env_file = project_root / 'backend' / '.env'
    if not env_file.exists():
        print("⚠️  .env文件不存在，将使用默认配置")
        print("建议复制.env.example并配置相关参数")
        return True
    
    # 检查关键环境变量
    from dotenv import load_dotenv
    load_dotenv(env_file)
    
    critical_vars = ['OPENAI_API_KEY']
    missing_vars = []
    
    for var in critical_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  缺少关键环境变量: {', '.join(missing_vars)}")
        print("系统将使用默认配置运行，但某些功能可能受限")
    else:
        print("✅ 环境配置检查通过")
    
    return True

def setup_directories():
    """设置必要的目录"""
    print("📁 设置项目目录...")
    
    directories = [
        project_root / 'backend' / 'logs',
        project_root / 'backend' / 'output',
        project_root / 'backend' / 'temp',
        project_root / 'backend' / 'data'
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        print(f"  ✅ {directory.relative_to(project_root)}")
    
    print("✅ 目录设置完成")

def install_dependencies():
    """安装依赖包"""
    print("📦 安装依赖包...")
    
    requirements_file = project_root / 'backend' / 'requirements.txt'
    if not requirements_file.exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    try:
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)
        ], check=True)
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def start_server(host='127.0.0.1', port=8000, reload=True, workers=1):
    """启动服务器"""
    print(f"🚀 启动服务器 http://{host}:{port}")
    
    # 切换到backend目录
    os.chdir(backend_path)
    
    cmd = [
        sys.executable, '-m', 'uvicorn',
        'main:app',
        '--host', host,
        '--port', str(port)
    ]
    
    if reload:
        cmd.append('--reload')
    
    if workers > 1:
        cmd.extend(['--workers', str(workers)])
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")

def run_tests():
    """运行测试"""
    print("🧪 运行测试...")
    
    os.chdir(backend_path)
    
    try:
        # 运行基础测试
        result = subprocess.run([
            sys.executable, '-m', 'pytest', 'test_basic.py', '-v'
        ], capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ 所有测试通过")
        else:
            print("❌ 部分测试失败")
        
        return result.returncode == 0
    
    except Exception as e:
        print(f"❌ 运行测试失败: {e}")
        return False

def run_quick_test():
    """运行快速测试"""
    print("⚡ 运行快速测试...")
    
    try:
        # 导入并测试核心模块
        sys.path.insert(0, str(backend_path))
        
        print("  测试配置模块...")
        from config import Config
        config = Config
        print(f"    ✅ 配置加载成功")
        
        print("  测试日志模块...")
        from utils.logger import get_logger
        logger = get_logger('test')
        logger.info("测试日志")
        print(f"    ✅ 日志模块正常")
        
        print("  测试模板渲染器...")
        from utils.template_renderer import template_renderer
        templates = template_renderer.get_available_templates()
        print(f"    ✅ 模板渲染器正常，可用模板: {len(templates)}")
        
        print("✅ 快速测试通过")
        return True
    
    except Exception as e:
        print(f"❌ 快速测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_system_info():
    """显示系统信息"""
    print("📊 系统信息:")
    print(f"  Python版本: {sys.version.split()[0]}")
    print(f"  项目路径: {project_root}")
    print(f"  后端路径: {backend_path}")
    
    # 检查环境变量
    env_file = backend_path / '.env'
    if env_file.exists():
        print(f"  环境配置: {env_file} (存在)")
    else:
        print(f"  环境配置: .env (不存在)")
    
    # 检查关键目录
    directories = ['logs', 'output', 'templates', 'temp']
    for dir_name in directories:
        dir_path = backend_path / dir_name
        status = "存在" if dir_path.exists() else "不存在"
        print(f"  {dir_name}目录: {status}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='crewAI多智能体Playable广告生成系统',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python run.py                    # 启动开发服务器
  python run.py --install          # 安装依赖包
  python run.py --test             # 运行测试
  python run.py --quick-test       # 运行快速测试
  python run.py --info             # 显示系统信息
  python run.py --host 0.0.0.0     # 启动服务器并绑定所有接口
  python run.py --port 8080        # 使用指定端口
  python run.py --no-reload        # 禁用自动重载
        """
    )
    
    parser.add_argument('--install', action='store_true', help='安装依赖包')
    parser.add_argument('--test', action='store_true', help='运行测试')
    parser.add_argument('--quick-test', action='store_true', help='运行快速测试')
    parser.add_argument('--info', action='store_true', help='显示系统信息')
    parser.add_argument('--host', default='127.0.0.1', help='服务器主机地址 (默认: 127.0.0.1)')
    parser.add_argument('--port', type=int, default=8000, help='服务器端口 (默认: 8000)')
    parser.add_argument('--no-reload', action='store_true', help='禁用自动重载')
    parser.add_argument('--workers', type=int, default=1, help='工作进程数 (默认: 1)')
    parser.add_argument('--skip-checks', action='store_true', help='跳过环境检查')
    
    args = parser.parse_args()
    
    print("🎮 crewAI多智能体Playable广告生成系统")
    print("=" * 50)
    
    # 显示系统信息
    if args.info:
        show_system_info()
        return
    
    # 基础检查
    if not args.skip_checks:
        check_python_version()
        setup_directories()
    
    # 安装依赖
    if args.install:
        if install_dependencies():
            print("\n✅ 依赖包安装完成，现在可以启动服务器了")
        return
    
    # 运行测试
    if args.test:
        if not args.skip_checks:
            if not check_dependencies():
                print("请先安装依赖包: python run.py --install")
                return
        run_tests()
        return
    
    # 运行快速测试
    if args.quick_test:
        if run_quick_test():
            print("\n✅ 系统就绪，可以启动服务器")
        else:
            print("\n❌ 系统检查失败，请检查配置")
        return
    
    # 启动服务器
    if not args.skip_checks:
        if not check_dependencies():
            print("\n❌ 依赖包检查失败")
            print("请运行: python run.py --install")
            return
        
        check_environment()
    
    # 启动服务器
    start_server(
        host=args.host,
        port=args.port,
        reload=not args.no_reload,
        workers=args.workers
    )

if __name__ == '__main__':
    main()