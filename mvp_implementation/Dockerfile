# crewAI多智能体Playable广告生成系统 - Docker配置
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app/backend
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY backend/requirements.txt /app/requirements.txt

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 复制项目文件
COPY backend/ /app/backend/
COPY run.py /app/

# 创建必要的目录
RUN mkdir -p /app/backend/logs \
    /app/backend/output \
    /app/backend/temp \
    /app/backend/data

# 设置权限
RUN chmod +x /app/run.py

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "/app/run.py", "--host", "0.0.0.0", "--port", "8000", "--skip-checks"]