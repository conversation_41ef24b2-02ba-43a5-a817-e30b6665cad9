version: '3.8'

services:
  # 主应用服务
  crewai-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: crewai-playable-ad-generator
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app/backend
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
      - HOST=0.0.0.0
      - PORT=8000
    volumes:
      - ./backend/logs:/app/backend/logs
      - ./backend/output:/app/backend/output
      - ./backend/data:/app/backend/data
      - ./backend/.env:/app/backend/.env:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - crewai-network

  # Redis缓存服务 (可选)
  redis:
    image: redis:7-alpine
    container_name: crewai-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes
    networks:
      - crewai-network
    profiles:
      - with-redis

  # PostgreSQL数据库服务 (可选)
  postgres:
    image: postgres:15-alpine
    container_name: crewai-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=crewai_db
      - POSTGRES_USER=crewai_user
      - POSTGRES_PASSWORD=crewai_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/sql:/docker-entrypoint-initdb.d:ro
    restart: unless-stopped
    networks:
      - crewai-network
    profiles:
      - with-database

  # Nginx反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: crewai-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./backend/output:/var/www/static:ro
    depends_on:
      - crewai-app
    restart: unless-stopped
    networks:
      - crewai-network
    profiles:
      - with-nginx

  # 监控服务 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: crewai-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - crewai-network
    profiles:
      - with-monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: crewai-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning:ro
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - crewai-network
    profiles:
      - with-monitoring

networks:
  crewai-network:
    driver: bridge

volumes:
  redis_data:
    driver: local
  postgres_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local