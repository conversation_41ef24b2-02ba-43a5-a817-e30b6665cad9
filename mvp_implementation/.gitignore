# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
PIPFILE.lock

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# VS Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# 项目特定文件
# 日志文件
backend/logs/
*.log
*.log.*

# 输出文件
backend/output/
backend/temp/
backend/data/cache/

# 配置文件
backend/.env
backend/.env.local
backend/.env.production
backend/.env.development

# 生成的文件
*.zip
*.tar.gz
*.html
*.js.map

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 缓存文件
.cache/
cache/
*.cache

# 临时文件
tmp/
temp/
*.tmp
*.temp

# 备份文件
*.bak
*.backup
*.old

# 密钥和证书
*.key
*.pem
*.crt
*.p12
*.pfx
secrets/

# Docker
.dockerignore

# Node.js (如果有前端)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# 监控和分析
.nyc_output
coverage/

# 性能分析
*.prof
*.pstats

# 测试文件
test_output/
test_results/
.coverage
.pytest_cache/

# 文档生成
docs/_build/
docs/build/

# 部署相关
deploy/
.deploy/
kubernetes/secrets/

# 本地开发
local/
.local/
dev/
.dev/

# AI模型文件
*.model
*.pkl
*.joblib
models/
checkpoints/

# 大文件
*.zip
*.tar
*.gz
*.rar
*.7z

# 系统文件
.system/
system/

# 用户上传文件
uploads/
user_data/

# 第三方库
vendor/
third_party/