# crewAI多智能体Playable广告生成系统 - 环境变量配置
# 复制此文件为 .env 并填入实际值

# 基础配置
APP_NAME="crewAI Playable广告生成系统"
APP_VERSION="1.0.0"
DEBUG=false
ENVIRONMENT="development"

# 服务器配置
HOST="0.0.0.0"
PORT=8000
WORKERS=1

# OpenAI配置（必填）
OPENAI_API_KEY="your-openai-api-key-here"
OPENAI_API_BASE="https://api.openai.com/v1"
OPENAI_MODEL="gpt-4"
OPENAI_TEMPERATURE=0.7
OPENAI_MAX_TOKENS=4000

# crewAI配置
CREW_VERBOSE=true
CREW_MEMORY=true
CREW_MAX_ITER=5
CREW_MAX_EXECUTION_TIME=300

# 文件存储配置
STATIC_DIR="static"
UPLOAD_DIR="uploads"
OUTPUT_DIR="outputs"
MAX_FILE_SIZE=10485760

# 日志配置
LOG_LEVEL="INFO"
LOG_FILE="app.log"
LOG_ROTATION="1 day"
LOG_RETENTION="30 days"

# 数据库配置（可选）
DATABASE_URL="sqlite:///./app.db"
DATABASE_ECHO=false

# Redis配置（可选）
REDIS_URL="redis://localhost:6379/0"
REDIS_PASSWORD=""

# 安全配置（必填）
SECRET_KEY="your-secret-key-here-please-change-this"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS配置
CORS_ORIGINS="*"
CORS_METHODS="*"
CORS_HEADERS="*"

# 项目配置
MAX_CONCURRENT_PROJECTS=5
PROJECT_TIMEOUT=600
CLEANUP_INTERVAL=3600

# 代码生成配置
CODE_TEMPLATES_DIR="templates"
SUPPORTED_FRAMEWORKS="phaser,pixijs,threejs"
DEFAULT_FRAMEWORK="phaser"

# 质量控制配置
QUALITY_THRESHOLD=0.8
ENABLE_CODE_REVIEW=true
ENABLE_TESTING=true

# 监控配置
ENABLE_METRICS=true
METRICS_INTERVAL=60

# API配置
API_PREFIX="/api"
DOCS_URL="/docs"
REDOC_URL="/redoc"