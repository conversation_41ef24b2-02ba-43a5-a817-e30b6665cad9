#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
crewAI多智能体Playable广告生成系统 - 基础测试
MVP版本 - 基本功能测试
"""

import pytest
import asyncio
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

# 导入应用
from app.main import app
from app.config import settings, validate_config

# 创建测试客户端
client = TestClient(app)

class TestBasicAPI:
    """基础API测试"""
    
    def test_root_endpoint(self):
        """测试根路径"""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
        assert "status" in data
        assert data["status"] == "running"
    
    def test_health_check(self):
        """测试健康检查"""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert "system" in data
    
    def test_system_info(self):
        """测试系统信息"""
        response = client.get("/api/system/info")
        assert response.status_code == 200
        data = response.json()
        assert "system" in data
        assert "agents" in data
        assert "projects" in data
        assert "capabilities" in data
    
    def test_api_docs(self):
        """测试API文档"""
        response = client.get("/docs")
        assert response.status_code == 200
    
    def test_openapi_schema(self):
        """测试OpenAPI模式"""
        response = client.get("/openapi.json")
        assert response.status_code == 200
        data = response.json()
        assert "openapi" in data
        assert "info" in data
        assert "paths" in data

class TestConfiguration:
    """配置测试"""
    
    def test_settings_loading(self):
        """测试配置加载"""
        assert settings.app_name
        assert settings.app_version
        assert settings.host
        assert settings.port > 0
    
    def test_agent_configs(self):
        """测试智能体配置"""
        from app.config import AGENT_CONFIGS
        
        required_agents = [
            "product_manager",
            "tech_architect", 
            "code_generator",
            "project_coordinator"
        ]
        
        for agent in required_agents:
            assert agent in AGENT_CONFIGS
            config = AGENT_CONFIGS[agent]
            assert "role" in config
            assert "goal" in config
            assert "backstory" in config
    
    def test_task_configs(self):
        """测试任务配置"""
        from app.config import TASK_CONFIGS
        
        required_tasks = [
            "requirement_analysis",
            "architecture_design",
            "code_generation",
            "project_coordination"
        ]
        
        for task in required_tasks:
            assert task in TASK_CONFIGS
            config = TASK_CONFIGS[task]
            assert "name" in config
            assert "description" in config
            assert "expected_output" in config
    
    def test_template_configs(self):
        """测试模板配置"""
        from app.config import CODE_TEMPLATES
        
        assert "phaser" in CODE_TEMPLATES
        phaser_config = CODE_TEMPLATES["phaser"]
        assert "name" in phaser_config
        assert "version" in phaser_config
        assert "template_files" in phaser_config
        assert "cdn_links" in phaser_config

class TestProjectAPI:
    """项目API测试"""
    
    @patch('app.main.coordinator')
    def test_create_project_without_coordinator(self, mock_coordinator):
        """测试无协调器时创建项目"""
        mock_coordinator = None
        
        response = client.post("/api/projects", json={
            "user_input": "创建一个简单的游戏",
            "project_type": "standard_playable_ad"
        })
        
        # 应该返回503错误（服务不可用）
        assert response.status_code == 503
    
    def test_create_project_invalid_input(self):
        """测试无效输入创建项目"""
        response = client.post("/api/projects", json={})
        
        # 应该返回422错误（验证错误）
        assert response.status_code == 422
    
    def test_get_nonexistent_project(self):
        """测试获取不存在的项目"""
        response = client.get("/api/projects/nonexistent-id/status")
        
        # 应该返回404或503错误
        assert response.status_code in [404, 503]
    
    def test_list_projects_without_coordinator(self):
        """测试无协调器时列出项目"""
        response = client.get("/api/projects")
        
        # 应该返回503错误（服务不可用）
        assert response.status_code == 503

class TestQuickGenerate:
    """快速生成测试"""
    
    def test_quick_generate_invalid_input(self):
        """测试无效输入的快速生成"""
        response = client.post("/api/generate", json={})
        
        # 应该返回422错误（验证错误）
        assert response.status_code == 422
    
    def test_quick_generate_without_coordinator(self):
        """测试无协调器时快速生成"""
        response = client.post("/api/generate", json={
            "user_input": "创建一个简单的游戏",
            "project_type": "standard_playable_ad"
        })
        
        # 应该返回503错误（服务不可用）
        assert response.status_code == 503

class TestErrorHandling:
    """错误处理测试"""
    
    def test_404_error(self):
        """测试404错误"""
        response = client.get("/nonexistent-endpoint")
        assert response.status_code == 404
    
    def test_method_not_allowed(self):
        """测试方法不允许错误"""
        response = client.put("/")
        assert response.status_code == 405
    
    def test_invalid_json(self):
        """测试无效JSON"""
        response = client.post(
            "/api/projects",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == 422

class TestUtilityFunctions:
    """工具函数测试"""
    
    def test_get_agent_config(self):
        """测试获取智能体配置"""
        from app.config import get_agent_config
        
        config = get_agent_config("product_manager")
        assert config is not None
        assert "role" in config
        
        # 测试不存在的智能体
        config = get_agent_config("nonexistent_agent")
        assert config == {}
    
    def test_get_task_config(self):
        """测试获取任务配置"""
        from app.config import get_task_config
        
        config = get_task_config("requirement_analysis")
        assert config is not None
        assert "name" in config
        
        # 测试不存在的任务
        config = get_task_config("nonexistent_task")
        assert config == {}
    
    def test_get_template_config(self):
        """测试获取模板配置"""
        from app.config import get_template_config
        
        config = get_template_config("phaser")
        assert config is not None
        assert "name" in config
        
        # 测试不存在的框架（应该返回默认框架）
        config = get_template_config("nonexistent_framework")
        assert config is not None  # 应该返回默认框架配置
    
    def test_get_quality_config(self):
        """测试获取质量配置"""
        from app.config import get_quality_config
        
        config = get_quality_config()
        assert config is not None
        assert "code_quality" in config
        assert "functionality" in config
        assert "overall" in config

class TestDataValidation:
    """数据验证测试"""
    
    def test_project_create_request_validation(self):
        """测试项目创建请求验证"""
        from app.main import ProjectCreateRequest
        
        # 有效请求
        valid_request = ProjectCreateRequest(
            user_input="创建一个游戏",
            project_type="standard_playable_ad"
        )
        assert valid_request.user_input == "创建一个游戏"
        assert valid_request.project_type == "standard_playable_ad"
        
        # 测试默认值
        minimal_request = ProjectCreateRequest(user_input="测试")
        assert minimal_request.project_type == "standard_playable_ad"
        assert minimal_request.options == {}
    
    def test_project_status_response_validation(self):
        """测试项目状态响应验证"""
        from app.main import ProjectStatusResponse
        
        response = ProjectStatusResponse(
            project_id="test-id",
            status="running",
            progress=0.5,
            current_phase="code_generation",
            estimated_completion=None,
            issues=[],
            next_actions=["完成代码生成"]
        )
        
        assert response.project_id == "test-id"
        assert response.status == "running"
        assert response.progress == 0.5
        assert response.current_phase == "code_generation"
        assert response.estimated_completion is None
        assert response.issues == []
        assert response.next_actions == ["完成代码生成"]
    
    def test_generation_result_validation(self):
        """测试生成结果验证"""
        from app.main import GenerationResult
        
        result = GenerationResult(
            success=True,
            project_id="test-id",
            deliverables={"code": "test code"},
            quality_metrics={"overall": 0.9},
            recommendations=["建议1"],
            execution_time=120.5
        )
        
        assert result.success is True
        assert result.project_id == "test-id"
        assert result.deliverables == {"code": "test code"}
        assert result.quality_metrics == {"overall": 0.9}
        assert result.recommendations == ["建议1"]
        assert result.execution_time == 120.5

# 测试夹具
@pytest.fixture
def mock_coordinator():
    """模拟协调器"""
    coordinator = Mock()
    coordinator.create_project.return_value = "test-project-id"
    coordinator.execute_project.return_value = Mock(
        success=True,
        deliverables={"code": "test code"},
        quality_metrics={"overall": 0.9},
        recommendations=["测试建议"]
    )
    coordinator.get_project_status.return_value = Mock(
        status="completed",
        progress_percentage=1.0,
        current_phase="finished",
        estimated_completion=None,
        issues=[],
        next_actions=[]
    )
    coordinator.list_projects.return_value = []
    coordinator.cancel_project.return_value = True
    return coordinator

@pytest.fixture
def sample_project_request():
    """示例项目请求"""
    return {
        "user_input": "创建一个贪吃蛇游戏，使用Phaser.js框架",
        "project_type": "standard_playable_ad",
        "options": {
            "framework": "phaser",
            "target_size": "800x600"
        }
    }

# 运行测试的主函数
if __name__ == "__main__":
    pytest.main(["-v", __file__])