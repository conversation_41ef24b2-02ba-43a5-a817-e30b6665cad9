#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品经理智能体 - MVP版本
负责分析用户需求并生成Playable广告的产品规划
"""

import json
import logging
from typing import Dict, Any, Optional
from crewai import Agent, Task, Crew, LLM
from pydantic import BaseModel, Field

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProductRequirement(BaseModel):
    """产品需求数据模型"""
    target_audience: str = Field(description="目标用户群体")
    game_type: str = Field(description="游戏类型")
    core_mechanics: list = Field(description="核心游戏机制")
    visual_style: str = Field(description="视觉风格")
    interactions: list = Field(description="交互方式")
    animations: list = Field(description="动画效果")
    ui_elements: list = Field(description="UI元素")
    success_metrics: dict = Field(description="成功指标")
    technical_constraints: dict = Field(description="技术约束")
    estimated_complexity: str = Field(description="预估复杂度")

class ProductManagerAgent:
    """产品经理智能体"""
    
    def __init__(self, llm_model: str = "gpt-4.1", temperature: float = 0.1):
        """
        初始化产品经理智能体
        
        Args:
            llm_model: 使用的语言模型
            temperature: 模型温度参数
        """
        # 🔍 优先使用配置文件中的设置
        from ..config import settings
        actual_model = settings.openai_model
        actual_temperature = settings.openai_temperature
        
        self.llm_model = actual_model if actual_model else llm_model
        self.temperature = actual_temperature if actual_temperature is not None else temperature
        
        logger.info(f"🔍 [ProductManager] 使用模型: {self.llm_model}, 温度: {self.temperature}")
        self._initialize_agent()
        
    def _initialize_agent(self):
        """初始化crewAI智能体"""
        try:
            # 创建LLM实例 - 使用CrewAI的LLM类
            custom_llm = LLM(
                model=self.llm_model,
                temperature=self.temperature,
                max_tokens=4000,
                timeout=300
            )
            
            self.agent = Agent(
                role='Playable广告产品经理',
                goal='分析用户需求并制定专业的Playable广告产品规划',
                backstory="""
                你是一位拥有5年以上经验的Playable广告产品经理，专门负责移动游戏广告的产品设计。
                你深度理解用户心理、游戏机制设计、以及移动端交互体验。
                你的任务是将用户的模糊需求转化为清晰、可执行的产品规划文档。
                """,
                llm=custom_llm,
                verbose=True,
                allow_delegation=False,
                max_iter=3
            )
            logger.info("产品经理智能体初始化成功")
        except Exception as e:
            logger.error(f"智能体初始化失败: {e}")
            raise
    
    def analyze_requirements(self, user_input: str, context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        分析用户需求并生成产品规划
        
        Args:
            user_input: 用户输入的需求描述
            context: 额外的上下文信息
            
        Returns:
            结构化的产品需求文档
        """
        try:
            logger.info(f"开始分析用户需求: {user_input[:100]}...")
            
            # 构建任务描述
            task_description = self._build_task_description(user_input, context)
            
            # 创建分析任务
            analysis_task = Task(
                description=task_description,
                agent=self.agent,
                expected_output="结构化的JSON格式产品需求文档"
            )
            
            # 执行任务
            crew = Crew(
                agents=[self.agent],
                tasks=[analysis_task],
                verbose=True
            )
            
            result = crew.kickoff()
            
            # 解析和验证结果
            parsed_result = self._parse_and_validate_result(result)
            
            logger.info("需求分析完成")
            return parsed_result
            
        except Exception as e:
            logger.error(f"需求分析失败: {e}")
            return self._get_fallback_requirements(user_input)
    
    def _build_task_description(self, user_input: str, context: Optional[Dict] = None) -> str:
        """构建任务描述"""
        base_description = f"""
        请分析以下用户需求并生成详细的Playable广告产品规划：
        
        用户需求描述：
        {user_input}
        
        请按照以下结构生成产品规划（必须是有效的JSON格式）：
        
        {{
            "target_audience": "目标用户群体（如：休闲游戏玩家、策略游戏爱好者等）",
            "game_type": "游戏类型（如：点击收集、拼图、跑酷、策略等）",
            "core_mechanics": ["核心游戏机制列表"],
            "visual_style": "视觉风格（如：卡通、写实、像素、简约等）",
            "interactions": ["交互方式列表（如：点击、拖拽、滑动等）"],
            "animations": ["动画效果列表"],
            "ui_elements": ["UI元素列表"],
            "success_metrics": {{
                "primary_goal": "主要目标",
                "engagement_time": "预期参与时长（秒）",
                "conversion_target": "转化目标"
            }},
            "technical_constraints": {{
                "file_size_limit": "文件大小限制（KB）",
                "loading_time": "加载时间要求（秒）",
                "device_compatibility": "设备兼容性要求"
            }},
            "estimated_complexity": "复杂度评估（简单/中等/复杂）"
        }}
        
        要求：
        1. 深入理解用户需求的核心意图
        2. 考虑Playable广告的特点（短时间吸引、易上手、强转化）
        3. 确保技术实现的可行性
        4. 平衡用户体验和商业目标
        5. 输出必须是有效的JSON格式
        """
        
        # 添加上下文信息
        if context:
            base_description += f"\n\n额外上下文信息：\n{json.dumps(context, ensure_ascii=False, indent=2)}"
        
        return base_description
    
    def _parse_and_validate_result(self, result) -> Dict[str, Any]:
        """解析和验证智能体输出结果"""
        try:
            # 处理CrewOutput对象
            if hasattr(result, 'raw'):
                result_text = result.raw
            elif isinstance(result, str):
                result_text = result
            else:
                result_text = str(result)
            
            # 移除可能的markdown标记
            cleaned_result = result_text.strip()
            if cleaned_result.startswith('```json'):
                cleaned_result = cleaned_result[7:]
            if cleaned_result.endswith('```'):
                cleaned_result = cleaned_result[:-3]
            
            parsed = json.loads(cleaned_result)
            
            # 验证必要字段
            required_fields = [
                'target_audience', 'game_type', 'core_mechanics', 
                'visual_style', 'interactions', 'animations', 
                'ui_elements', 'success_metrics', 'technical_constraints',
                'estimated_complexity'
            ]
            
            for field in required_fields:
                if field not in parsed:
                    logger.warning(f"缺少必要字段: {field}，使用默认值")
                    parsed[field] = self._get_default_value(field)
            
            # 使用Pydantic验证
            validated = ProductRequirement(**parsed)
            return validated.dict()
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return self._extract_structured_info(result)
        except Exception as e:
            logger.error(f"结果验证失败: {e}")
            return self._get_fallback_requirements("解析失败")
    
    def _extract_structured_info(self, text: str) -> Dict[str, Any]:
        """从非结构化文本中提取结构化信息"""
        logger.info("尝试从文本中提取结构化信息")
        
        # 基础信息提取逻辑
        extracted = {
            "target_audience": "游戏玩家",
            "game_type": "休闲游戏",
            "core_mechanics": ["点击交互"],
            "visual_style": "简约现代",
            "interactions": ["点击", "触摸"],
            "animations": ["基础动画"],
            "ui_elements": ["按钮", "分数显示"],
            "success_metrics": {
                "primary_goal": "用户参与",
                "engagement_time": "30",
                "conversion_target": "应用下载"
            },
            "technical_constraints": {
                "file_size_limit": "2048",
                "loading_time": "3",
                "device_compatibility": "移动设备"
            },
            "estimated_complexity": "简单"
        }
        
        # 简单的关键词匹配提取
        text_lower = text.lower()
        
        # 游戏类型识别
        game_types = {
            "拼图": "拼图游戏",
            "跑酷": "跑酷游戏", 
            "收集": "收集游戏",
            "射击": "射击游戏",
            "策略": "策略游戏",
            "rpg": "角色扮演游戏",
            "卡牌": "卡牌游戏"
        }
        
        for keyword, game_type in game_types.items():
            if keyword in text_lower:
                extracted["game_type"] = game_type
                break
        
        # 交互方式识别
        interactions = []
        interaction_keywords = {
            "点击": "点击",
            "拖拽": "拖拽", 
            "滑动": "滑动",
            "长按": "长按",
            "双击": "双击"
        }
        
        for keyword, interaction in interaction_keywords.items():
            if keyword in text_lower:
                interactions.append(interaction)
        
        if interactions:
            extracted["interactions"] = interactions
        
        return extracted
    
    def _get_default_value(self, field: str) -> Any:
        """获取字段的默认值"""
        defaults = {
            'target_audience': '休闲游戏玩家',
            'game_type': '休闲游戏',
            'core_mechanics': ['点击交互'],
            'visual_style': '简约现代',
            'interactions': ['点击', '触摸'],
            'animations': ['基础动画效果'],
            'ui_elements': ['开始按钮', '分数显示', '进度条'],
            'success_metrics': {
                'primary_goal': '用户参与',
                'engagement_time': '30',
                'conversion_target': '应用下载'
            },
            'technical_constraints': {
                'file_size_limit': '2048',
                'loading_time': '3',
                'device_compatibility': '移动设备'
            },
            'estimated_complexity': '简单'
        }
        return defaults.get(field, "未知")
    
    def _get_fallback_requirements(self, user_input: str) -> Dict[str, Any]:
        """获取备用的产品需求（当AI分析失败时使用）"""
        logger.warning("使用备用产品需求")
        
        return {
            "target_audience": "休闲游戏玩家",
            "game_type": "简单互动游戏",
            "core_mechanics": ["点击交互", "即时反馈"],
            "visual_style": "简约现代",
            "interactions": ["点击", "触摸"],
            "animations": ["点击反馈动画", "得分动画"],
            "ui_elements": ["开始按钮", "分数显示", "重新开始按钮"],
            "success_metrics": {
                "primary_goal": "用户参与和转化",
                "engagement_time": "30",
                "conversion_target": "应用下载"
            },
            "technical_constraints": {
                "file_size_limit": "2048",
                "loading_time": "3",
                "device_compatibility": "iOS和Android移动设备"
            },
            "estimated_complexity": "简单",
            "fallback_reason": "AI分析失败，使用默认配置",
            "original_input": user_input[:200]  # 保留原始输入的前200字符
        }
    
    def validate_requirements(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """验证和优化产品需求"""
        try:
            # 使用Pydantic进行验证
            validated = ProductRequirement(**requirements)
            
            # 添加额外的业务逻辑验证
            result = validated.dict()
            
            # 复杂度评估
            complexity_score = self._calculate_complexity_score(result)
            if complexity_score > 8:
                result["estimated_complexity"] = "复杂"
                result["complexity_warning"] = "当前需求较为复杂，建议简化部分功能"
            elif complexity_score > 5:
                result["estimated_complexity"] = "中等"
            else:
                result["estimated_complexity"] = "简单"
            
            # 技术可行性检查
            feasibility_issues = self._check_technical_feasibility(result)
            if feasibility_issues:
                result["feasibility_warnings"] = feasibility_issues
            
            return result
            
        except Exception as e:
            logger.error(f"需求验证失败: {e}")
            return requirements
    
    def _calculate_complexity_score(self, requirements: Dict[str, Any]) -> int:
        """计算需求复杂度评分"""
        score = 0
        
        # 核心机制数量
        score += len(requirements.get('core_mechanics', [])) * 2
        
        # 交互方式数量
        score += len(requirements.get('interactions', [])) * 1
        
        # 动画效果数量
        score += len(requirements.get('animations', [])) * 1.5
        
        # UI元素数量
        score += len(requirements.get('ui_elements', [])) * 0.5
        
        # 特殊游戏类型加分
        complex_types = ['策略游戏', '角色扮演游戏', '模拟游戏']
        if requirements.get('game_type') in complex_types:
            score += 3
        
        return int(score)
    
    def _check_technical_feasibility(self, requirements: Dict[str, Any]) -> list:
        """检查技术可行性"""
        warnings = []
        
        # 🔧 修复：安全地解析文件大小限制，处理带单位的字符串
        file_size_limit = self._parse_size_constraint(
            requirements.get('technical_constraints', {}).get('file_size_limit', 2048)
        )
        if file_size_limit < 1024:
            warnings.append("文件大小限制过小，可能影响功能实现")
        
        # 🔧 修复：安全地解析加载时间，处理带单位的字符串
        loading_time = self._parse_time_constraint(
            requirements.get('technical_constraints', {}).get('loading_time', 3)
        )
        if loading_time < 2:
            warnings.append("加载时间要求过严格，建议适当放宽")
        
        # 复杂动画检查
        animations = requirements.get('animations', [])
        complex_animations = ['3D动画', '粒子效果', '物理模拟']
        if any(anim in str(animations) for anim in complex_animations):
            warnings.append("包含复杂动画效果，需要额外的性能优化")
        
        return warnings
    
    def _parse_size_constraint(self, size_value) -> int:
        """安全地解析文件大小约束，支持带单位的字符串"""
        try:
            if isinstance(size_value, (int, float)):
                return int(size_value)
            
            if isinstance(size_value, str):
                # 移除空格并转为小写
                size_str = size_value.strip().lower()
                
                # 提取数字部分
                import re
                numbers = re.findall(r'\d+', size_str)
                if not numbers:
                    logger.warning(f"无法解析文件大小约束: {size_value}，使用默认值2048KB")
                    return 2048
                
                base_size = int(numbers[0])
                
                # 根据单位转换
                if 'mb' in size_str or 'M' in size_value:
                    return base_size * 1024  # MB转KB
                elif 'gb' in size_str or 'G' in size_value:
                    return base_size * 1024 * 1024  # GB转KB
                else:
                    # 默认为KB或无单位
                    return base_size
            
            logger.warning(f"未知的文件大小格式: {size_value}，使用默认值2048KB")
            return 2048
            
        except Exception as e:
            logger.error(f"解析文件大小约束失败: {e}，使用默认值2048KB")
            return 2048
    
    def _parse_time_constraint(self, time_value) -> int:
        """安全地解析时间约束，支持带单位的字符串"""
        try:
            if isinstance(time_value, (int, float)):
                return int(time_value)
            
            if isinstance(time_value, str):
                # 移除空格并转为小写
                time_str = time_value.strip().lower()
                
                # 提取数字部分
                import re
                numbers = re.findall(r'\d+', time_str)
                if not numbers:
                    logger.warning(f"无法解析时间约束: {time_value}，使用默认值3秒")
                    return 3
                
                base_time = int(numbers[0])
                
                # 根据单位转换
                if 'min' in time_str or '分' in time_str:
                    return base_time * 60  # 分钟转秒
                elif 'hour' in time_str or 'h' in time_str or '小时' in time_str:
                    return base_time * 3600  # 小时转秒
                else:
                    # 默认为秒
                    return base_time
            
            logger.warning(f"未知的时间格式: {time_value}，使用默认值3秒")
            return 3
            
        except Exception as e:
            logger.error(f"解析时间约束失败: {e}，使用默认值3秒")
            return 3

# 使用示例
if __name__ == "__main__":
    # 测试产品经理智能体
    pm_agent = ProductManagerAgent()
    
    test_inputs = [
        "制作一个点击收集金币的小游戏广告",
        "创建一个简单的拼图游戏，适合儿童玩家",
        "设计一个RPG风格的角色升级广告，包含战斗系统"
    ]
    
    for i, test_input in enumerate(test_inputs, 1):
        print(f"\n=== 测试用例 {i} ===")
        print(f"输入: {test_input}")
        
        try:
            result = pm_agent.analyze_requirements(test_input)
            print(f"输出: {json.dumps(result, ensure_ascii=False, indent=2)}")
        except Exception as e:
            print(f"错误: {e}")