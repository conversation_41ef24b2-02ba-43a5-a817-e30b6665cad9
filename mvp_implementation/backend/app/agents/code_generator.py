#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码生成器智能体 - 增强版本
负责技术架构设计和代码生成的全流程
"""

import json
import logging
from typing import Dict, Any, List, Optional
from crewai import Agent, Task, Crew, LLM
from pydantic import BaseModel, Field

from .game_designer import GameDesignerAgent

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GeneratedCode(BaseModel):
    """生成的代码数据模型"""
    filename: str = Field(description="文件名")
    content: str = Field(description="文件内容")
    description: str = Field(description="文件描述")

class CodeGeneratorAgent:
    """代码生成器智能体 - 增强版（包含架构设计功能）"""
    
    def __init__(self, llm_model: str = "gpt-4", temperature: float = 0.7):
        """
        初始化代码生成器智能体
        
        Args:
            llm_model: 使用的语言模型
            temperature: 模型温度参数
        """
        # 🔍 调试：检查是否从配置文件加载了正确的模型
        from ..config import settings
        actual_model = settings.openai_model
        logger.info(f"🔍 [调试] 配置文件中的模型: {actual_model}")
        logger.info(f"🔍 [调试] 传入的模型参数: {llm_model}")
        
        # 优先使用配置文件中的模型和温度
        actual_temperature = settings.openai_temperature
        self.llm_model = actual_model if actual_model else llm_model
        self.temperature = actual_temperature if actual_temperature is not None else temperature
        
        logger.info(f"🔍 [调试] 最终使用的模型: {self.llm_model}")
        logger.info(f"🔍 [调试] 最终使用的温度: {self.temperature}")
        
        # 初始化游戏设计师智能体
        self.game_designer = GameDesignerAgent(llm_model=self.llm_model, temperature=self.temperature)
        self._initialize_agent()
        
    def _initialize_agent(self):
        """初始化crewAI智能体"""
        try:
            # 🔍 调试：打印模型配置信息
            logger.info("=" * 60)
            logger.info("🔍 [调试] 智能体配置信息:")
            logger.info(f"模型名称: {self.llm_model}")
            logger.info(f"温度参数: {self.temperature}")
            logger.info("=" * 60)
            
            # 创建LLM实例 - 使用CrewAI的LLM类而不是langchain的OpenAI
            custom_llm = LLM(
                model=self.llm_model,
                temperature=self.temperature,
                max_tokens=32000,  # 大幅增加token限制
                timeout=600,       # 增加超时时间
                top_p=0.9,         # 添加nucleus sampling
                frequency_penalty=0.1,  # 减少重复
                presence_penalty=0.1    # 鼓励主题多样性
            )
            
            self.agent = Agent(
                role='Phaser.js高级游戏开发专家',
                goal='基于Phaser.js 3.70.0生成高质量、完整可运行的专业级HTML5游戏代码',
                backstory="""你是一位精通Phaser.js 3.70.0的高级游戏开发专家，专门生成基于现代Phaser.js架构的高质量HTML5游戏。

🎯 专业领域：Phaser.js 3.70.0游戏开发
🚀 核心技能：
- 精通Phaser.js 3.70.0最新API和最佳实践
- 深度理解Scene生命周期管理（preload → create → update）
- 熟练掌握Arcade Physics物理引擎配置和优化
- 精通游戏对象管理、资源加载、性能优化
- 专业的响应式设计和跨平台兼容性处理

🏗️ 架构专精：
- 场景管理：MenuScene、GameScene、GameOverScene多场景架构
- 状态管理：完整的游戏状态系统和数据持久化
- 输入系统：统一的pointer事件处理，支持鼠标/触摸/键盘
- 物理系统：Arcade Physics碰撞检测和物理属性配置
- 性能优化：对象池、批量渲染、内存管理

📋 输出标准：
- 严格遵循Phaser.js 3.70.0 API规范和最佳实践
- 输出完整的HTML文件（<!DOCTYPE html>到</html>）
- 正确引用Phaser.js 3.70.0 CDN
- 内嵌完整的CSS样式和JavaScript代码
- 实现完整的游戏生命周期和状态管理
- 包含详细的中文注释，便于理解和维护
- 代码结构清晰，支持后续扩展

💡 技术特长：
- 响应式游戏设计：Phaser.Scale.FIT自适应缩放
- 多场景切换：平滑的场景转换和状态管理
- 物理引擎：精确的碰撞检测和物理模拟
- 性能优化：Group对象池、Graphics批量渲染
- 跨平台兼容：支持桌面和移动端全平台运行

🔧 代码质量要求：
- 使用ES6+现代JavaScript语法
- 遵循Phaser.js官方代码规范
- 严格避免重复声明变量（const/let/var）
- 确保所有函数和类完整实现，括号正确配对
- 实现完整的错误处理和调试支持
- 包含性能监控和优化措施
- 代码注释清晰，逻辑结构合理

⚠️ 语法安全措施：
- 使用唯一变量名避免重复声明冲突
- 合理使用作用域隔离变量
- 确保代码完整性，无截断或遗漏
- 通过语法检查，符合JavaScript规范

🚨 重要承诺：生成的代码必须是完整、可运行、高质量的Phaser.js游戏，确保功能完整性和专业性，且完全没有语法错误。""",
                llm=custom_llm,
                verbose=True,
                allow_delegation=False,
                max_iter=2,  # 减少迭代次数，避免内容被截断
                max_execution_time=600,  # 设置最大执行时间
                max_retry_limit=1,  # 减少重试次数
                respect_context_window=True,  # 🔑 关键：自动管理上下文窗口
                memory=False,  # 禁用内存以避免上下文混乱
                step_callback=None,
                use_system_prompt=True,  # 确保使用系统提示
                inject_date=False  # 不注入日期避免干扰
            )
            logger.info(f"✅ 代码生成器智能体初始化成功 - 使用模型: {self.llm_model}")
        except Exception as e:
            logger.error(f"智能体初始化失败: {e}")
            raise
    
    def design_architecture(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据产品需求设计技术架构
        
        Args:
            requirements: 产品需求文档
            
        Returns:
            技术架构方案
        """
        try:
            logger.info("开始设计技术架构")
            
            game_type = requirements.get('game_type', '休闲游戏')
            core_mechanics = requirements.get('core_mechanics', ['点击交互'])
            
            # 基于需求生成Phaser.js专业架构
            architecture = {
                "framework": "Phaser.js 3.70.0",
                "framework_version": "3.70.0",
                "architecture_type": "Scene-based单文件架构",
                "file_structure": {
                    "index.html": "完整的Phaser.js游戏文件，包含HTML、CSS、JavaScript和场景管理"
                },
                "core_components": [
                    {
                        "name": "MenuScene",
                        "purpose": "菜单场景",
                        "responsibilities": ["游戏启动界面", "设置选项", "场景切换"],
                        "phaser_methods": ["preload", "create", "update"]
                    },
                    {
                        "name": "GameScene", 
                        "purpose": "主游戏场景",
                        "responsibilities": ["游戏逻辑", "物理模拟", "用户交互", "分数管理"],
                        "phaser_methods": ["preload", "create", "update"]
                    },
                    {
                        "name": "GameOverScene",
                        "purpose": "游戏结束场景", 
                        "responsibilities": ["结果显示", "重新开始", "返回菜单"],
                        "phaser_methods": ["preload", "create", "update"]
                    },
                    {
                        "name": "GameConfig",
                        "purpose": "Phaser配置管理",
                        "responsibilities": ["场景注册", "物理引擎", "缩放适配", "输入配置"]
                    }
                ],
                "game_objects": self._design_game_objects(game_type, core_mechanics),
                "event_system": {
                    "input_events": ["pointerdown", "pointerup", "pointermove"],
                    "game_events": ["score_change", "game_over", "level_complete"],
                    "ui_events": ["button_click", "menu_toggle"]
                },
                "performance_optimization": [
                    "单文件部署减少HTTP请求",
                    "内嵌CSS和JavaScript避免外部依赖",
                    "使用对象池管理游戏对象",
                    "优化渲染循环"
                ],
                "technical_constraints": {
                    "deployment": "单文件HTML",
                    "framework": "根据游戏需求自选框架",
                    "compatibility": "现代浏览器",
                    "performance": "移动端友好"
                },
                "implementation_strategy": {
                    "approach": "一体化开发",
                    "benefits": ["简化部署", "减少依赖", "快速加载"],
                    "trade_offs": ["文件较大", "代码耦合度高"]
                }
            }
            
            logger.info("技术架构设计完成")
            return architecture
            
        except Exception as e:
            logger.error(f"技术架构设计失败: {e}")
            raise e
    
    def _design_game_objects(self, game_type: str, core_mechanics: List[str]) -> List[Dict[str, Any]]:
        """根据游戏类型和机制设计游戏对象"""
        
        base_objects = [
            {
                "name": "Player",
                "properties": ["position", "score", "active"],
                "methods": ["move", "interact", "update"],
                "interactions": ["click", "touch"]
            }
        ]
        
        # 根据游戏类型添加特定对象
        if "点击" in game_type or "点击" in str(core_mechanics):
            base_objects.append({
                "name": "ClickableItem",
                "properties": ["position", "value", "type"],
                "methods": ["onClick", "animate", "destroy"],
                "interactions": ["click"]
            })
        
        if "收集" in game_type or "收集" in str(core_mechanics):
            base_objects.append({
                "name": "CollectibleItem",
                "properties": ["position", "points", "collected"],
                "methods": ["collect", "respawn", "animate"],
                "interactions": ["collision"]
            })
        
        if "射击" in game_type or "射击" in str(core_mechanics):
            base_objects.extend([
                {
                    "name": "Bullet",
                    "properties": ["position", "velocity", "damage"],
                    "methods": ["move", "checkCollision", "destroy"],
                    "interactions": ["collision"]
                },
                {
                    "name": "Enemy",
                    "properties": ["position", "health", "speed"],
                    "methods": ["move", "attack", "takeDamage"],
                    "interactions": ["collision", "ai"]
                }
            ])
        
        return base_objects
    
    def design_and_generate(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """
        设计架构并生成代码的一体化方法
        
        Args:
            requirements: 产品需求文档
            
        Returns:
            包含架构设计和生成代码的完整结果
        """
        try:
            logger.info("开始一体化设计和代码生成流程")
            
            # 1. 设计技术架构
            architecture = self.design_architecture(requirements)
            logger.info("✅ 技术架构设计完成")
            
            # 2. 基于架构生成代码
            code_result = self.generate_code(architecture, requirements)
            logger.info("✅ 代码生成完成")
            
            # 3. 合并结果
            combined_result = {
                **code_result,
                'architecture': architecture,
                'workflow': 'integrated_design_and_development',
                'benefits': [
                    '架构与实现高度一致',
                    '减少沟通成本',
                    '加快开发速度',
                    '降低技术风险'
                ]
            }
            
            logger.info("🎉 一体化设计和代码生成流程完成")
            return combined_result
            
        except Exception as e:
            logger.error(f"❌ 一体化流程失败: {e}")
            raise e
    
    def generate_code(self, architecture: Dict[str, Any], 
                     requirements: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成单一HTML游戏文件并直接保存到静态目录
        
        Args:
            architecture: 技术架构文档
            requirements: 产品需求文档
            
        Returns:
            生成的HTML游戏文件信息
        """
        try:
            import os
            from datetime import datetime
            
            game_type = requirements.get('game_type', '休闲游戏')
            logger.info(f"开始生成HTML游戏 - 游戏类型: {game_type}")
            
            # 构建游戏生成上下文
            context = self._build_game_context(architecture, requirements)
            
            # 生成HTML游戏内容
            html_content = self._generate_html_game(context)
            
            # 🔑 直接在execute阶段生成文件
            # 创建项目目录
            project_id = f"project_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            static_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'static')
            project_dir = os.path.join(static_dir, project_id)
            
            # 确保目录存在
            os.makedirs(project_dir, exist_ok=True)
            
            # 写入HTML文件
            html_file_path = os.path.join(project_dir, 'index.html')
            with open(html_file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"✅ 文件已保存到: {html_file_path}")
            
            result = {
                'files': [{
                    'filename': 'index.html',
                    'path': html_file_path,
                    'content': html_content,
                    'file_type': 'html',
                    'dependencies': [],
                    'description': f"完整的{game_type}单文件游戏",
                    'url': f"/static/{project_id}/index.html",
                    'size': len(html_content)
                }],
                'project_id': project_id,
                'project_dir': project_dir,
                'file_urls': [{
                    'filename': 'index.html',
                    'url': f"/static/{project_id}/index.html",
                    'size': len(html_content)
                }],
                'build_instructions': ["直接在浏览器中打开index.html即可运行"],
                'deployment_notes': ["单文件部署，无外部依赖"],
                'testing_guide': ["在不同浏览器和设备上测试"],
                'performance_notes': ["已优化为单文件加载"],
                'generation_summary': {
                    'total_files': 1,
                    'framework': 'HTML5 + JavaScript',
                    'estimated_size_kb': len(html_content) // 1024,
                    'game_type': game_type,
                    'is_single_file': True,
                    'generated_at': datetime.now().isoformat()
                }
            }
            
            logger.info(f"🎉 HTML游戏生成完成，文件大小: {len(html_content) // 1024}KB")
            logger.info(f"🎉 文件已直接保存，可通过以下URL访问: /static/{project_id}/index.html")
            return result
            
        except Exception as e:
            logger.error(f"❌ 代码生成失败: {e}")
            raise e
    
    def _build_game_context(self, architecture: Dict[str, Any], 
                           requirements: Dict[str, Any]) -> Dict[str, Any]:
        """构建Phaser.js游戏生成上下文"""
        game_type = requirements.get('game_type', '休闲游戏')
        core_mechanics = requirements.get('core_mechanics', ['点击交互'])
        
        # 构建Phaser.js专业技术约束
        technical_constraints = {
            'framework': 'Phaser.js 3.70.0',
            'framework_version': '3.70.0',
            'physics_engine': 'Arcade Physics',
            'file_type': '单文件HTML',
            'target_platform': 'Web浏览器（桌面+移动端）',
            'performance': '高性能优化',
            'scene_architecture': '多场景架构',
            'input_system': '统一pointer事件',
            'scaling': '响应式自适应',
            'asset_loading': '资源预加载和缓存',
            'object_management': '对象池和内存优化',
            'dependencies': 'Phaser.js CDN依赖'
        }
        
        # Phaser.js专业配置上下文
        phaser_config = {
            'scale_mode': 'Phaser.Scale.FIT',
            'auto_center': 'Phaser.Scale.CENTER_BOTH',
            'physics_type': 'arcade',
            'gravity_y': 300,
            'scene_structure': ['MenuScene', 'GameScene', 'GameOverScene'],
            'input_events': ['pointerdown', 'pointerup', 'pointermove'],
            'performance_features': ['object_pool', 'graphics_batching', 'asset_caching']
        }
        
        return {
            'game_type': game_type,
            'core_mechanics': core_mechanics,
            'framework': 'Phaser.js 3.70.0',
            'game_width': requirements.get('game_width', 800),
            'game_height': requirements.get('game_height', 600),
            'visual_style': requirements.get('visual_style', '现代简约'),
            'target_audience': requirements.get('target_audience', '休闲玩家'),
            'interactions': requirements.get('interactions', ['点击', '触摸', '键盘']),
            'technical_constraints': technical_constraints,
            'phaser_config': phaser_config,
            'quality_requirements': {
                'code_comments': '详细中文注释',
                'architecture_pattern': 'Scene-based架构',
                'performance_optimization': '必须包含',
                'responsive_design': '全设备适配',
                'error_handling': '完整错误处理',
                'debugging_support': '开发调试支持'
            }
        }
    
    def _generate_html_game(self, context: Dict[str, Any]) -> str:
        """生成HTML游戏内容"""
        
        # 构建游戏生成任务
        task_description = self._build_generation_task(context)
        # 创建生成任务
        generation_task = Task(
            description=task_description,
            agent=self.agent,
            expected_output="""完整的单文件HTML5游戏代码，必须满足以下要求：
1. 从<!DOCTYPE html>开始，以</html>结束
2. 内嵌完整的CSS样式（在<style>标签中）
3. 内嵌完整的JavaScript代码（在<script>标签中）
4. 包含Phaser.js 3.70.0 CDN引用
5. 实现完整的Phaser.js游戏逻辑（Scene类、preload、create、update方法）
6. 包含用户交互处理（pointer事件）
7. 包含游戏状态管理和重启功能
8. 确保功能完整性
9. 不使用markdown代码块包装
10. 可以直接在浏览器中运行，无任何JavaScript语法错误
11. 所有变量名称唯一，避免重复声明
12. 所有函数和类都完整实现，括号正确配对
13. 特别要求：JavaScript代码必须在</script>标签前完整结束，无遗漏的大括号
14. 所有Scene类都有完整的constructor、preload、create、update方法实现
15. 避免代码中途截断，确保每个代码块都有完整的开始和结束
16. 符合ES6+现代JavaScript语法规范""",
            output_json=None,  # 确保输出纯文本而非JSON
            output_pydantic=None,  # 确保输出纯文本而非Pydantic模型
            context=None,  # 避免上下文干扰
            callback=None  # 避免回调干扰
        )
        
        # 执行任务 - 配置crew参数以支持长输出
        crew = Crew(
            agents=[self.agent],
            tasks=[generation_task],
            verbose=True,
            memory=False,  # 🔑 禁用内存以避免上下文截断
            cache=False,   # 🔑 禁用缓存确保每次都是新的输出
            max_rpm=10,    # 限制每分钟请求数避免被限流
            planning=False,  # 禁用规划功能，直接执行
            embedder={"provider": "openai"},  # 使用默认embedder配置
            step_callback=None,  # 避免回调干扰
            process="sequential"  # 明确指定sequential流程
        )
        
        logger.info("🚀 [调试] 开始调用CrewAI...")
        
        try:
            # 使用标准的kickoff方法
            result = crew.kickoff()
            logger.info("✅ CrewAI执行完成")
        except Exception as e:
            logger.error(f"❌ CrewAI执行失败: {e}")
            # 尝试获取更多错误信息
            if hasattr(e, 'response'):
                logger.error(f"错误响应: {e.response}")
            raise e
    
        
        # 检查所有可能的属性
        attributes_to_check = ['raw', 'json_dict', 'pydantic', 'tasks_output', 'final_output']
        for attr in attributes_to_check:
            has_attr = hasattr(result, attr)
            logger.info(f"{attr}属性: {has_attr}")
            if has_attr:
                attr_value = getattr(result, attr)
                logger.info(f"{attr}值类型: {type(attr_value)}")
                if attr_value:
                    logger.info(f"{attr}值长度: {len(str(attr_value))}")
        
        # 获取实际的HTML内容 - 优化获取策略
        html_content = None
        
        # 策略1：尝试获取raw属性
        if hasattr(result, 'raw') and result.raw:
            html_content = result.raw
            logger.info(f"✅ 使用result.raw获取内容，长度: {len(html_content)} 字符")
        
        # 策略2：尝试获取tasks_output
        elif hasattr(result, 'tasks_output') and result.tasks_output:
            if isinstance(result.tasks_output, list) and len(result.tasks_output) > 0:
                task_output = result.tasks_output[0]
                if hasattr(task_output, 'raw'):
                    html_content = task_output.raw
                    logger.info(f"✅ 使用tasks_output[0].raw获取内容，长度: {len(html_content)} 字符")
                else:
                    html_content = str(task_output)
                    logger.info(f"✅ 使用str(tasks_output[0])获取内容，长度: {len(html_content)} 字符")
            else:
                html_content = str(result.tasks_output)
                logger.info(f"✅ 使用str(tasks_output)获取内容，长度: {len(html_content)} 字符")
        
        # 策略3：尝试其他属性
        elif hasattr(result, 'json_dict') and result.json_dict:
            html_content = str(result.json_dict)
            logger.info(f"✅ 使用result.json_dict获取内容，长度: {len(html_content)} 字符")
        elif hasattr(result, 'pydantic') and result.pydantic:
            html_content = str(result.pydantic)
            logger.info(f"✅ 使用result.pydantic获取内容，长度: {len(html_content)} 字符")
        
        # 策略4：最后的备选方案
        else:
            html_content = str(result)
            logger.info(f"⚠️ 使用str(result)获取内容，长度: {len(html_content)} 字符")
        
        # 确保获取到了内容
        if not html_content:
            logger.error("❌ 无法从CrewOutput对象获取任何内容")
            raise ValueError("CrewOutput对象返回空内容")
        
        # 记录生成的原始内容信息
        logger.info(f"生成的HTML内容长度: {len(html_content)} 字符")
        if html_content.strip().startswith('<!DOCTYPE html>'):
            logger.info("✅ 内容以DOCTYPE html开始")
        else:
            logger.warning("⚠️ 内容未以DOCTYPE html开始")
        
        return html_content
    

    def _build_generation_task(self, context: Dict[str, Any]) -> str:
        """构建高质量Phaser.js游戏生成任务描述"""
        
        game_type = context['game_type']
        core_mechanics = context['core_mechanics']
        
        # 获取游戏设计指南
        game_guide = self.game_designer.generate_game_mechanics_guide(
            game_type=game_type,
            core_mechanics=core_mechanics,
            target_audience=context.get('target_audience', '休闲玩家'),
            technical_constraints=context.get('technical_constraints')
        )
        
        return f"""生成一个完整的高质量{game_type}HTML5游戏，基于Phaser.js 3.70.0框架，严格按照以下规范：

【项目规格】
- 游戏类型：{game_type}
- 核心机制：{', '.join(core_mechanics)}
- 目标分辨率：{context['game_width']}x{context['game_height']}px（支持响应式适配）
- 技术框架：Phaser.js 3.70.0（必须使用最新API规范）
- 目标平台：现代Web浏览器（桌面和移动端全兼容）

【Phaser.js架构要求】
🎯 必须严格遵循Phaser 3.70.0最新API规范和最佳实践：

1. **场景管理（Scene Management）**
   - 使用Phaser.Scene基类创建游戏场景
   - 实现完整的场景生命周期：preload() → create() → update()
   - 支持多场景切换：MenuScene、GameScene、GameOverScene
   - 使用scene key进行场景管理和切换

2. **资源管理（Asset Management）**
   - 在preload()中正确加载所有资源
   - 使用合适的资源加载器：this.load.image(), this.load.spritesheet(), this.load.audio()
   - 实现资源缓存和预加载进度显示
   - 优先使用几何图形绘制，避免外部图片依赖

3. **物理引擎配置（Physics Engine）**
   - 启用Arcade Physics：physics: {{ default: 'arcade', arcade: {{ ... }} }}
   - 正确配置物理属性：gravity, bounce, velocity, acceleration
   - 实现碰撞检测：this.physics.overlap(), this.physics.collide()
   - 使用物理组：this.physics.add.group()

4. **游戏对象管理（Game Object Management）**
   - 使用Phaser的内置对象：Sprite, Image, Graphics, Text
   - 实现对象池模式优化性能：Phaser.GameObjects.Group
   - 正确管理对象生命周期：create, update, destroy
   - 使用容器组织复杂对象：this.add.container()

5. **输入处理（Input Handling）**
   - 统一输入管理：this.input.on('pointerdown/pointerup')
   - 支持多平台输入：鼠标、触摸、键盘
   - 实现输入映射和手势识别
   - 添加输入反馈和视觉提示

🚨 **JavaScript语法严格要求**
⚠️ 必须严格遵守以下JavaScript语法规范，避免任何语法错误：

**变量声明规范：**
- 避免在同一作用域内重复声明变量（特别是const/let）
- 使用唯一的变量名，如需重用概念相同的变量，用不同名称
- 示例：第一次用`const dirs`，第二次用`const directions`或`const checkDirs`

**作用域管理：**
- 合理使用块级作用域 `{{ }}` 来隔离变量
- 函数内部变量不要与外部变量重名
- 避免全局变量污染

**代码完整性：**
- 确保所有的大括号、小括号、方括号都正确配对
- 每个函数都必须有完整的实现，不能中途截断
- 所有的类定义都必须完整闭合
- 特别注意：每个Scene类都必须有完整的constructor、preload、create、update方法
- 确保JavaScript代码在</script>标签前正确结束，没有遗漏的大括号
- 避免代码突然中断或截断，必须生成完整的代码结构

**语法检查清单：**
- ✅ 没有重复声明的变量名
- ✅ 所有括号都正确配对（大括号、小括号、方括号）
- ✅ 所有方法都有完整实现，包含开头和结尾的大括号
- ✅ 所有类定义完整闭合，没有遗漏的大括号
- ✅ 没有未定义的变量引用
- ✅ 正确的作用域管理
- ✅ 符合ES6+语法规范
- ✅ 代码结构完整，没有突然中断或截断
- ✅ JavaScript代码在</script>前正确结束

【技术实现规范】
📋 HTML结构要求：
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{game_type}游戏</title>
    <!-- 响应式CSS样式 -->
</head>
<body>
    <!-- CDN引用Phaser.js 3.70.0 -->
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <!-- 游戏容器和UI元素 -->
    <!-- 游戏JavaScript代码 -->
</body>
</html>
```

🎮 Phaser配置模板：
```javascript
const config = {{
    type: Phaser.AUTO,
    width: {context['game_width']},
    height: {context['game_height']},
    parent: 'game-container',
    backgroundColor: '#2c3e50',
    scale: {{
        mode: Phaser.Scale.FIT,
        autoCenter: Phaser.Scale.CENTER_BOTH,
        min: {{ width: 320, height: 240 }},
        max: {{ width: 1920, height: 1080 }}
    }},
    physics: {{
        default: 'arcade',
        arcade: {{
            gravity: {{ y: 300 }},
            debug: false
        }}
    }},
    scene: [MenuScene, GameScene, GameOverScene]
}};
```

🏗️ 场景架构模式：
```javascript
class GameScene extends Phaser.Scene {{
    constructor() {{
        super({{ key: 'GameScene' }});
    }}
    
    preload() {{
        // 资源加载逻辑
        this.load.on('progress', (percent) => {{
            // 加载进度显示
        }});
    }}
    
    create() {{
        // 游戏对象初始化
        // 物理世界设置
        // 输入事件绑定
        // UI元素创建
    }}
    
    update(time, delta) {{
        // 游戏主循环逻辑
        // 对象状态更新
        // 碰撞检测
        // 游戏状态判断
    }}
}}
```

【性能优化要求】
⚡ 必须实现以下性能优化：
1. **对象池管理**：使用Phaser.GameObjects.Group实现对象重用
2. **批量渲染**：合理使用Graphics对象减少绘制调用
3. **纹理优化**：优先使用几何图形，避免大量图片资源
4. **内存管理**：及时销毁不用的对象，防止内存泄漏
5. **帧率优化**：控制update频率，避免复杂计算

【响应式设计要求】
📱 必须实现完整的响应式适配：
1. 使用Phaser.Scale.FIT自适应屏幕
2. 支持横屏和竖屏模式切换
3. UI元素按比例缩放
4. 触摸和鼠标事件统一处理
5. 适配不同设备的性能差异

【游戏状态管理】
🎲 实现完整的游戏状态系统：
1. **菜单状态**：开始游戏、设置、关于
2. **游戏状态**：运行、暂停、继续
3. **结束状态**：游戏结束、重新开始、返回菜单
4. **数据持久化**：本地存储高分和设置
5. **状态转换**：平滑的场景切换动画

【代码质量标准】
✨ 代码必须符合以下质量标准：
1. **清晰注释**：每个关键方法和逻辑都有中文注释
2. **模块化设计**：功能分离，便于维护和扩展
3. **错误处理**：适当的异常捕获和错误提示
4. **调试支持**：开发模式下的调试信息输出
5. **代码规范**：遵循JavaScript ES6+标准

【游戏机制指南】
{game_guide}

【最终输出要求】
🚨 严格按照以下要求输出：
1. **完整性**：从<!DOCTYPE html>到</html>的完整代码
2. **可运行性**：可以直接在浏览器中运行，无任何JavaScript语法错误
3. **格式要求**：不使用markdown代码块包装，直接输出HTML
4. **注释完整**：关键逻辑都有详细的中文注释说明
5. **性能优化**：包含必要的性能优化代码
6. **扩展性**：代码结构清晰，便于后续功能扩展
7. **语法正确**：通过JavaScript语法检查，无重复变量声明等错误

【代码生成前必须检查】
⚠️ 在输出代码前，必须自检以下关键点：

**语法正确性检查：**
✅ 没有重复声明的变量名（const/let/var不能重复）
✅ 所有大括号、小括号、方括号都正确配对
✅ 所有函数和类定义都完整闭合
✅ 特别检查：每个class定义都有完整的constructor和所有方法
✅ 特别检查：所有if/for/while语句都有完整的大括号
✅ 特别检查：JavaScript代码在</script>标签前完整结束
✅ 没有语法错误如Unexpected token、Unexpected end of input等
✅ 变量作用域正确，没有未定义变量引用
✅ 代码没有中途截断或不完整的函数实现

**Phaser.js API检查：**
✅ 正确引用Phaser.js 3.70.0 CDN
✅ 使用最新的Scene类和生命周期方法
✅ 正确配置游戏config对象
✅ 物理引擎配置正确（Arcade Physics）
✅ 输入处理使用统一的pointer事件
✅ 场景管理和切换逻辑完整
✅ 资源加载和缓存管理正确
✅ 响应式缩放配置正确

**代码质量检查：**
✅ 性能优化措施已实现
✅ 游戏状态管理完整
✅ 错误处理和调试支持完善
✅ 代码注释清晰完整
✅ 代码逻辑完整，没有未完成的函数
✅ 符合ES6+现代JavaScript标准

🔥 **重要提醒**：如果代码中需要多次使用相似的变量，请使用不同的变量名：
- 第一次：`const dirs = [[1,0],[0,1],[1,1],[1,-1]]`
- 第二次：`const directions = [[1,0],[0,1],[1,1],[1,-1]]`
- 第三次：`const checkDirections = [[1,0],[0,1],[1,1],[1,-1]]`

🚨 **严重警告：防止语法错误的核心规则**
1. **禁止类定义中途截断**：确保每个Scene类从class开头到最后的大括号完整
2. **禁止方法写在类外部**：所有方法必须在对应类的大括号内部
3. **禁止无意义空行填充**：用有效代码达到长度要求，而不是空行
4. **禁止代码突然中断**：每个函数都必须完整实现，不能半途而废
5. **绝对禁止错误的--运算符使用**：如`--this.score`这样的代码会导致语法错误

⚠️ **代码结构完整性检查（生成前必须验证）**：
✅ GameScene类完整：从class GameScene extends Phaser.Scene开头到最后一个大括号结束
✅ 所有方法都在类内部：initSnake(), handlePointerMove(), update()等方法都在类的大括号内
✅ 没有孤立的函数定义在类外部
✅ JavaScript语法100%正确，能通过语法解析器
✅ 代码通过逻辑内容达到长度，不使用空行填充

现在请生成基于Phaser.js 3.70.0的完整{game_type}游戏代码："""

# 使用示例
if __name__ == "__main__":
    # 测试代码生成器
    code_agent = CodeGeneratorAgent()
    
    sample_architecture = {
        "framework": "Phaser.js 3.x"
    }
    
    sample_requirements = {
        "game_type": "点击收集游戏",
        "core_mechanics": ["点击收集", "分数累积"],
        "interactions": ["点击", "触摸"]
    }
    
    try:
        result = code_agent.generate_code(sample_architecture, sample_requirements)
        print(f"生成完成: {result['generation_summary']['estimated_size_kb']}KB")
    except Exception as e:
        print(f"测试失败: {e}") 