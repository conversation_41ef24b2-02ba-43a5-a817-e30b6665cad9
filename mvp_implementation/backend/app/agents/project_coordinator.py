#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目协调智能体 - MVP版本
负责整体项目管理、智能体协调和工作流程控制
"""

import json
import logging
import os
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timed<PERSON>ta
from crewai import Agent, Task, Crew, LLM
from pydantic import BaseModel, Field

# 导入其他智能体
from .product_manager import ProductManagerAgent
from .code_generator import CodeGeneratorAgent

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProjectStatus(BaseModel):
    """项目状态数据模型"""
    project_id: str = Field(description="项目ID")
    current_phase: str = Field(description="当前阶段")
    progress_percentage: float = Field(description="进度百分比")
    start_time: datetime = Field(description="开始时间")
    estimated_completion: datetime = Field(description="预计完成时间")
    actual_completion: Optional[datetime] = Field(description="实际完成时间")
    status: str = Field(description="状态: planning, in_progress, review, completed, failed")
    issues: List[str] = Field(description="当前问题列表")
    next_actions: List[str] = Field(description="下一步行动")

class TaskAssignment(BaseModel):
    """任务分配数据模型"""
    task_id: str = Field(description="任务ID")
    agent_name: str = Field(description="负责智能体")
    task_type: str = Field(description="任务类型")
    priority: str = Field(description="优先级")
    dependencies: List[str] = Field(description="依赖任务")
    estimated_duration: int = Field(description="预计耗时（分钟）")
    status: str = Field(description="任务状态")
    assigned_time: datetime = Field(description="分配时间")
    deadline: datetime = Field(description="截止时间")

class WorkflowResult(BaseModel):
    """工作流结果数据模型"""
    success: bool = Field(description="是否成功")
    project_status: ProjectStatus = Field(description="项目状态")
    deliverables: Dict[str, Any] = Field(description="交付物")
    quality_metrics: Dict[str, float] = Field(description="质量指标")
    execution_log: List[str] = Field(description="执行日志")
    recommendations: List[str] = Field(description="建议")

class ProjectCoordinatorAgent:
    """项目协调智能体"""
    
    def __init__(self, llm_model: str = "gpt-4.1", temperature: float = 0.2):
        """
        初始化项目协调智能体
        
        Args:
            llm_model: 使用的语言模型
            temperature: 模型温度参数
        """
        # 🔍 优先使用配置文件中的设置
        from ..config import settings
        actual_model = settings.openai_model
        actual_temperature = settings.openai_temperature
        
        self.llm_model = actual_model if actual_model else llm_model
        self.temperature = actual_temperature if actual_temperature is not None else temperature
        
        logger.info(f"🔍 [ProjectCoordinator] 使用模型: {self.llm_model}, 温度: {self.temperature}")
        self.current_projects = {}
        self.task_queue = []
        self.execution_log = []
        
        # 初始化智能体
        self._initialize_agent()
        self._initialize_sub_agents()
        self._setup_workflow_templates()
        
    def _initialize_agent(self):
        """初始化crewAI协调智能体"""
        try:
            # 创建LLM实例 - 使用CrewAI的LLM类
            custom_llm = LLM(
                model=self.llm_model,
                temperature=self.temperature,
                max_tokens=4000,
                timeout=300
            )
            
            self.agent = Agent(
                role='Playable广告项目协调专家',
                goal='高效协调多智能体协作，确保项目按时高质量交付',
                backstory="""
                你是一位经验丰富的项目管理专家，专门负责AI驱动的Playable广告开发项目。
                你深度理解软件开发生命周期，擅长任务分解、资源调度和风险管控。
                你具备敏捷开发和DevOps实践经验，能够有效协调产品、技术、开发和质量团队。
                你注重沟通协调，善于识别瓶颈和依赖关系，确保项目顺利推进。
                你具备数据驱动的决策能力，能够基于实时反馈优化工作流程。
                """,
                llm=custom_llm,
                verbose=True,
                allow_delegation=True,
                max_iter=5
            )
            logger.info("项目协调智能体初始化成功")
        except Exception as e:
            logger.error(f"协调智能体初始化失败: {e}")
            raise
    
    def _initialize_sub_agents(self):
        """初始化子智能体"""
        try:
            # 初始化产品经理智能体
            self.product_manager = ProductManagerAgent(
                llm_model=self.llm_model,
                temperature=self.temperature
            )
            
            # 初始化代码生成智能体（现在包含架构设计功能）
            self.code_generator = CodeGeneratorAgent(
                llm_model=self.llm_model,
                temperature=self.temperature
            )
            
            self.agents = {
                'product_manager': self.product_manager,
                'code_generator': self.code_generator
            }
            
            logger.info("所有子智能体初始化完成")
        except Exception as e:
            logger.error(f"子智能体初始化失败: {e}")
            raise
    
    def _setup_workflow_templates(self):
        """设置工作流模板"""
        self.workflow_templates = {
            'standard_playable_ad': {
                'phases': [
                    {
                        'name': 'requirements_analysis',
                        'agent': 'product_manager',
                        'method': 'analyze_requirements',
                        'estimated_duration': 15,
                        'dependencies': []
                    },
                    {
                        'name': 'architecture_and_code_generation',
                        'agent': 'code_generator',
                        'method': 'design_and_generate',
                        'estimated_duration': 40,
                        'dependencies': ['requirements_analysis']
                    }
                ],
                'total_estimated_duration': 55
            },
            'rapid_prototype': {
                'phases': [
                    {
                        'name': 'quick_requirements',
                        'agent': 'product_manager',
                        'method': 'analyze_requirements',
                        'estimated_duration': 5,
                        'dependencies': []
                    },
                    {
                        'name': 'rapid_development',
                        'agent': 'code_generator',
                        'method': 'design_and_generate',
                        'estimated_duration': 20,
                        'dependencies': ['quick_requirements']
                    }
                ],
                'total_estimated_duration': 25
            }
        }
    
    def create_project(self, user_input: str, project_type: str = 'standard_playable_ad') -> str:
        """
        创建新项目
        
        Args:
            user_input: 用户输入的需求描述
            project_type: 项目类型
            
        Returns:
            项目ID
        """
        try:
            project_id = f"project_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 创建项目状态
            project_status = ProjectStatus(
                project_id=project_id,
                current_phase='initialization',
                progress_percentage=0.0,
                start_time=datetime.now(),
                estimated_completion=datetime.now() + timedelta(
                    minutes=self.workflow_templates[project_type]['total_estimated_duration']
                ),
                actual_completion=None,
                status='planning',
                issues=[],
                next_actions=['开始需求分析']
            )
            
            # 存储项目信息
            self.current_projects[project_id] = {
                'status': project_status,
                'user_input': user_input,
                'project_type': project_type,
                'workflow': self.workflow_templates[project_type],
                'results': {},
                'created_time': datetime.now()
            }
            
            # 生成任务队列
            self._generate_task_queue(project_id)
            
            logger.info(f"项目 {project_id} 创建成功，类型: {project_type}")
            self._log_execution(project_id, f"项目创建成功，预计耗时 {self.workflow_templates[project_type]['total_estimated_duration']} 分钟")
            
            return project_id
            
        except Exception as e:
            logger.error(f"项目创建失败: {e}")
            raise
    
    def _generate_task_queue(self, project_id: str):
        """生成任务队列"""
        project = self.current_projects[project_id]
        workflow = project['workflow']
        
        tasks = []
        for phase in workflow['phases']:
            task = TaskAssignment(
                task_id=f"{project_id}_{phase['name']}",
                agent_name=phase['agent'],
                task_type=phase['name'],
                priority='normal',
                dependencies=phase['dependencies'],
                estimated_duration=phase['estimated_duration'],
                status='pending',
                assigned_time=datetime.now(),
                deadline=datetime.now() + timedelta(minutes=phase['estimated_duration'])
            )
            tasks.append(task)
        
        project['tasks'] = tasks
        self.task_queue.extend(tasks)
    
    def execute_project(self, project_id: str) -> WorkflowResult:
        """
        执行项目工作流
        
        Args:
            project_id: 项目ID
            
        Returns:
            工作流执行结果
        """
        try:
            logger.info(f"开始执行项目 {project_id}")
            
            if project_id not in self.current_projects:
                raise ValueError(f"项目 {project_id} 不存在")
            
            project = self.current_projects[project_id]
            project['status'].status = 'in_progress'
            
            # 执行工作流阶段
            execution_results = {}
            total_phases = len(project['workflow']['phases'])
            
            for i, phase in enumerate(project['workflow']['phases']):
                try:
                    # 更新项目状态
                    project['status'].current_phase = phase['name']
                    project['status'].progress_percentage = (i / total_phases) * 100
                    
                    self._log_execution(project_id, f"开始执行阶段: {phase['name']}")
                    
                    # 检查依赖
                    if not self._check_dependencies(phase['dependencies'], execution_results):
                        raise Exception(f"阶段 {phase['name']} 的依赖条件未满足")
                    
                    # 执行阶段
                    phase_result = self._execute_phase(project_id, phase, execution_results)
                    execution_results[phase['name']] = phase_result
                    
                    self._log_execution(project_id, f"阶段 {phase['name']} 执行完成")
                    
                except Exception as e:
                    error_msg = f"阶段 {phase['name']} 执行失败: {e}"
                    logger.error(error_msg)
                    self._log_execution(project_id, error_msg)
                    
                    # 尝试恢复或跳过
                    if self._should_continue_on_error(phase['name']):
                        execution_results[phase['name']] = self._get_fallback_result(phase['name'])
                        continue
                    else:
                        project['status'].status = 'failed'
                        project['status'].issues.append(error_msg)
                        raise
            
            # 完成项目
            project['status'].status = 'completed'
            project['status'].progress_percentage = 100.0
            project['status'].actual_completion = datetime.now()
            
            # 生成最终结果
            workflow_result = self._generate_workflow_result(project_id, execution_results)
            
            logger.info(f"项目 {project_id} 执行完成")
            return workflow_result
            
        except Exception as e:
            logger.error(f"项目执行失败: {e}")
            if project_id in self.current_projects:
                self.current_projects[project_id]['status'].status = 'failed'
                self.current_projects[project_id]['status'].issues.append(str(e))
            raise
    
    def _execute_phase(self, project_id: str, phase: Dict[str, Any], 
                      previous_results: Dict[str, Any]) -> Dict[str, Any]:
        """执行单个阶段"""
        project = self.current_projects[project_id]
        agent_name = phase['agent']
        method_name = phase['method']
        
        if agent_name not in self.agents:
            raise ValueError(f"智能体 {agent_name} 不存在")
        
        agent = self.agents[agent_name]
        method = getattr(agent, method_name, None)
        
        if not method:
            raise ValueError(f"智能体 {agent_name} 没有方法 {method_name}")
        
        # 准备输入参数
        input_params = self._prepare_phase_input(project_id, phase, previous_results)
        
        # 执行方法
        start_time = datetime.now()
        result = method(**input_params)
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # 包装结果
        phase_result = {
            'result': result,
            'execution_time': execution_time,
            'agent': agent_name,
            'method': method_name,
            'timestamp': datetime.now().isoformat()
        }
        
        return phase_result
    
    def _prepare_phase_input(self, project_id: str, phase: Dict[str, Any], 
                           previous_results: Dict[str, Any]) -> Dict[str, Any]:
        """准备阶段输入参数"""
        project = self.current_projects[project_id]
        phase_name = phase['name']
        
        # 根据阶段类型准备特定参数
        input_params = {}
        
        if phase_name == 'requirements_analysis':
            # 需求分析阶段只需要用户输入
            input_params['user_input'] = project['user_input']
        
        elif phase_name in ['technical_design', 'simple_design']:
            # 技术设计阶段需要需求分析结果
            if 'requirements_analysis' in previous_results or 'quick_requirements' in previous_results:
                req_key = 'requirements_analysis' if 'requirements_analysis' in previous_results else 'quick_requirements'
                input_params['product_requirements'] = previous_results[req_key]['result']
            else:
                input_params['product_requirements'] = {'error': '缺少需求分析结果'}
        
        elif phase_name in ['code_generation', 'rapid_coding', 'architecture_and_code_generation', 'rapid_development']:
            # 代码生成阶段需要需求分析结果
            req_key = 'requirements_analysis' if 'requirements_analysis' in previous_results else 'quick_requirements'
            
            if req_key in previous_results:
                input_params['requirements'] = previous_results[req_key]['result']
            
            # 如果有技术设计结果，也传递给代码生成器（向后兼容）
            design_key = 'technical_design' if 'technical_design' in previous_results else 'simple_design'
            if design_key in previous_results:
                input_params['architecture'] = previous_results[design_key]['result']
        
        elif phase_name in ['quality_review', 'basic_review']:
            # 质量审查阶段需要需求和生成的代码
            code_key = 'code_generation' if 'code_generation' in previous_results else 'rapid_coding'
            req_key = 'requirements_analysis' if 'requirements_analysis' in previous_results else 'quick_requirements'
            
            if req_key in previous_results:
                input_params['requirements'] = previous_results[req_key]['result']
            if code_key in previous_results:
                input_params['generated_code'] = previous_results[code_key]['result']
        
        elif phase_name == 'test_generation':
            # 测试生成阶段需要需求和代码
            req_key = 'requirements_analysis' if 'requirements_analysis' in previous_results else 'quick_requirements'
            code_key = 'code_generation' if 'code_generation' in previous_results else 'rapid_coding'
            
            if req_key in previous_results:
                input_params['requirements'] = previous_results[req_key]['result']
            if code_key in previous_results:
                input_params['generated_code'] = previous_results[code_key]['result']
        
        return input_params
    
    def _check_dependencies(self, dependencies: List[str], 
                          completed_phases: Dict[str, Any]) -> bool:
        """检查依赖条件"""
        for dep in dependencies:
            if dep not in completed_phases:
                return False
            if not completed_phases[dep].get('result'):
                return False
        return True
    
    def _should_continue_on_error(self, phase_name: str) -> bool:
        """判断是否应该在错误时继续"""
        # 关键阶段失败时不继续
        critical_phases = ['requirements_analysis', 'quick_requirements', 'code_generation', 'rapid_coding']
        return phase_name not in critical_phases
    
    def _get_fallback_result(self, phase_name: str) -> Dict[str, Any]:
        """获取备用结果"""
        fallback_results = {
            'technical_design': {
                'result': {
                    'architecture': {'framework': 'Phaser.js', 'structure': 'basic'},
                    'error': '使用默认架构设计'
                }
            },
            'quality_review': {
                'result': {
                    'review_summary': {'quality_score': 50, 'total_issues_found': 0},
                    'recommendations': ['手动检查代码质量'],
                    'error': '质量审查失败，使用默认结果'
                }
            },
            'test_generation': {
                'result': {
                    'test_summary': {'total_test_cases': 1},
                    'test_cases': [{'test_name': '基础功能测试'}],
                    'error': '测试生成失败，使用默认测试用例'
                }
            }
        }
        
        return fallback_results.get(phase_name, {
            'result': {'error': f'{phase_name} 阶段失败'},
            'execution_time': 0,
            'agent': 'fallback',
            'method': 'fallback',
            'timestamp': datetime.now().isoformat()
        })
    
    def _generate_workflow_result(self, project_id: str, 
                                execution_results: Dict[str, Any]) -> WorkflowResult:
        """生成工作流结果"""
        project = self.current_projects[project_id]
        
        # 提取交付物
        deliverables = {}
        # 检查所有可能的代码生成阶段
        code_phase_keys = ['code_generation', 'rapid_coding', 'architecture_and_code_generation', 'rapid_development']
        for code_key in code_phase_keys:
            if code_key in execution_results:
                deliverables['generated_code'] = execution_results[code_key]['result']
                break
        
        if 'quality_review' in execution_results or 'basic_review' in execution_results:
            review_key = 'quality_review' if 'quality_review' in execution_results else 'basic_review'
            deliverables['quality_report'] = execution_results[review_key]['result']
        
        if 'test_generation' in execution_results:
            deliverables['test_cases'] = execution_results['test_generation']['result']
        
        # 计算质量指标
        quality_metrics = self._calculate_quality_metrics(execution_results)
        
        # 生成建议
        recommendations = self._generate_project_recommendations(execution_results, project)
        
        return WorkflowResult(
            success=project['status'].status == 'completed',
            project_status=project['status'],
            deliverables=deliverables,
            quality_metrics=quality_metrics,
            execution_log=self.execution_log,
            recommendations=recommendations
        )
    
    def _calculate_quality_metrics(self, execution_results: Dict[str, Any]) -> Dict[str, float]:
        """计算质量指标"""
        metrics = {
            'overall_success_rate': 0.0,
            'code_quality_score': 0.0,
            'execution_efficiency': 0.0,
            'completeness_score': 0.0
        }
        
        # 计算成功率
        total_phases = len(execution_results)
        successful_phases = len([r for r in execution_results.values() 
                               if r.get('result') and 'error' not in r['result']])
        metrics['overall_success_rate'] = (successful_phases / total_phases * 100) if total_phases > 0 else 0
        
        # 提取代码质量分数
        review_keys = ['quality_review', 'basic_review']
        for key in review_keys:
            if key in execution_results:
                result = execution_results[key]['result']
                if 'review_summary' in result and 'quality_score' in result['review_summary']:
                    metrics['code_quality_score'] = result['review_summary']['quality_score']
                break
        
        # 计算执行效率（基于预计时间vs实际时间）
        total_execution_time = sum(r.get('execution_time', 0) for r in execution_results.values())
        if total_execution_time > 0:
            metrics['execution_efficiency'] = min(100, (60 / total_execution_time) * 100)  # 假设理想时间为60秒
        
        # 计算完整性分数
        expected_deliverables = ['generated_code', 'quality_report']
        actual_deliverables = 0
        
        # 检查代码生成阶段
        code_phase_keys = ['code_generation', 'rapid_coding', 'architecture_and_code_generation', 'rapid_development']
        if any(key in execution_results for key in code_phase_keys):
            actual_deliverables += 1
        if any(key in execution_results for key in ['quality_review', 'basic_review']):
            actual_deliverables += 1
        
        metrics['completeness_score'] = (actual_deliverables / len(expected_deliverables)) * 100
        
        return metrics
    
    def _generate_project_recommendations(self, execution_results: Dict[str, Any], 
                                        project: Dict[str, Any]) -> List[str]:
        """生成项目建议"""
        recommendations = []
        
        # 基于执行结果的建议
        failed_phases = [name for name, result in execution_results.items() 
                        if 'error' in result.get('result', {})]
        
        if failed_phases:
            recommendations.append(f"以下阶段执行失败，建议重新执行: {', '.join(failed_phases)}")
        
        # 基于质量分数的建议
        review_keys = ['quality_review', 'basic_review']
        for key in review_keys:
            if key in execution_results:
                result = execution_results[key]['result']
                if 'review_summary' in result:
                    quality_score = result['review_summary'].get('quality_score', 0)
                    if quality_score < 70:
                        recommendations.append("代码质量分数较低，建议进行代码重构")
                    elif quality_score < 90:
                        recommendations.append("代码质量良好，建议进行细节优化")
                break
        
        # 基于项目类型的建议
        if project['project_type'] == 'rapid_prototype':
            recommendations.append("这是快速原型，建议后续进行完整的质量检查")
            recommendations.append("考虑升级为标准开发流程以获得更好的质量")
        
        # 通用建议
        recommendations.append("建议进行用户测试以验证功能和体验")
        recommendations.append("考虑设置持续集成流程以提高开发效率")
        
        return recommendations
    
    def _log_execution(self, project_id: str, message: str):
        """记录执行日志"""
        log_entry = f"[{datetime.now().strftime('%H:%M:%S')}] {project_id}: {message}"
        self.execution_log.append(log_entry)
        logger.info(log_entry)
    
    def get_project_status(self, project_id: str) -> Optional[ProjectStatus]:
        """获取项目状态"""
        if project_id in self.current_projects:
            return self.current_projects[project_id]['status']
        return None
    
    def list_projects(self) -> List[Dict[str, Any]]:
        """列出所有项目"""
        projects = []
        for project_id, project_data in self.current_projects.items():
            projects.append({
                'project_id': project_id,
                'status': project_data['status'].status,
                'progress': project_data['status'].progress_percentage,
                'created_time': project_data['created_time'].isoformat(),
                'project_type': project_data['project_type']
            })
        return projects
    
    def cancel_project(self, project_id: str) -> bool:
        """取消项目"""
        if project_id in self.current_projects:
            self.current_projects[project_id]['status'].status = 'cancelled'
            self._log_execution(project_id, "项目已取消")
            return True
        return False
    
    def create_crew_workflow(self, project_id: str) -> Crew:
        """创建crewAI工作流"""
        if project_id not in self.current_projects:
            raise ValueError(f"项目 {project_id} 不存在")
        
        project = self.current_projects[project_id]
        
        # 创建任务列表
        tasks = []
        for phase in project['workflow']['phases']:
            task = Task(
                description=f"执行 {phase['name']} 阶段",
                agent=self.agents[phase['agent']].agent,
                expected_output=f"{phase['name']} 阶段的执行结果"
            )
            tasks.append(task)
        
        # 创建Crew
        crew = Crew(
            agents=list(self.agents.values()),
            tasks=tasks,
            verbose=True,
            process="sequential"  # 顺序执行
        )
        
        return crew
    
    async def execute_project_async(self, project_id: str) -> WorkflowResult:
        """异步执行项目"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.execute_project, project_id)

# 使用示例
if __name__ == "__main__":
    # 测试项目协调智能体
    coordinator = ProjectCoordinatorAgent()
    
    try:
        # 创建项目
        user_input = "创建一个简单的贪吃蛇游戏，支持移动端触摸控制"
        project_id = coordinator.create_project(user_input, 'rapid_prototype')
        print(f"项目创建成功: {project_id}")
        
        # 执行项目
        result = coordinator.execute_project(project_id)
        print(f"项目执行完成，成功: {result.success}")
        print(f"质量指标: {result.quality_metrics}")
        
        # 查看项目状态
        status = coordinator.get_project_status(project_id)
        print(f"项目状态: {status.status}, 进度: {status.progress_percentage}%")
        
    except Exception as e:
        print(f"测试失败: {e}")