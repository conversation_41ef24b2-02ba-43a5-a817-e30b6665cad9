#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏设计师智能体
负责动态生成游戏机制指南和设计模板
"""

import logging
from typing import Dict, Any, List, Optional
from crewai import Agent, Task, Crew, LLM
from pydantic import BaseModel, Field

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GameDesignGuide(BaseModel):
    """游戏设计指南数据模型"""
    game_type: str = Field(description="游戏类型")
    mechanics_guide: str = Field(description="游戏机制指南")
    core_loop: str = Field(description="核心游戏循环")
    implementation_tips: List[str] = Field(description="实现要点")

class GameDesignerAgent:
    """游戏设计师智能体"""
    
    def __init__(self, llm_model: str = "gpt-4", temperature: float = 0.7):
        """
        初始化游戏设计师智能体
        
        Args:
            llm_model: 使用的语言模型
            temperature: 模型温度参数
        """
        # 🔍 优先使用配置文件中的设置
        from ..config import settings
        actual_model = settings.openai_model
        actual_temperature = settings.openai_temperature
        
        self.llm_model = actual_model if actual_model else llm_model
        self.temperature = actual_temperature if actual_temperature is not None else temperature
        
        logger.info(f"🔍 [GameDesigner] 使用模型: {self.llm_model}, 温度: {self.temperature}")
        self._initialize_agent()
        
    def _initialize_agent(self):
        """初始化crewAI智能体"""
        try:
            # 创建LLM实例 - 使用CrewAI的LLM类
            custom_llm = LLM(
                model=self.llm_model,
                temperature=self.temperature,
                max_tokens=8000,
                timeout=300
            )
            
            self.agent = Agent(
                role='游戏设计专家',
                goal='为不同类型的游戏提供专业的设计指南和机制建议，支持多种技术栈',
                backstory="""你是一位资深的游戏设计师，拥有丰富的各类游戏开发经验。
你精通各种游戏类型的核心机制、玩法设计和用户体验优化。

你的专长包括但不限于：
- 休闲游戏：点击收集、消除类、放置类游戏
- 动作游戏：射击、跑酷、格斗类游戏  
- 策略游戏：回合制、实时战略、塔防类游戏
- 益智游戏：解谜、逻辑推理、数学类游戏
- 卡牌游戏：集换式、构筑类、战术类游戏

你能够根据游戏类型、目标受众和技术限制，提供详细的游戏机制设计指南，
包括核心玩法循环、交互系统、反馈机制和平衡性考虑。

你支持多种技术栈的游戏开发，包括原生JavaScript、Canvas API、以及各种HTML5游戏框架。""",
                llm=custom_llm,
                verbose=True,
                allow_delegation=False,
                max_iter=1
            )
            logger.info("游戏设计师智能体初始化成功")
        except Exception as e:
            logger.error(f"游戏设计师智能体初始化失败: {e}")
            raise
    
    def generate_game_mechanics_guide(self, game_type: str, 
                                    core_mechanics: List[str] = None,
                                    target_audience: str = "休闲玩家",
                                    technical_constraints: Dict[str, Any] = None) -> str:
        """
        动态生成游戏机制指南
        
        Args:
            game_type: 游戏类型
            core_mechanics: 核心机制列表
            target_audience: 目标受众
            technical_constraints: 技术限制
            
        Returns:
            游戏机制实现指南
        """
        try:
            logger.info(f"开始生成游戏机制指南 - 游戏类型: {game_type}")
            
            # 构建设计任务描述
            task_description = self._build_design_task(
                game_type, core_mechanics, target_audience, technical_constraints
            )
            
            # 创建设计任务
            design_task = Task(
                description=task_description,
                agent=self.agent,
                expected_output="""详细的游戏机制实现指南，包含：
1. 💡 游戏机制实现要点（5-8个要点）
2. 🎮 核心游戏循环（5步循环流程）
3. 🎯 具体实现建议（技术实现细节）
4. ⚡ 特殊功能和系统（道具、升级、特效等）

格式要求：使用emoji图标，条理清晰，实用性强"""
            )
            
            # 执行任务
            crew = Crew(
                agents=[self.agent],
                tasks=[design_task],
                verbose=True
            )
            
            result = crew.kickoff()
            guide_content = str(result)
            
            logger.info(f"✅ 游戏机制指南生成完成 - {game_type}")
            return guide_content
            
        except Exception as e:
            logger.error(f"❌ 游戏机制指南生成失败: {e}")
            raise e
    
    def generate_html_template_structure(self, game_type: str, 
                                       game_title: str = "我的游戏",
                                       width: int = 800, 
                                       height: int = 600) -> str:
        """
        生成HTML模板结构
        
        Args:
            game_type: 游戏类型
            game_title: 游戏标题
            width: 游戏宽度
            height: 游戏高度
            
        Returns:
            HTML模板字符串
        """
        try:
            logger.info(f"生成HTML模板结构 - {game_type}")
            
            task_description = f"""
为{game_type}生成一个完整的HTML模板结构，要求：

1. 响应式设计，适配移动端和桌面端
2. 现代化的UI风格，包含渐变背景和阴影效果
3. 游戏容器居中显示，带有边框和圆角
4. 包含游戏UI元素：分数显示、时间显示、游戏结束界面
5. 包含重新开始按钮和基础样式
6. 根据需要引入合适的游戏框架CDN链接
7. 预留游戏脚本插入位置

游戏标题：{game_title}
游戏尺寸：{width}x{height}

请生成完整的HTML模板，使用{{game_title}}和{{game_script}}作为占位符。
"""
            
            template_task = Task(
                description=task_description,
                agent=self.agent,
                expected_output="完整的HTML模板代码，包含现代化样式和响应式设计"
            )
            
            crew = Crew(
                agents=[self.agent],
                tasks=[template_task],
                verbose=True
            )
            
            result = crew.kickoff()
            template_content = str(result)
            
            logger.info(f"✅ HTML模板生成完成")
            return template_content
            
        except Exception as e:
            logger.error(f"❌ HTML模板生成失败: {e}")
            raise e
    
    def generate_game_config_template(self, width: int = 800, 
                                    height: int = 600,
                                    game_type: str = "休闲游戏",
                                    framework: str = "自选") -> str:
        """
        生成游戏配置模板
        
        Args:
            width: 游戏宽度
            height: 游戏高度
            game_type: 游戏类型
            framework: 使用的框架
            
        Returns:
            游戏配置JavaScript代码
        """
        try:
            logger.info(f"生成游戏配置模板 - {game_type}")
            
            task_description = f"""
为{game_type}生成游戏配置代码，要求：

1. 游戏尺寸：{width}x{height}
2. 自适应缩放配置，居中显示
3. 根据游戏类型选择合适的技术方案（原生Canvas、游戏框架等）
4. 包含游戏初始化、更新循环、渲染逻辑的基础结构
5. 现代化的样式配置
6. 性能优化的设置

技术栈建议：
- 简单游戏：原生JavaScript + Canvas API
- 复杂游戏：可选择Phaser.js、PixiJS等成熟框架
- 根据游戏复杂度选择最合适的方案

请生成完整的游戏配置JavaScript代码。
"""
            
            config_task = Task(
                description=task_description,
                agent=self.agent,
                expected_output="完整的游戏配置JavaScript代码，适合指定的游戏类型"
            )
            
            crew = Crew(
                agents=[self.agent],
                tasks=[config_task],
                verbose=True
            )
            
            result = crew.kickoff()
            config_content = str(result)
            
            logger.info(f"✅ 游戏配置模板生成完成")
            return config_content
            
        except Exception as e:
            logger.error(f"❌ 游戏配置模板生成失败: {e}")
            raise e
    
    def _build_design_task(self, game_type: str, 
                          core_mechanics: List[str] = None,
                          target_audience: str = "休闲玩家",
                          technical_constraints: Dict[str, Any] = None) -> str:
        """构建游戏设计任务描述"""
        
        mechanics_text = ""
        if core_mechanics:
            mechanics_text = f"\n核心机制要求：{', '.join(core_mechanics)}"
        
        constraints_text = ""
        if technical_constraints:
            constraints_text = f"\n技术限制：{technical_constraints}"
        
        return f"""
请为{game_type}设计详细的游戏机制实现指南。

游戏类型：{game_type}
目标受众：{target_audience}{mechanics_text}{constraints_text}

请提供：
1. 💡 游戏机制实现要点：列出5-8个关键的游戏机制实现要点
2. 🎮 核心游戏循环：描述完整的游戏循环流程（5个步骤）
3. 🎯 具体实现建议：提供技术实现的具体建议和细节
4. ⚡ 特殊功能：道具系统、升级机制、特效等增强游戏性的功能

要求：
- 内容实用，可直接用于游戏开发
- 支持多种HTML5技术栈（原生JavaScript、Canvas API、各种游戏框架）
- 适合单文件游戏的实现方式
- 注重用户体验和游戏平衡性
- 根据游戏复杂度推荐最合适的技术方案
"""
    
 