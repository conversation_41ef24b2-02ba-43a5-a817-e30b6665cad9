#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码清理工具
用于清理和优化生成的HTML游戏代码
"""

import re
import logging
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class CodeCleaningUtils:
    """代码清理工具类"""
    
    def __init__(self):
        """初始化代码清理工具"""
        self.html_patterns = {
            # 移除多余的空行
            'extra_lines': re.compile(r'\n\s*\n\s*\n', re.MULTILINE),
            # 移除行首尾空白
            'line_spaces': re.compile(r'^\s+|\s+$', re.MULTILINE),
            # 移除注释中的多余空格
            'comment_spaces': re.compile(r'//\s+', re.MULTILINE),
            # 移除console.log语句
            'console_logs': re.compile(r'console\.log\([^)]*\);\s*\n?', re.MULTILINE),
            # 移除调试代码
            'debug_code': re.compile(r'//\s*debug.*?\n|/\*\s*debug.*?\*/', re.MULTILINE | re.DOTALL)
        }
    
    def clean_html_content(self, html_content: str) -> str:
        """
        清理HTML内容
        
        Args:
            html_content: 原始HTML内容
            
        Returns:
            清理后的HTML内容
        """
        try:
            # 基础清理
            cleaned = self._basic_cleanup(html_content)
            
            # 提取并清理各部分
            cleaned = self._clean_css_section(cleaned)
            cleaned = self._clean_js_section(cleaned)
            cleaned = self._optimize_structure(cleaned)
            
            # 最终格式化
            cleaned = self._final_formatting(cleaned)
            
            logger.info(f"代码清理完成，大小从 {len(html_content)} 字节减少到 {len(cleaned)} 字节")
            return cleaned
            
        except Exception as e:
            logger.warning(f"代码清理失败，返回原内容: {e}")
            return html_content
    
    def _basic_cleanup(self, content: str) -> str:
        """基础清理"""
        # 移除多余的空行
        content = self.html_patterns['extra_lines'].sub('\n\n', content)
        
        # 移除调试代码
        content = self.html_patterns['debug_code'].sub('', content)
        
        # 移除console.log
        content = self.html_patterns['console_logs'].sub('', content)
        
        return content
    
    def _clean_css_section(self, content: str) -> str:
        """清理CSS部分"""
        css_pattern = re.compile(r'<style>(.*?)</style>', re.DOTALL)
        
        def clean_css(match):
            css_content = match.group(1)
            
            # 移除CSS注释
            css_content = re.sub(r'/\*.*?\*/', '', css_content, flags=re.DOTALL)
            
            # 压缩CSS（移除多余空格）
            css_content = re.sub(r'\s+', ' ', css_content)
            css_content = re.sub(r';\s*}', '}', css_content)
            css_content = re.sub(r'{\s*', '{', css_content)
            css_content = re.sub(r';\s*', ';', css_content)
            
            return f'<style>{css_content.strip()}</style>'
        
        return css_pattern.sub(clean_css, content)
    
    def _clean_js_section(self, content: str) -> str:
        """清理JavaScript部分"""
        js_pattern = re.compile(r'<script(?![^>]*src)>(.*?)</script>', re.DOTALL)
        
        def clean_js(match):
            js_content = match.group(1)
            
            # 移除单行注释（保留URL中的//）
            js_content = re.sub(r'(?<!:)//(?![/\w]).*$', '', js_content, flags=re.MULTILINE)
            
            # 移除多行注释
            js_content = re.sub(r'/\*.*?\*/', '', js_content, flags=re.DOTALL)
            
            # 移除多余的空行
            js_content = re.sub(r'\n\s*\n\s*\n', '\n\n', js_content)
            
            # 移除行尾分号前的空格
            js_content = re.sub(r'\s*;\s*$', ';', js_content, flags=re.MULTILINE)
            
            return f'<script>{js_content.strip()}</script>'
        
        return js_pattern.sub(clean_js, content)
    
    def _optimize_structure(self, content: str) -> str:
        """优化HTML结构"""
        # 确保DOCTYPE声明正确
        if not content.strip().startswith('<!DOCTYPE html>'):
            content = '<!DOCTYPE html>\n' + content.lstrip()
        
        # 优化meta标签顺序
        content = self._optimize_meta_tags(content)
        
        # 确保脚本加载顺序正确
        content = self._optimize_script_order(content)
        
        return content
    
    def _optimize_meta_tags(self, content: str) -> str:
        """优化meta标签"""
        # 确保基础meta标签存在
        required_meta = [
            '<meta charset="UTF-8">',
            '<meta name="viewport" content="width=device-width, initial-scale=1.0">'
        ]
        
        head_pattern = re.compile(r'<head>(.*?)</head>', re.DOTALL)
        
        def optimize_head(match):
            head_content = match.group(1)
            
            # 检查并添加缺失的meta标签
            for meta in required_meta:
                if meta not in head_content:
                    head_content = meta + '\n' + head_content
            
            return f'<head>{head_content}</head>'
        
        return head_pattern.sub(optimize_head, content)
    
    def _optimize_script_order(self, content: str) -> str:
        """优化脚本加载顺序"""
        # 确保外部脚本在内联脚本之前
        body_pattern = re.compile(r'<body>(.*?)</body>', re.DOTALL)
        
        def optimize_body(match):
            body_content = match.group(1)
            
            # 提取外部脚本和内联脚本
            external_scripts = re.findall(r'<script[^>]*src[^>]*></script>', body_content)
            inline_scripts = re.findall(r'<script(?![^>]*src)>.*?</script>', body_content, re.DOTALL)
            
            # 移除原有脚本
            body_content = re.sub(r'<script.*?</script>', '', body_content, flags=re.DOTALL)
            
            # 重新添加脚本（外部在前，内联在后）
            scripts_html = '\n'.join(external_scripts + inline_scripts)
            body_content = body_content.rstrip() + '\n' + scripts_html
            
            return f'<body>{body_content}</body>'
        
        return body_pattern.sub(optimize_body, content)
    
    def _final_formatting(self, content: str) -> str:
        """最终格式化"""
        # 统一缩进（使用4个空格）
        lines = content.split('\n')
        formatted_lines = []
        indent_level = 0
        
        for line in lines:
            stripped = line.strip()
            
            if not stripped:
                formatted_lines.append('')
                continue
            
            # 调整缩进级别
            if stripped.startswith('</') and not stripped.startswith('</script>') and not stripped.startswith('</style>'):
                indent_level = max(0, indent_level - 1)
            
            # 添加缩进
            formatted_lines.append('    ' * indent_level + stripped)
            
            # 增加缩进级别
            if (stripped.startswith('<') and not stripped.startswith('</') 
                and not stripped.endswith('/>') and not stripped.endswith('>')
                and '>' in stripped):
                indent_level += 1
            elif (stripped.startswith('<') and not stripped.startswith('</')
                  and not stripped.endswith('/>') and stripped.endswith('>')):
                if not any(tag in stripped for tag in ['<meta', '<link', '<img', '<input', '<br', '<hr']):
                    indent_level += 1
        
        return '\n'.join(formatted_lines)
    
    def validate_html_structure(self, html_content: str) -> Dict[str, Any]:
        """
        验证HTML结构
        
        Args:
            html_content: HTML内容
            
        Returns:
            验证结果
        """
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'stats': {}
        }
        
        try:
            # 检查基本结构
            if '<!DOCTYPE html>' not in html_content:
                validation_result['warnings'].append('缺少DOCTYPE声明')
            
            if '<html' not in html_content or '</html>' not in html_content:
                validation_result['errors'].append('缺少html标签')
                validation_result['is_valid'] = False
            
            if '<head>' not in html_content or '</head>' not in html_content:
                validation_result['errors'].append('缺少head标签')
                validation_result['is_valid'] = False
            
            if '<body>' not in html_content or '</body>' not in html_content:
                validation_result['errors'].append('缺少body标签')
                validation_result['is_valid'] = False
            
            # 检查必要的meta标签
            if 'charset' not in html_content:
                validation_result['warnings'].append('建议添加charset声明')
            
            if 'viewport' not in html_content:
                validation_result['warnings'].append('建议添加viewport meta标签')
            
            # 统计信息
            validation_result['stats'] = {
                'total_size': len(html_content),
                'script_tags': len(re.findall(r'<script', html_content)),
                'style_tags': len(re.findall(r'<style', html_content)),
                'has_phaser': 'phaser' in html_content.lower()
            }
            
        except Exception as e:
            validation_result['errors'].append(f'验证过程出错: {e}')
            validation_result['is_valid'] = False
        
        return validation_result
    
    def extract_game_info(self, html_content: str) -> Dict[str, Any]:
        """
        从HTML内容中提取游戏信息
        
        Args:
            html_content: HTML内容
            
        Returns:
            游戏信息
        """
        game_info = {
            'title': 'HTML5游戏',
            'framework': 'Unknown',
            'has_physics': False,
            'game_size': {'width': 800, 'height': 600},
            'features': []
        }
        
        try:
            # 提取标题
            title_match = re.search(r'<title>(.*?)</title>', html_content)
            if title_match:
                game_info['title'] = title_match.group(1)
            
            # 检测框架
            if 'phaser' in html_content.lower():
                game_info['framework'] = 'Phaser.js'
            elif 'pixi' in html_content.lower():
                game_info['framework'] = 'PIXI.js'
            elif 'three' in html_content.lower():
                game_info['framework'] = 'Three.js'
            
            # 检测物理引擎
            if 'physics' in html_content.lower() or 'arcade' in html_content.lower():
                game_info['has_physics'] = True
            
            # 提取游戏尺寸
            size_match = re.search(r'width:\s*(\d+).*?height:\s*(\d+)', html_content)
            if size_match:
                game_info['game_size'] = {
                    'width': int(size_match.group(1)),
                    'height': int(size_match.group(2))
                }
            
            # 检测功能特性
            if 'score' in html_content.lower():
                game_info['features'].append('计分系统')
            if 'timer' in html_content.lower() or 'time' in html_content.lower():
                game_info['features'].append('计时器')
            if 'collision' in html_content.lower():
                game_info['features'].append('碰撞检测')
            if 'sound' in html_content.lower() or 'audio' in html_content.lower():
                game_info['features'].append('音效系统')
            
        except Exception as e:
            logger.warning(f"提取游戏信息失败: {e}")
        
        return game_info 