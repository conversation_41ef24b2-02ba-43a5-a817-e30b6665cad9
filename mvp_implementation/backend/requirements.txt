# crewAI多智能体Playable广告生成系统 - MVP版本
# Python依赖包列表

# 核心框架
crewai>=0.28.8
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
pydantic>=2.5.0

# AI和机器学习
openai>=1.3.0
langchain>=0.0.350
langchain-openai>=0.0.2
langchain-community>=0.0.10

# 数据处理
pandas>=2.1.0
numpy>=1.24.0
python-multipart>=0.0.6

# 文件处理
aiofiles>=23.2.1
Pillow>=10.1.0

# 网络和HTTP
httpx>=0.25.0
requests>=2.31.0

# 模板引擎
jinja2>=3.1.2

# 配置管理
python-dotenv>=1.0.0
pyyaml>=6.0.1

# 日志和监控
loguru>=0.7.2

# 测试框架
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# 代码质量
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0

# 开发工具
watchdog>=3.0.0

# 数据库（可选）
sqlalchemy>=2.0.0
alembic>=1.12.0

# 缓存（可选）
redis>=5.0.0

# 安全
cryptography>=41.0.0
passlib[bcrypt]>=1.7.4
python-jose[cryptography]>=3.3.0

# 其他工具
click>=8.1.0
rich>=13.0.0
tqdm>=4.66.0