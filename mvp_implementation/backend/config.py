#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
crewAI多智能体Playable广告生成系统 - 配置管理
MVP版本 - 系统配置
"""

import os
from typing import Dict, Any, List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Settings(BaseSettings):
    """系统配置类"""
    
    # 基础配置
    app_name: str = Field(default="crewAI Playable广告生成系统", description="应用名称")
    app_version: str = Field(default="1.0.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")
    environment: str = Field(default="development", description="运行环境")
    
    # 服务器配置
    host: str = Field(default="0.0.0.0", description="服务器地址")
    port: int = Field(default=8000, description="服务器端口")
    workers: int = Field(default=1, description="工作进程数")
    
    # API配置
    api_prefix: str = Field(default="/api", description="API前缀")
    docs_url: str = Field(default="/docs", description="文档URL")
    redoc_url: str = Field(default="/redoc", description="ReDoc URL")
    
    # OpenAI配置
    openai_api_key: str = Field(default="", description="OpenAI API密钥")
    openai_api_base: str = Field(default="http://oneapi.funplus.com/v1", description="OpenAI API基础URL")
    openai_model: str = Field(default="gpt-4.1", description="默认OpenAI模型")
    openai_temperature: float = Field(default=0.2, description="生成温度")
    openai_max_tokens: int = Field(default=4000, description="最大token数")
    
    # crewAI配置
    crew_verbose: bool = Field(default=True, description="crewAI详细输出")
    crew_memory: bool = Field(default=True, description="启用crewAI记忆")
    crew_max_iter: int = Field(default=5, description="最大迭代次数")
    crew_max_execution_time: int = Field(default=300, description="最大执行时间（秒）")
    
    # 文件存储配置
    static_dir: str = Field(default="static", description="静态文件目录")
    upload_dir: str = Field(default="uploads", description="上传文件目录")
    output_dir: str = Field(default="outputs", description="输出文件目录")
    max_file_size: int = Field(default=10 * 1024 * 1024, description="最大文件大小（字节）")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_file: str = Field(default="app.log", description="日志文件")
    log_rotation: str = Field(default="1 day", description="日志轮转")
    log_retention: str = Field(default="30 days", description="日志保留")
    
    # 数据库配置（可选）
    database_url: str = Field(default="sqlite:///./app.db", description="数据库URL")
    database_echo: bool = Field(default=False, description="数据库SQL输出")
    
    # Redis配置（可选）
    redis_url: str = Field(default="redis://localhost:6379/0", description="Redis URL")
    redis_password: str = Field(default="", description="Redis密码")
    
    # 安全配置
    secret_key: str = Field(default="your-secret-key-here", description="密钥")
    access_token_expire_minutes: int = Field(default=30, description="访问令牌过期时间")
    
    # CORS配置
    cors_origins: List[str] = Field(default=["*"], description="CORS允许的源")
    cors_methods: List[str] = Field(default=["*"], description="CORS允许的方法")
    cors_headers: List[str] = Field(default=["*"], description="CORS允许的头部")
    
    # 项目配置
    max_concurrent_projects: int = Field(default=5, description="最大并发项目数")
    project_timeout: int = Field(default=600, description="项目超时时间（秒）")
    cleanup_interval: int = Field(default=3600, description="清理间隔（秒）")
    
    # 代码生成配置
    code_templates_dir: str = Field(default="templates", description="代码模板目录")
    supported_frameworks: List[str] = Field(default=["phaser", "pixijs", "threejs"], description="支持的框架")
    default_framework: str = Field(default="phaser", description="默认框架")
    
    # 质量控制配置
    quality_threshold: float = Field(default=0.8, description="质量阈值")
    enable_code_review: bool = Field(default=True, description="启用代码审查")
    enable_testing: bool = Field(default=True, description="启用测试")
    
    # 监控配置
    enable_metrics: bool = Field(default=True, description="启用指标收集")
    metrics_interval: int = Field(default=60, description="指标收集间隔（秒）")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

# 创建全局配置实例
settings = Settings()

# 智能体配置
AGENT_CONFIGS = {
    "product_manager": {
        "role": "产品经理",
        "goal": "分析用户需求，制定产品规格和功能要求",
        "backstory": "你是一位经验丰富的产品经理，专门负责Playable广告产品的需求分析和规格制定。",
        "verbose": settings.crew_verbose,
        "allow_delegation": False,
        "max_iter": settings.crew_max_iter,
        "memory": settings.crew_memory
    },
    "tech_architect": {
        "role": "技术架构师",
        "goal": "设计技术架构，制定实现方案",
        "backstory": "你是一位资深的技术架构师，专门负责Playable广告的技术架构设计和实现方案制定。",
        "verbose": settings.crew_verbose,
        "allow_delegation": False,
        "max_iter": settings.crew_max_iter,
        "memory": settings.crew_memory
    },
    "code_generator": {
        "role": "代码生成器",
        "goal": "根据技术方案生成高质量的代码",
        "backstory": "你是一位专业的代码生成专家，能够根据技术架构生成完整、可运行的Playable广告代码。",
        "verbose": settings.crew_verbose,
        "allow_delegation": False,
        "max_iter": settings.crew_max_iter,
        "memory": settings.crew_memory
    },

    "project_coordinator": {
        "role": "项目协调员",
        "goal": "协调各个智能体，管理项目流程",
        "backstory": "你是一位经验丰富的项目协调员，负责整个Playable广告开发流程的协调和管理。",
        "verbose": settings.crew_verbose,
        "allow_delegation": True,
        "max_iter": settings.crew_max_iter,
        "memory": settings.crew_memory
    }
}

# 任务配置
TASK_CONFIGS = {
    "requirement_analysis": {
        "name": "需求分析",
        "description": "分析用户输入，提取核心需求和功能要求",
        "expected_output": "详细的需求分析报告，包括功能列表、技术要求和约束条件",
        "timeout": 120
    },
    "architecture_design": {
        "name": "架构设计",
        "description": "基于需求分析结果，设计技术架构和实现方案",
        "expected_output": "完整的技术架构文档，包括组件设计、数据流和接口定义",
        "timeout": 180
    },
    "code_generation": {
        "name": "代码生成",
        "description": "根据架构设计生成完整的Playable广告代码",
        "expected_output": "可运行的HTML5游戏代码，包括HTML、CSS、JavaScript文件",
        "timeout": 300
    },

    "project_coordination": {
        "name": "项目协调",
        "description": "协调整个开发流程，确保各阶段顺利进行",
        "expected_output": "项目执行报告，包括进度状态、问题记录和最终交付物",
        "timeout": 60
    }
}

# 代码模板配置
CODE_TEMPLATES = {
    "phaser": {
        "name": "Phaser.js",
        "version": "3.70.0",
        "description": "基于Phaser.js的HTML5游戏框架",
        "template_files": {
            "index.html": "phaser_index.html",
            "game.js": "phaser_game.js",
            "style.css": "phaser_style.css"
        },
        "cdn_links": {
            "phaser": "https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"
        }
    },
    "pixijs": {
        "name": "PixiJS",
        "version": "7.3.0",
        "description": "基于PixiJS的2D渲染引擎",
        "template_files": {
            "index.html": "pixi_index.html",
            "game.js": "pixi_game.js",
            "style.css": "pixi_style.css"
        },
        "cdn_links": {
            "pixi": "https://cdn.jsdelivr.net/npm/pixi.js@7.3.0/dist/pixi.min.js"
        }
    }
}

# 质量指标配置
QUALITY_METRICS = {
    "code_quality": {
        "syntax_correctness": {"weight": 0.3, "threshold": 0.95},
        "code_structure": {"weight": 0.2, "threshold": 0.8},
        "best_practices": {"weight": 0.2, "threshold": 0.8},
        "documentation": {"weight": 0.1, "threshold": 0.7},
        "performance": {"weight": 0.2, "threshold": 0.8}
    },
    "functionality": {
        "requirement_coverage": {"weight": 0.4, "threshold": 0.9},
        "feature_completeness": {"weight": 0.3, "threshold": 0.8},
        "user_experience": {"weight": 0.2, "threshold": 0.8},
        "compatibility": {"weight": 0.1, "threshold": 0.9}
    },
    "overall": {
        "minimum_score": 0.8,
        "target_score": 0.9
    }
}

# 错误处理配置
ERROR_HANDLING = {
    "max_retries": 3,
    "retry_delay": 5,
    "timeout_handling": "graceful",
    "error_reporting": True,
    "fallback_strategies": {
        "code_generation_failure": "use_template",
        "quality_check_failure": "manual_review",
        "timeout": "partial_delivery"
    }
}

# 性能配置
PERFORMANCE_CONFIG = {
    "async_processing": True,
    "parallel_tasks": True,
    "cache_enabled": True,
    "cache_ttl": 3600,
    "batch_processing": False,
    "resource_limits": {
        "memory_limit": "1GB",
        "cpu_limit": "80%",
        "disk_limit": "10GB"
    }
}

# 获取配置的辅助函数
def get_agent_config(agent_name: str) -> Dict[str, Any]:
    """获取智能体配置"""
    return AGENT_CONFIGS.get(agent_name, {})

def get_task_config(task_name: str) -> Dict[str, Any]:
    """获取任务配置"""
    return TASK_CONFIGS.get(task_name, {})

def get_template_config(framework: str) -> Dict[str, Any]:
    """获取模板配置"""
    return CODE_TEMPLATES.get(framework, CODE_TEMPLATES[settings.default_framework])

def get_quality_config() -> Dict[str, Any]:
    """获取质量配置"""
    return QUALITY_METRICS

def validate_config() -> bool:
    """验证配置完整性"""
    try:
        # 检查必要的配置项
        required_configs = [
            settings.openai_api_key,
            settings.secret_key
        ]
        
        for config in required_configs:
            if not config or config == "your-secret-key-here":
                return False
        
        # 检查目录是否存在
        directories = [
            settings.static_dir,
            settings.upload_dir,
            settings.output_dir
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
        
        return True
        
    except Exception as e:
        print(f"配置验证失败: {e}")
        return False

# 创建Config类别名
Config = settings

# 导出配置
__all__ = [
    "settings",
    "Config",
    "AGENT_CONFIGS",
    "TASK_CONFIGS",
    "CODE_TEMPLATES",
    "QUALITY_METRICS",
    "ERROR_HANDLING",
    "PERFORMANCE_CONFIG",
    "get_agent_config",
    "get_task_config",
    "get_template_config",
    "get_quality_config",
    "validate_config"
]