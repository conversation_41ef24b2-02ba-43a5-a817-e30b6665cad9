#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
crewAI多智能体Playable广告生成系统 - 启动脚本
MVP版本 - 系统启动和管理
"""

import os
import sys
import argparse
import subprocess
import signal
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.config import settings, validate_config

def check_dependencies():
    """检查依赖包"""
    print("检查Python依赖包...")
    
    required_packages = [
        'crewai',
        'fastapi',
        'uvicorn',
        'openai',
        'langchain',
        'pydantic'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺失的依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("所有依赖包检查完成!")
    return True

def check_environment():
    """检查环境配置"""
    print("\n检查环境配置...")
    
    # 检查.env文件
    env_file = project_root / ".env"
    if not env_file.exists():
        print("✗ .env文件不存在")
        print("请复制.env.example为.env并配置相关参数")
        return False
    else:
        print("✓ .env文件存在")
    
    # 验证配置
    if not validate_config():
        print("✗ 配置验证失败")
        print("请检查.env文件中的配置项")
        return False
    else:
        print("✓ 配置验证通过")
    
    # 检查OpenAI API密钥
    if not settings.openai_api_key or settings.openai_api_key == "your-openai-api-key-here":
        print("✗ OpenAI API密钥未配置")
        print("请在.env文件中设置OPENAI_API_KEY")
        return False
    else:
        print("✓ OpenAI API密钥已配置")
    
    # 检查必要目录
    directories = [
        settings.static_dir,
        settings.upload_dir,
        settings.output_dir
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"✓ 创建目录: {directory}")
        else:
            print(f"✓ 目录存在: {directory}")
    
    print("环境配置检查完成!")
    return True

def install_dependencies():
    """安装依赖包"""
    print("安装Python依赖包...")
    
    requirements_file = project_root / "requirements.txt"
    if not requirements_file.exists():
        print("requirements.txt文件不存在")
        return False
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], check=True)
        print("依赖包安装完成!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"依赖包安装失败: {e}")
        return False

def start_server(host=None, port=None, reload=False, workers=None):
    """启动服务器"""
    print("启动crewAI多智能体Playable广告生成系统...")
    
    # 使用配置或命令行参数
    host = host or settings.host
    port = port or settings.port
    workers = workers or settings.workers
    
    # 构建启动命令
    cmd = [
        sys.executable, "-m", "uvicorn",
        "app.main:app",
        "--host", host,
        "--port", str(port)
    ]
    
    if reload:
        cmd.append("--reload")
    else:
        cmd.extend(["--workers", str(workers)])
    
    if settings.log_level:
        cmd.extend(["--log-level", settings.log_level.lower()])
    
    print(f"启动命令: {' '.join(cmd)}")
    print(f"服务器地址: http://{host}:{port}")
    print(f"API文档: http://{host}:{port}/docs")
    print("按 Ctrl+C 停止服务器")
    
    try:
        # 启动服务器
        process = subprocess.Popen(cmd, cwd=project_root)
        
        # 等待中断信号
        def signal_handler(signum, frame):
            print("\n正在停止服务器...")
            process.terminate()
            process.wait()
            print("服务器已停止")
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # 等待进程结束
        process.wait()
        
    except Exception as e:
        print(f"启动服务器失败: {e}")
        return False
    
    return True

def run_tests():
    """运行测试"""
    print("运行测试套件...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pytest",
            "tests/",
            "-v",
            "--cov=app",
            "--cov-report=html",
            "--cov-report=term"
        ], check=True, cwd=project_root)
        print("测试完成!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"测试失败: {e}")
        return False
    except FileNotFoundError:
        print("pytest未安装，跳过测试")
        return False

def format_code():
    """格式化代码"""
    print("格式化代码...")
    
    try:
        # 使用black格式化
        subprocess.run([
            sys.executable, "-m", "black",
            "app/",
            "tests/",
            "--line-length", "88"
        ], check=True, cwd=project_root)
        
        # 使用isort排序导入
        subprocess.run([
            sys.executable, "-m", "isort",
            "app/",
            "tests/",
            "--profile", "black"
        ], check=True, cwd=project_root)
        
        print("代码格式化完成!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"代码格式化失败: {e}")
        return False
    except FileNotFoundError:
        print("代码格式化工具未安装")
        return False

def lint_code():
    """代码检查"""
    print("代码质量检查...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "flake8",
            "app/",
            "--max-line-length", "88",
            "--extend-ignore", "E203,W503"
        ], check=True, cwd=project_root)
        print("代码质量检查通过!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"代码质量检查发现问题: {e}")
        return False
    except FileNotFoundError:
        print("flake8未安装，跳过代码检查")
        return False

def show_status():
    """显示系统状态"""
    print("=== crewAI多智能体Playable广告生成系统状态 ===")
    print(f"项目路径: {project_root}")
    print(f"Python版本: {sys.version}")
    print(f"配置环境: {settings.environment}")
    print(f"调试模式: {settings.debug}")
    print(f"服务器地址: {settings.host}:{settings.port}")
    print(f"OpenAI模型: {settings.openai_model}")
    print(f"支持的框架: {settings.supported_frameworks}")
    print(f"默认框架: {settings.default_framework}")
    print("="*50)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="crewAI多智能体Playable广告生成系统 - 启动脚本"
    )
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 启动服务器命令
    start_parser = subparsers.add_parser("start", help="启动服务器")
    start_parser.add_argument("--host", default=None, help="服务器地址")
    start_parser.add_argument("--port", type=int, default=None, help="服务器端口")
    start_parser.add_argument("--reload", action="store_true", help="启用热重载")
    start_parser.add_argument("--workers", type=int, default=None, help="工作进程数")
    
    # 其他命令
    subparsers.add_parser("install", help="安装依赖包")
    subparsers.add_parser("check", help="检查环境")
    subparsers.add_parser("test", help="运行测试")
    subparsers.add_parser("format", help="格式化代码")
    subparsers.add_parser("lint", help="代码质量检查")
    subparsers.add_parser("status", help="显示系统状态")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 执行命令
    if args.command == "start":
        if not check_dependencies():
            print("\n请先安装依赖包: python start.py install")
            return
        
        if not check_environment():
            print("\n请先配置环境")
            return
        
        start_server(
            host=args.host,
            port=args.port,
            reload=args.reload,
            workers=args.workers
        )
    
    elif args.command == "install":
        install_dependencies()
    
    elif args.command == "check":
        deps_ok = check_dependencies()
        env_ok = check_environment()
        
        if deps_ok and env_ok:
            print("\n✓ 系统检查通过，可以启动服务器")
        else:
            print("\n✗ 系统检查失败，请解决上述问题")
    
    elif args.command == "test":
        run_tests()
    
    elif args.command == "format":
        format_code()
    
    elif args.command == "lint":
        lint_code()
    
    elif args.command == "status":
        show_status()

if __name__ == "__main__":
    main()