#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板渲染工具
负责处理游戏模板的渲染、验证和生成
"""

import os
import json
import zipfile
import tempfile
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import re

from jinja2 import Environment, FileSystemLoader, Template
from jinja2.exceptions import TemplateError, TemplateSyntaxError

from config import Config
from utils.logger import get_logger

logger = get_logger(__name__)

class TemplateRenderer:
    """模板渲染器"""
    
    def __init__(self):
        self.config = Config
        self.templates_dir = Path("templates")
        self.output_dir = Path(self.config.output_dir)
        
        # 确保目录存在
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化Jinja2环境
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.templates_dir)),
            autoescape=True,
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # 加载游戏模板配置
        self.game_templates = self._load_game_templates()
        
        logger.info(f"模板渲染器初始化完成，模板目录: {self.templates_dir}")
    
    def _load_game_templates(self) -> Dict[str, Any]:
        """加载游戏模板配置"""
        try:
            templates_config_path = self.templates_dir / "game_templates.json"
            if not templates_config_path.exists():
                logger.warning(f"游戏模板配置文件不存在: {templates_config_path}")
                return {}
            
            with open(templates_config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            logger.info(f"成功加载 {len(config.get('templates', {}))} 个游戏模板")
            return config
        
        except Exception as e:
            logger.error(f"加载游戏模板配置失败: {e}")
            return {}
    
    def get_available_templates(self) -> List[Dict[str, Any]]:
        """获取可用的游戏模板列表"""
        templates = []
        
        for template_id, template_config in self.game_templates.get('templates', {}).items():
            templates.append({
                'id': template_id,
                'name': template_config.get('name', template_id),
                'description': template_config.get('description', ''),
                'customizable_fields': template_config.get('customizable_fields', [])
            })
        
        return templates
    
    def get_template_config(self, template_id: str) -> Optional[Dict[str, Any]]:
        """获取指定模板的配置"""
        return self.game_templates.get('templates', {}).get(template_id)
    
    def validate_template_params(self, template_id: str, params: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证模板参数"""
        errors = []
        
        # 获取模板配置
        template_config = self.get_template_config(template_id)
        if not template_config:
            errors.append(f"未找到模板: {template_id}")
            return False, errors
        
        # 验证必填字段
        customizable_fields = template_config.get('customizable_fields', [])
        for field_config in customizable_fields:
            field_name = field_config['field']
            field_type = field_config['type']
            required = field_config.get('required', False)
            
            if required and field_name not in params:
                errors.append(f"缺少必填字段: {field_name}")
                continue
            
            if field_name in params:
                value = params[field_name]
                
                # 类型验证
                if field_type == 'string' and not isinstance(value, str):
                    errors.append(f"字段 {field_name} 必须是字符串类型")
                elif field_type == 'integer' and not isinstance(value, int):
                    errors.append(f"字段 {field_name} 必须是整数类型")
                elif field_type == 'boolean' and not isinstance(value, bool):
                    errors.append(f"字段 {field_name} 必须是布尔类型")
                elif field_type == 'color' and not self._validate_color(value):
                    errors.append(f"字段 {field_name} 必须是有效的颜色值 (如: #FF0000)")
                
                # 范围验证
                if field_type == 'integer':
                    min_val = field_config.get('min')
                    max_val = field_config.get('max')
                    if min_val is not None and value < min_val:
                        errors.append(f"字段 {field_name} 不能小于 {min_val}")
                    if max_val is not None and value > max_val:
                        errors.append(f"字段 {field_name} 不能大于 {max_val}")
                
                # 字符串长度验证
                if field_type == 'string':
                    validation_rules = self.game_templates.get('validation_rules', {})
                    if field_name in validation_rules:
                        rules = validation_rules[field_name]
                        min_length = rules.get('min_length')
                        max_length = rules.get('max_length')
                        pattern = rules.get('pattern')
                        
                        if min_length is not None and len(value) < min_length:
                            errors.append(f"字段 {field_name} 长度不能少于 {min_length} 个字符")
                        if max_length is not None and len(value) > max_length:
                            errors.append(f"字段 {field_name} 长度不能超过 {max_length} 个字符")
                        if pattern and not re.match(pattern, value):
                            errors.append(f"字段 {field_name} 格式不正确")
        
        return len(errors) == 0, errors
    
    def _validate_color(self, color: str) -> bool:
        """验证颜色值格式"""
        if not isinstance(color, str):
            return False
        
        color_pattern = self.game_templates.get('validation_rules', {}).get('color_format', '^#[0-9A-Fa-f]{6}$')
        return bool(re.match(color_pattern, color))
    
    def render_game(self, template_id: str, params: Dict[str, Any], output_format: str = 'html') -> Tuple[bool, str, Optional[str]]:
        """渲染游戏
        
        Args:
            template_id: 模板ID
            params: 渲染参数
            output_format: 输出格式 ('html' 或 'zip')
        
        Returns:
            (成功标志, 消息, 输出文件路径)
        """
        try:
            # 验证模板ID
            template_config = self.get_template_config(template_id)
            if not template_config:
                return False, f"未找到模板: {template_id}", None
            
            # 验证参数
            is_valid, errors = self.validate_template_params(template_id, params)
            if not is_valid:
                return False, f"参数验证失败: {'; '.join(errors)}", None
            
            # 合并默认配置和用户参数
            render_params = template_config.get('default_config', {}).copy()
            render_params.update(params)
            
            # 添加生成时间和其他元数据
            render_params['generation_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            render_params['template_id'] = template_id
            render_params['game_config'] = json.dumps(render_params, ensure_ascii=False, indent=2)
            
            # 创建输出目录
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            project_name = f"{template_id}_{timestamp}"
            project_dir = self.output_dir / project_name
            project_dir.mkdir(parents=True, exist_ok=True)
            
            # 渲染所有文件
            template_files = template_config.get('files', {})
            rendered_files = []
            
            for output_filename, template_filename in template_files.items():
                try:
                    # 渲染模板
                    template = self.jinja_env.get_template(template_filename)
                    rendered_content = template.render(**render_params)
                    
                    # 写入文件
                    output_path = project_dir / output_filename
                    with open(output_path, 'w', encoding='utf-8') as f:
                        f.write(rendered_content)
                    
                    rendered_files.append(output_filename)
                    logger.info(f"成功渲染文件: {output_filename}")
                
                except TemplateError as e:
                    logger.error(f"渲染模板 {template_filename} 失败: {e}")
                    return False, f"渲染模板失败: {e}", None
            
            # 生成项目信息文件
            project_info = {
                'template_id': template_id,
                'template_name': template_config.get('name', template_id),
                'generation_time': render_params['generation_time'],
                'parameters': params,
                'files': rendered_files,
                'framework': self.game_templates.get('generation_metadata', {}).get('framework', 'Phaser.js'),
                'version': self.game_templates.get('generation_metadata', {}).get('version', '1.0.0')
            }
            
            project_info_path = project_dir / 'project_info.json'
            with open(project_info_path, 'w', encoding='utf-8') as f:
                json.dump(project_info, f, ensure_ascii=False, indent=2)
            
            # 根据输出格式处理
            if output_format == 'zip':
                zip_path = self._create_zip_package(project_dir, project_name)
                return True, f"游戏生成成功，包含 {len(rendered_files)} 个文件", str(zip_path)
            else:
                # HTML格式，返回主文件路径
                main_file = project_dir / 'index.html'
                if main_file.exists():
                    return True, f"游戏生成成功，包含 {len(rendered_files)} 个文件", str(main_file)
                else:
                    return True, f"游戏生成成功，包含 {len(rendered_files)} 个文件", str(project_dir)
        
        except Exception as e:
            logger.error(f"渲染游戏失败: {e}")
            return False, f"渲染失败: {str(e)}", None
    
    def _create_zip_package(self, project_dir: Path, project_name: str) -> Path:
        """创建ZIP包"""
        zip_path = self.output_dir / f"{project_name}.zip"
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in project_dir.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(project_dir)
                    zipf.write(file_path, arcname)
        
        logger.info(f"创建ZIP包: {zip_path}")
        return zip_path
    
    def quick_generate(self, template_id: str, title: str = None) -> Tuple[bool, str, Optional[str]]:
        """快速生成游戏（使用默认参数）"""
        template_config = self.get_template_config(template_id)
        if not template_config:
            return False, f"未找到模板: {template_id}", None
        
        # 使用默认配置，只覆盖标题
        params = {}
        if title:
            params['game_title'] = title
        
        return self.render_game(template_id, params, 'html')
    
    def get_template_preview(self, template_id: str) -> Optional[Dict[str, Any]]:
        """获取模板预览信息"""
        template_config = self.get_template_config(template_id)
        if not template_config:
            return None
        
        return {
            'id': template_id,
            'name': template_config.get('name', template_id),
            'description': template_config.get('description', ''),
            'default_config': template_config.get('default_config', {}),
            'customizable_fields': template_config.get('customizable_fields', []),
            'files': list(template_config.get('files', {}).keys())
        }
    
    def validate_template_files(self, template_id: str) -> Tuple[bool, List[str]]:
        """验证模板文件是否存在"""
        errors = []
        
        template_config = self.get_template_config(template_id)
        if not template_config:
            errors.append(f"未找到模板配置: {template_id}")
            return False, errors
        
        template_files = template_config.get('files', {})
        for output_filename, template_filename in template_files.items():
            template_path = self.templates_dir / template_filename
            if not template_path.exists():
                errors.append(f"模板文件不存在: {template_filename}")
        
        return len(errors) == 0, errors
    
    def get_render_statistics(self) -> Dict[str, Any]:
        """获取渲染统计信息"""
        try:
            # 统计输出目录中的项目数量
            project_count = len([d for d in self.output_dir.iterdir() if d.is_dir()])
            zip_count = len(list(self.output_dir.glob('*.zip')))
            
            # 统计各模板的使用次数（基于输出目录命名）
            template_usage = {}
            for item in self.output_dir.iterdir():
                if item.is_dir() or item.suffix == '.zip':
                    name_parts = item.stem.split('_')
                    if name_parts:
                        template_id = name_parts[0]
                        template_usage[template_id] = template_usage.get(template_id, 0) + 1
            
            return {
                'total_projects': project_count,
                'total_zip_packages': zip_count,
                'template_usage': template_usage,
                'available_templates': len(self.game_templates.get('templates', {})),
                'output_directory': str(self.output_dir),
                'templates_directory': str(self.templates_dir)
            }
        
        except Exception as e:
            logger.error(f"获取渲染统计信息失败: {e}")
            return {}
    
    def cleanup_old_files(self, days: int = 7) -> int:
        """清理旧的生成文件"""
        try:
            import time
            
            current_time = time.time()
            cutoff_time = current_time - (days * 24 * 60 * 60)
            
            cleaned_count = 0
            
            for item in self.output_dir.iterdir():
                try:
                    if item.stat().st_mtime < cutoff_time:
                        if item.is_dir():
                            import shutil
                            shutil.rmtree(item)
                        else:
                            item.unlink()
                        cleaned_count += 1
                        logger.info(f"清理旧文件: {item}")
                except Exception as e:
                    logger.warning(f"清理文件失败 {item}: {e}")
            
            logger.info(f"清理完成，删除了 {cleaned_count} 个文件/目录")
            return cleaned_count
        
        except Exception as e:
            logger.error(f"清理旧文件失败: {e}")
            return 0

# 创建全局实例
template_renderer = TemplateRenderer()

# 导出主要函数
__all__ = [
    'TemplateRenderer',
    'template_renderer'
]