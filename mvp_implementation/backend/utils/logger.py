#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志工具模块
提供统一的日志配置和管理功能
"""

import os
import sys
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path
from typing import Optional
import json

class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',      # 青色
        'INFO': '\033[32m',       # 绿色
        'WARNING': '\033[33m',    # 黄色
        'ERROR': '\033[31m',      # 红色
        'CRITICAL': '\033[35m',   # 紫色
        'RESET': '\033[0m'        # 重置
    }
    
    def format(self, record):
        # 获取原始格式化结果
        log_message = super().format(record)
        
        # 添加颜色
        if record.levelname in self.COLORS:
            color = self.COLORS[record.levelname]
            reset = self.COLORS['RESET']
            log_message = f"{color}{log_message}{reset}"
        
        return log_message

class JSONFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
        if hasattr(record, 'project_id'):
            log_entry['project_id'] = record.project_id
        
        return json.dumps(log_entry, ensure_ascii=False)

class LoggerManager:
    """日志管理器"""
    
    def __init__(self):
        self.loggers = {}
        self.log_dir = None
        self.log_level = logging.INFO
        self.console_enabled = True
        self.file_enabled = True
        self.json_enabled = False
        self.max_file_size = 10 * 1024 * 1024  # 10MB
        self.backup_count = 5
        
        # 从环境变量读取配置
        self._load_config_from_env()
        
        # 创建日志目录
        self._setup_log_directory()
    
    def _load_config_from_env(self):
        """从环境变量加载配置"""
        # 日志级别
        log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
        self.log_level = getattr(logging, log_level, logging.INFO)
        
        # 日志目录
        self.log_dir = os.getenv('LOG_DIR', 'logs')
        
        # 控制台输出
        self.console_enabled = os.getenv('LOG_CONSOLE', 'true').lower() == 'true'
        
        # 文件输出
        self.file_enabled = os.getenv('LOG_FILE', 'true').lower() == 'true'
        
        # JSON格式
        self.json_enabled = os.getenv('LOG_JSON', 'false').lower() == 'true'
        
        # 文件大小限制
        try:
            self.max_file_size = int(os.getenv('LOG_MAX_FILE_SIZE', str(self.max_file_size)))
        except ValueError:
            pass
        
        # 备份文件数量
        try:
            self.backup_count = int(os.getenv('LOG_BACKUP_COUNT', str(self.backup_count)))
        except ValueError:
            pass
    
    def _setup_log_directory(self):
        """设置日志目录"""
        if self.file_enabled and self.log_dir:
            log_path = Path(self.log_dir)
            log_path.mkdir(parents=True, exist_ok=True)
    
    def get_logger(self, name: str, 
                   level: Optional[int] = None,
                   console: Optional[bool] = None,
                   file_output: Optional[bool] = None,
                   json_format: Optional[bool] = None) -> logging.Logger:
        """获取或创建日志器
        
        Args:
            name: 日志器名称
            level: 日志级别
            console: 是否输出到控制台
            file_output: 是否输出到文件
            json_format: 是否使用JSON格式
        
        Returns:
            配置好的日志器
        """
        if name in self.loggers:
            return self.loggers[name]
        
        # 创建日志器
        logger = logging.getLogger(name)
        logger.setLevel(level or self.log_level)
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 控制台处理器
        if console if console is not None else self.console_enabled:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(level or self.log_level)
            
            if json_format if json_format is not None else self.json_enabled:
                console_formatter = JSONFormatter()
            else:
                console_formatter = ColoredFormatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                )
            
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        
        # 文件处理器
        if (file_output if file_output is not None else self.file_enabled) and self.log_dir:
            # 主日志文件
            log_file = Path(self.log_dir) / f"{name}.log"
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            file_handler.setLevel(level or self.log_level)
            
            if json_format if json_format is not None else self.json_enabled:
                file_formatter = JSONFormatter()
            else:
                file_formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
                )
            
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
            
            # 错误日志文件
            error_log_file = Path(self.log_dir) / f"{name}_error.log"
            error_handler = logging.handlers.RotatingFileHandler(
                error_log_file,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(file_formatter)
            logger.addHandler(error_handler)
        
        # 防止重复日志
        logger.propagate = False
        
        # 缓存日志器
        self.loggers[name] = logger
        
        return logger
    
    def set_global_level(self, level: int):
        """设置全局日志级别"""
        self.log_level = level
        for logger in self.loggers.values():
            logger.setLevel(level)
            for handler in logger.handlers:
                handler.setLevel(level)
    
    def add_request_context(self, logger: logging.Logger, request_id: str, user_id: str = None):
        """为日志器添加请求上下文"""
        # 创建适配器来添加上下文信息
        class ContextAdapter(logging.LoggerAdapter):
            def process(self, msg, kwargs):
                return f"[{request_id}] {msg}", kwargs
        
        return ContextAdapter(logger, {'request_id': request_id, 'user_id': user_id})
    
    def get_log_files(self) -> list:
        """获取所有日志文件列表"""
        if not self.log_dir or not Path(self.log_dir).exists():
            return []
        
        log_files = []
        log_path = Path(self.log_dir)
        
        for file_path in log_path.glob('*.log*'):
            if file_path.is_file():
                stat = file_path.stat()
                log_files.append({
                    'name': file_path.name,
                    'path': str(file_path),
                    'size': stat.st_size,
                    'modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
                })
        
        return sorted(log_files, key=lambda x: x['modified'], reverse=True)
    
    def cleanup_old_logs(self, days: int = 30) -> int:
        """清理旧日志文件"""
        if not self.log_dir or not Path(self.log_dir).exists():
            return 0
        
        import time
        current_time = time.time()
        cutoff_time = current_time - (days * 24 * 60 * 60)
        
        cleaned_count = 0
        log_path = Path(self.log_dir)
        
        for file_path in log_path.glob('*.log*'):
            if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                try:
                    file_path.unlink()
                    cleaned_count += 1
                except Exception as e:
                    print(f"清理日志文件失败 {file_path}: {e}")
        
        return cleaned_count
    
    def get_logger_stats(self) -> dict:
        """获取日志器统计信息"""
        stats = {
            'total_loggers': len(self.loggers),
            'log_level': logging.getLevelName(self.log_level),
            'console_enabled': self.console_enabled,
            'file_enabled': self.file_enabled,
            'json_enabled': self.json_enabled,
            'log_directory': self.log_dir,
            'max_file_size': self.max_file_size,
            'backup_count': self.backup_count,
            'loggers': list(self.loggers.keys())
        }
        
        if self.log_dir and Path(self.log_dir).exists():
            log_files = self.get_log_files()
            stats['log_files_count'] = len(log_files)
            stats['total_log_size'] = sum(f['size'] for f in log_files)
        
        return stats

# 创建全局日志管理器实例
_logger_manager = LoggerManager()

# 便捷函数
def get_logger(name: str, **kwargs) -> logging.Logger:
    """获取日志器的便捷函数"""
    return _logger_manager.get_logger(name, **kwargs)

def set_global_log_level(level: int):
    """设置全局日志级别"""
    _logger_manager.set_global_level(level)

def get_log_files() -> list:
    """获取日志文件列表"""
    return _logger_manager.get_log_files()

def cleanup_old_logs(days: int = 30) -> int:
    """清理旧日志文件"""
    return _logger_manager.cleanup_old_logs(days)

def get_logger_stats() -> dict:
    """获取日志统计信息"""
    return _logger_manager.get_logger_stats()

def add_request_context(logger: logging.Logger, request_id: str, user_id: str = None):
    """添加请求上下文"""
    return _logger_manager.add_request_context(logger, request_id, user_id)

# 创建一些常用的日志器
app_logger = get_logger('app')
api_logger = get_logger('api')
agent_logger = get_logger('agent')
template_logger = get_logger('template')
performance_logger = get_logger('performance')

# 导出
__all__ = [
    'get_logger',
    'set_global_log_level',
    'get_log_files',
    'cleanup_old_logs',
    'get_logger_stats',
    'add_request_context',
    'LoggerManager',
    'ColoredFormatter',
    'JSONFormatter',
    'app_logger',
    'api_logger',
    'agent_logger',
    'template_logger',
    'performance_logger'
]