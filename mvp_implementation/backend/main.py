#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
crewAI多智能体Playable广告生成系统 - 主API服务
MVP版本 - 统一服务入口
"""

import os
import sys
import json
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, List, Optional

from fastapi import FastAPI, HTTPException, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field
import uvicorn

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入智能体
from app.agents import (
    ProductManagerAgent,
    CodeGeneratorAgent,
    ProjectCoordinatorAgent
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="crewAI Playable广告生成系统",
    description="基于crewAI的多智能体协作Playable广告自动生成系统 - MVP版本",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
static_dir = os.path.join(os.path.dirname(__file__), "static")
if not os.path.exists(static_dir):
    os.makedirs(static_dir)
app.mount("/static", StaticFiles(directory=static_dir), name="static")

# 全局变量
coordinator: Optional[ProjectCoordinatorAgent] = None
active_projects: Dict[str, Dict[str, Any]] = {}

# 请求模型
class ProjectCreateRequest(BaseModel):
    """项目创建请求"""
    user_input: str = Field(description="用户需求描述")
    project_type: str = Field(default="standard_playable_ad", description="项目类型")
    options: Optional[Dict[str, Any]] = Field(default={}, description="额外选项")

class ProjectStatusResponse(BaseModel):
    """项目状态响应"""
    project_id: str
    status: str
    progress: float
    current_phase: str
    estimated_completion: Optional[str]
    issues: List[str]
    next_actions: List[str]

class GenerationResult(BaseModel):
    """生成结果"""
    success: bool
    project_id: str
    deliverables: Dict[str, Any]
    quality_metrics: Dict[str, float]
    recommendations: List[str]
    execution_time: float

# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    global coordinator
    try:
        logger.info("正在初始化crewAI多智能体系统...")
        coordinator = ProjectCoordinatorAgent()
        logger.info("系统初始化完成")
    except Exception as e:
        logger.error(f"系统初始化失败: {e}")
        logger.error(traceback.format_exc())
        raise

# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理"""
    logger.info("系统正在关闭...")
    # 这里可以添加清理逻辑

# 异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    logger.error(f"未处理的异常: {exc}")
    logger.error(traceback.format_exc())
    return JSONResponse(
        status_code=500,
        content={
            "error": "内部服务器错误",
            "message": str(exc),
            "timestamp": datetime.now().isoformat()
        }
    )

# API路由
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "crewAI Playable广告生成系统 - MVP版本",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 检查系统组件状态
        system_status = {
            "coordinator": coordinator is not None,
            "active_projects": len(active_projects),
            "timestamp": datetime.now().isoformat()
        }
        
        if coordinator:
            system_status["agents"] = {
                "product_manager": hasattr(coordinator, 'product_manager'),
                "code_generator": hasattr(coordinator, 'code_generator')
            }
        
        return {
            "status": "healthy",
            "system": system_status
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.post("/api/projects", response_model=Dict[str, str])
async def create_project(request: ProjectCreateRequest):
    """创建新项目"""
    try:
        if not coordinator:
            raise HTTPException(status_code=503, detail="系统未初始化")
        
        logger.info(f"创建新项目: {request.user_input[:100]}...")
        
        # 创建项目
        project_id = coordinator.create_project(
            user_input=request.user_input,
            project_type=request.project_type
        )
        
        # 存储项目信息
        active_projects[project_id] = {
            "request": request.dict(),
            "created_time": datetime.now(),
            "status": "created"
        }
        
        logger.info(f"项目创建成功: {project_id}")
        
        return {
            "project_id": project_id,
            "status": "created",
            "message": "项目创建成功"
        }
        
    except Exception as e:
        logger.error(f"项目创建失败: {e}")
        raise HTTPException(status_code=500, detail=f"项目创建失败: {str(e)}")

@app.post("/api/projects/{project_id}/execute", response_model=GenerationResult)
async def execute_project(project_id: str, background_tasks: BackgroundTasks):
    """执行项目生成"""
    try:
        if not coordinator:
            raise HTTPException(status_code=503, detail="系统未初始化")
        
        if project_id not in active_projects:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        logger.info(f"开始执行项目: {project_id}")
        
        # 更新项目状态
        active_projects[project_id]["status"] = "executing"
        active_projects[project_id]["start_time"] = datetime.now()
        
        # 执行项目
        start_time = datetime.now()
        result = coordinator.execute_project(project_id)
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # 更新项目状态
        active_projects[project_id]["status"] = "completed" if result.success else "failed"
        active_projects[project_id]["end_time"] = datetime.now()
        active_projects[project_id]["result"] = result
        
        logger.info(f"项目执行完成: {project_id}, 成功: {result.success}")
        
        return GenerationResult(
            success=result.success,
            project_id=project_id,
            deliverables=result.deliverables,
            quality_metrics=result.quality_metrics,
            recommendations=result.recommendations,
            execution_time=execution_time
        )
        
    except Exception as e:
        logger.error(f"项目执行失败: {e}")
        if project_id in active_projects:
            active_projects[project_id]["status"] = "failed"
            active_projects[project_id]["error"] = str(e)
        raise HTTPException(status_code=500, detail=f"项目执行失败: {str(e)}")

@app.get("/api/projects/{project_id}/status", response_model=ProjectStatusResponse)
async def get_project_status(project_id: str):
    """获取项目状态"""
    try:
        if not coordinator:
            raise HTTPException(status_code=503, detail="系统未初始化")
        
        if project_id not in active_projects:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 获取项目状态
        status = coordinator.get_project_status(project_id)
        if not status:
            raise HTTPException(status_code=404, detail="项目状态不存在")
        
        return ProjectStatusResponse(
            project_id=project_id,
            status=status.status,
            progress=status.progress_percentage,
            current_phase=status.current_phase,
            estimated_completion=status.estimated_completion.isoformat() if status.estimated_completion else None,
            issues=status.issues,
            next_actions=status.next_actions
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取项目状态失败: {str(e)}")

@app.get("/api/projects", response_model=List[Dict[str, Any]])
async def list_projects():
    """列出所有项目"""
    try:
        if not coordinator:
            raise HTTPException(status_code=503, detail="系统未初始化")
        
        # 获取项目列表
        projects = coordinator.list_projects()
        
        # 添加额外信息
        for project in projects:
            project_id = project['project_id']
            if project_id in active_projects:
                project.update({
                    "request_info": active_projects[project_id].get("request", {}),
                    "created_time": active_projects[project_id].get("created_time", datetime.now()).isoformat()
                })
        
        return projects
        
    except Exception as e:
        logger.error(f"获取项目列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取项目列表失败: {str(e)}")

@app.delete("/api/projects/{project_id}")
async def cancel_project(project_id: str):
    """取消项目"""
    try:
        if not coordinator:
            raise HTTPException(status_code=503, detail="系统未初始化")
        
        if project_id not in active_projects:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 取消项目
        success = coordinator.cancel_project(project_id)
        
        if success:
            active_projects[project_id]["status"] = "cancelled"
            active_projects[project_id]["cancelled_time"] = datetime.now()
            
            logger.info(f"项目已取消: {project_id}")
            return {"message": "项目已取消", "project_id": project_id}
        else:
            raise HTTPException(status_code=400, detail="项目取消失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消项目失败: {e}")
        raise HTTPException(status_code=500, detail=f"取消项目失败: {str(e)}")

@app.get("/api/projects/{project_id}/deliverables")
async def get_project_deliverables(project_id: str):
    """获取项目交付物"""
    try:
        if project_id not in active_projects:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        project_data = active_projects[project_id]
        
        if "result" not in project_data:
            raise HTTPException(status_code=400, detail="项目尚未完成")
        
        result = project_data["result"]
        deliverables = result.deliverables
        
        # 处理生成的代码文件
        if "generated_code" in deliverables:
            code_data = deliverables["generated_code"]
            
            # 保存文件到静态目录
            project_dir = os.path.join(static_dir, project_id)
            if not os.path.exists(project_dir):
                os.makedirs(project_dir)
            
            file_urls = []
            if "files" in code_data:
                for file_info in code_data["files"]:
                    filename = file_info.get("filename", "unknown.txt")
                    content = file_info.get("content", "")
                    
                    file_path = os.path.join(project_dir, filename)
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    file_urls.append({
                        "filename": filename,
                        "url": f"/static/{project_id}/{filename}",
                        "size": len(content)
                    })
            
            deliverables["file_urls"] = file_urls
        
        return {
            "project_id": project_id,
            "deliverables": deliverables,
            "generated_time": project_data.get("end_time", datetime.now()).isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目交付物失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取项目交付物失败: {str(e)}")

@app.get("/api/projects/{project_id}/download/{filename}")
async def download_file(project_id: str, filename: str):
    """下载项目文件"""
    try:
        file_path = os.path.join(static_dir, project_id, filename)
        
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在")
        
        return FileResponse(
            path=file_path,
            filename=filename,
            media_type='application/octet-stream'
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件下载失败: {e}")
        raise HTTPException(status_code=500, detail=f"文件下载失败: {str(e)}")

# 快速生成接口（一键生成）
@app.post("/api/generate", response_model=GenerationResult)
async def quick_generate(request: ProjectCreateRequest):
    """快速生成Playable广告（一键生成）"""
    try:
        if not coordinator:
            raise HTTPException(status_code=503, detail="系统未初始化")
        
        logger.info(f"快速生成请求: {request.user_input[:100]}...")
        
        # 创建项目
        project_id = coordinator.create_project(
            user_input=request.user_input,
            project_type=request.project_type
        )
        
        # 存储项目信息
        active_projects[project_id] = {
            "request": request.dict(),
            "created_time": datetime.now(),
            "status": "executing"
        }
        
        # 立即执行项目
        start_time = datetime.now()
        result = coordinator.execute_project(project_id)
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # 更新项目状态
        active_projects[project_id]["status"] = "completed" if result.success else "failed"
        active_projects[project_id]["end_time"] = datetime.now()
        active_projects[project_id]["result"] = result
        
        logger.info(f"快速生成完成: {project_id}, 成功: {result.success}, 耗时: {execution_time:.2f}秒")
        
        return GenerationResult(
            success=result.success,
            project_id=project_id,
            deliverables=result.deliverables,
            quality_metrics=result.quality_metrics,
            recommendations=result.recommendations,
            execution_time=execution_time
        )
        
    except Exception as e:
        logger.error(f"快速生成失败: {e}")
        raise HTTPException(status_code=500, detail=f"快速生成失败: {str(e)}")

# 系统信息接口
@app.get("/api/system/info")
async def get_system_info():
    """获取系统信息"""
    try:
        info = {
            "system": {
                "name": "crewAI Playable广告生成系统",
                "version": "1.0.0 MVP",
                "status": "running",
                "uptime": datetime.now().isoformat()
            },
            "agents": {
                "coordinator": coordinator is not None,
                "total_agents": 5 if coordinator else 0
            },
            "projects": {
                "total": len(active_projects),
                "active": len([p for p in active_projects.values() if p.get("status") == "executing"]),
                "completed": len([p for p in active_projects.values() if p.get("status") == "completed"]),
                "failed": len([p for p in active_projects.values() if p.get("status") == "failed"])
            },
            "capabilities": {
                "project_types": ["standard_playable_ad", "rapid_prototype"],
                "supported_frameworks": ["Phaser.js"],
                "output_formats": ["HTML5", "JavaScript", "CSS"]
            }
        }
        
        return info
        
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取系统信息失败: {str(e)}")

# 开发和调试接口
@app.get("/api/debug/logs")
async def get_logs(lines: int = 100):
    """获取系统日志（仅开发环境）"""
    try:
        log_file = "app.log"
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                return {
                    "logs": [line.strip() for line in recent_lines],
                    "total_lines": len(all_lines),
                    "returned_lines": len(recent_lines)
                }
        else:
            return {"logs": [], "message": "日志文件不存在"}
    except Exception as e:
        logger.error(f"获取日志失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取日志失败: {str(e)}")

# 主函数
if __name__ == "__main__":
    # 开发环境配置
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )