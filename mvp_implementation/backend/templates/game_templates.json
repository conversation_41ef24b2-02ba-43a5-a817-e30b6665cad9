{
  "templates": {
    "snake": {
      "name": "贪吃蛇游戏",
      "description": "经典的贪吃蛇游戏，支持移动端和桌面端操作",
      "files": {
        "index.html": "phaser_index.html",
        "game.js": "snake_game.js"
      },
      "default_config": {
        "game_title": "贪吃蛇大作战",
        "game_description": "控制贪吃蛇吃食物，避免撞墙和咬到自己！",
        "game_width": 800,
        "game_height": 600,
        "grid_size": 20,
        "initial_speed": 150,
        "speed_increase": 10,
        "max_speed": 50,
        "food_score": 10,
        "special_food_score": 50,
        "special_food_chance": 0.1,
        "snake_color": "#00ff00",
        "food_color": "#ff0000",
        "special_food_color": "#ffff00",
        "background_color": "#000000",
        "grid_color": "#333333",
        "show_grid": false,
        "controls_description": "方向键或WASD: 移动<br>空格键: 暂停/继续<br>移动端: 点击屏幕或使用虚拟方向键",
        "game_over_message": "游戏结束！挑战更高分数吧！"
      },
      "customizable_fields": [
        {
          "field": "game_title",
          "type": "string",
          "description": "游戏标题",
          "required": true
        },
        {
          "field": "game_description",
          "type": "string",
          "description": "游戏描述",
          "required": false
        },
        {
          "field": "game_width",
          "type": "integer",
          "description": "游戏宽度（像素）",
          "min": 400,
          "max": 1200,
          "required": false
        },
        {
          "field": "game_height",
          "type": "integer",
          "description": "游戏高度（像素）",
          "min": 300,
          "max": 900,
          "required": false
        },
        {
          "field": "grid_size",
          "type": "integer",
          "description": "网格大小（像素）",
          "min": 10,
          "max": 40,
          "required": false
        },
        {
          "field": "initial_speed",
          "type": "integer",
          "description": "初始移动速度（毫秒）",
          "min": 50,
          "max": 500,
          "required": false
        },
        {
          "field": "snake_color",
          "type": "color",
          "description": "蛇身颜色",
          "required": false
        },
        {
          "field": "food_color",
          "type": "color",
          "description": "食物颜色",
          "required": false
        },
        {
          "field": "background_color",
          "type": "color",
          "description": "背景颜色",
          "required": false
        },
        {
          "field": "show_grid",
          "type": "boolean",
          "description": "是否显示网格",
          "required": false
        }
      ]
    },
    "tetris": {
      "name": "俄罗斯方块",
      "description": "经典的俄罗斯方块游戏",
      "files": {
        "index.html": "phaser_index.html",
        "game.js": "tetris_game.js"
      },
      "default_config": {
        "game_title": "俄罗斯方块",
        "game_description": "消除完整的行来获得分数！",
        "game_width": 600,
        "game_height": 800,
        "grid_width": 10,
        "grid_height": 20,
        "block_size": 30,
        "fall_speed": 1000,
        "speed_increase": 50,
        "line_score": 100,
        "tetris_score": 800,
        "background_color": "#000033",
        "grid_color": "#333366",
        "controls_description": "方向键: 移动和旋转<br>空格键: 快速下落<br>P键: 暂停"
      },
      "customizable_fields": [
        {
          "field": "game_title",
          "type": "string",
          "description": "游戏标题",
          "required": true
        },
        {
          "field": "fall_speed",
          "type": "integer",
          "description": "方块下落速度（毫秒）",
          "min": 100,
          "max": 2000,
          "required": false
        },
        {
          "field": "background_color",
          "type": "color",
          "description": "背景颜色",
          "required": false
        }
      ]
    },
    "flappy_bird": {
      "name": "飞翔小鸟",
      "description": "点击屏幕让小鸟飞翔，避开障碍物",
      "files": {
        "index.html": "phaser_index.html",
        "game.js": "flappy_bird_game.js"
      },
      "default_config": {
        "game_title": "飞翔小鸟",
        "game_description": "点击屏幕让小鸟飞翔，穿越管道获得高分！",
        "game_width": 800,
        "game_height": 600,
        "bird_gravity": 600,
        "bird_jump": -300,
        "pipe_speed": 200,
        "pipe_gap": 150,
        "pipe_spawn_rate": 1500,
        "background_color": "#87CEEB",
        "bird_color": "#FFD700",
        "pipe_color": "#228B22",
        "controls_description": "点击屏幕或按空格键让小鸟飞翔"
      },
      "customizable_fields": [
        {
          "field": "game_title",
          "type": "string",
          "description": "游戏标题",
          "required": true
        },
        {
          "field": "bird_gravity",
          "type": "integer",
          "description": "小鸟重力",
          "min": 200,
          "max": 1000,
          "required": false
        },
        {
          "field": "pipe_gap",
          "type": "integer",
          "description": "管道间隙大小",
          "min": 100,
          "max": 250,
          "required": false
        },
        {
          "field": "background_color",
          "type": "color",
          "description": "背景颜色",
          "required": false
        },
        {
          "field": "bird_color",
          "type": "color",
          "description": "小鸟颜色",
          "required": false
        }
      ]
    },
    "breakout": {
      "name": "打砖块",
      "description": "控制挡板反弹球来打破所有砖块",
      "files": {
        "index.html": "phaser_index.html",
        "game.js": "breakout_game.js"
      },
      "default_config": {
        "game_title": "打砖块大师",
        "game_description": "控制挡板反弹球，打破所有砖块！",
        "game_width": 800,
        "game_height": 600,
        "paddle_speed": 500,
        "ball_speed": 300,
        "brick_rows": 5,
        "brick_cols": 10,
        "brick_score": 10,
        "background_color": "#000080",
        "paddle_color": "#FFFFFF",
        "ball_color": "#FFFF00",
        "brick_colors": ["#FF0000", "#FF8000", "#FFFF00", "#00FF00", "#0080FF"],
        "controls_description": "方向键或鼠标移动挡板"
      },
      "customizable_fields": [
        {
          "field": "game_title",
          "type": "string",
          "description": "游戏标题",
          "required": true
        },
        {
          "field": "paddle_speed",
          "type": "integer",
          "description": "挡板移动速度",
          "min": 200,
          "max": 800,
          "required": false
        },
        {
          "field": "ball_speed",
          "type": "integer",
          "description": "球的移动速度",
          "min": 150,
          "max": 500,
          "required": false
        },
        {
          "field": "brick_rows",
          "type": "integer",
          "description": "砖块行数",
          "min": 3,
          "max": 8,
          "required": false
        },
        {
          "field": "brick_cols",
          "type": "integer",
          "description": "砖块列数",
          "min": 5,
          "max": 15,
          "required": false
        },
        {
          "field": "background_color",
          "type": "color",
          "description": "背景颜色",
          "required": false
        }
      ]
    }
  },
  "common_settings": {
    "supported_formats": ["html", "zip"],
    "default_format": "html",
    "max_file_size": "10MB",
    "supported_browsers": ["Chrome", "Firefox", "Safari", "Edge"],
    "mobile_support": true,
    "responsive_design": true
  },
  "validation_rules": {
    "game_title": {
      "min_length": 1,
      "max_length": 50,
      "pattern": "^[\u4e00-\u9fa5a-zA-Z0-9\s\-_]+$"
    },
    "game_description": {
      "max_length": 200
    },
    "color_format": "^#[0-9A-Fa-f]{6}$",
    "dimension_range": {
      "min_width": 320,
      "max_width": 1200,
      "min_height": 240,
      "max_height": 900
    }
  },
  "generation_metadata": {
    "version": "1.0.0",
    "framework": "Phaser.js 3.70.0",
    "template_engine": "Jinja2",
    "last_updated": "2024-01-01",
    "supported_features": [
      "responsive_design",
      "mobile_controls",
      "touch_support",
      "keyboard_input",
      "sound_effects",
      "particle_effects",
      "pause_resume",
      "score_tracking",
      "level_progression",
      "game_over_screen",
      "restart_functionality"
    ]
  }
}