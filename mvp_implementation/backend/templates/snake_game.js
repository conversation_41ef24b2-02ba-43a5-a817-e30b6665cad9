// {{game_title}} - Phaser.js 贪吃蛇游戏
// 生成时间: {{generation_time}}
// 游戏配置: {{game_config}}

class GameScene extends Phaser.Scene {
    constructor() {
        super({ key: 'GameScene' });
        
        // 游戏配置
        this.config = {
            gridSize: {{grid_size|default(20)}},
            gameWidth: {{game_width|default(800)}},
            gameHeight: {{game_height|default(600)}},
            initialSpeed: {{initial_speed|default(150)}},
            speedIncrease: {{speed_increase|default(10)}},
            maxSpeed: {{max_speed|default(50)}},
            foodScore: {{food_score|default(10)}},
            specialFoodScore: {{special_food_score|default(50)}},
            specialFoodChance: {{special_food_chance|default(0.1)}},
            colors: {
                snake: '{{snake_color|default("#00ff00")}}',
                food: '{{food_color|default("#ff0000")}}',
                specialFood: '{{special_food_color|default("#ffff00")}}',
                background: '{{background_color|default("#000000")}}',
                grid: '{{grid_color|default("#333333")}}'
            }
        };
        
        // 游戏状态
        this.snake = [];
        this.food = null;
        this.specialFood = null;
        this.direction = { x: 1, y: 0 };
        this.nextDirection = { x: 1, y: 0 };
        this.score = 0;
        this.level = 1;
        this.gameTime = 0;
        this.isGameOver = false;
        this.isPaused = false;
        this.soundEnabled = true;
        
        // 移动计时器
        this.moveTimer = 0;
        this.moveInterval = this.config.initialSpeed;
        
        // 特殊食物计时器
        this.specialFoodTimer = 0;
        this.specialFoodDuration = 5000; // 5秒
        
        // 网格计算
        this.gridWidth = Math.floor(this.config.gameWidth / this.config.gridSize);
        this.gridHeight = Math.floor(this.config.gameHeight / this.config.gridSize);
    }
    
    preload() {
        // 创建简单的几何图形作为精灵
        this.load.image('pixel', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
    }
    
    create() {
        // 设置游戏世界边界
        this.physics.world.setBounds(0, 0, this.config.gameWidth, this.config.gameHeight);
        
        // 创建背景
        this.add.rectangle(
            this.config.gameWidth / 2, 
            this.config.gameHeight / 2, 
            this.config.gameWidth, 
            this.config.gameHeight, 
            Phaser.Display.Color.HexStringToColor(this.config.colors.background).color
        );
        
        // 绘制网格（可选）
        if ({{show_grid|default('false')}}) {
            this.drawGrid();
        }
        
        // 初始化蛇
        this.initSnake();
        
        // 创建第一个食物
        this.createFood();
        
        // 设置键盘输入
        this.setupInput();
        
        // 创建音效（如果需要）
        this.setupSounds();
        
        // 创建粒子效果
        this.setupParticles();
        
        // 开始游戏循环
        this.startGameLoop();
    }
    
    drawGrid() {
        const graphics = this.add.graphics();
        graphics.lineStyle(1, Phaser.Display.Color.HexStringToColor(this.config.colors.grid).color, 0.3);
        
        // 垂直线
        for (let x = 0; x <= this.gridWidth; x++) {
            graphics.moveTo(x * this.config.gridSize, 0);
            graphics.lineTo(x * this.config.gridSize, this.config.gameHeight);
        }
        
        // 水平线
        for (let y = 0; y <= this.gridHeight; y++) {
            graphics.moveTo(0, y * this.config.gridSize);
            graphics.lineTo(this.config.gameWidth, y * this.config.gridSize);
        }
        
        graphics.strokePath();
    }
    
    initSnake() {
        // 清除现有的蛇
        this.snake.forEach(segment => {
            if (segment.sprite) {
                segment.sprite.destroy();
            }
        });
        
        // 创建初始蛇身（3个段）
        this.snake = [];
        const startX = Math.floor(this.gridWidth / 4);
        const startY = Math.floor(this.gridHeight / 2);
        
        for (let i = 0; i < 3; i++) {
            const segment = {
                x: startX - i,
                y: startY,
                sprite: null
            };
            
            segment.sprite = this.add.rectangle(
                segment.x * this.config.gridSize + this.config.gridSize / 2,
                segment.y * this.config.gridSize + this.config.gridSize / 2,
                this.config.gridSize - 2,
                this.config.gridSize - 2,
                Phaser.Display.Color.HexStringToColor(this.config.colors.snake).color
            );
            
            // 蛇头稍微不同的样式
            if (i === 0) {
                segment.sprite.setStrokeStyle(2, 0xffffff);
            }
            
            this.snake.push(segment);
        }
        
        // 重置方向
        this.direction = { x: 1, y: 0 };
        this.nextDirection = { x: 1, y: 0 };
    }
    
    createFood() {
        let foodX, foodY;
        let validPosition = false;
        
        // 确保食物不会生成在蛇身上
        while (!validPosition) {
            foodX = Phaser.Math.Between(0, this.gridWidth - 1);
            foodY = Phaser.Math.Between(0, this.gridHeight - 1);
            
            validPosition = !this.snake.some(segment => 
                segment.x === foodX && segment.y === foodY
            );
        }
        
        // 销毁旧食物
        if (this.food && this.food.sprite) {
            this.food.sprite.destroy();
        }
        
        // 创建新食物
        this.food = {
            x: foodX,
            y: foodY,
            sprite: this.add.circle(
                foodX * this.config.gridSize + this.config.gridSize / 2,
                foodY * this.config.gridSize + this.config.gridSize / 2,
                this.config.gridSize / 2 - 2,
                Phaser.Display.Color.HexStringToColor(this.config.colors.food).color
            )
        };
        
        // 食物闪烁效果
        this.tweens.add({
            targets: this.food.sprite,
            alpha: 0.5,
            duration: 500,
            yoyo: true,
            repeat: -1
        });
        
        // 随机生成特殊食物
        if (Math.random() < this.config.specialFoodChance) {
            this.createSpecialFood();
        }
    }
    
    createSpecialFood() {
        if (this.specialFood && this.specialFood.sprite) {
            return; // 已经有特殊食物了
        }
        
        let foodX, foodY;
        let validPosition = false;
        
        // 确保特殊食物不会生成在蛇身或普通食物上
        while (!validPosition) {
            foodX = Phaser.Math.Between(0, this.gridWidth - 1);
            foodY = Phaser.Math.Between(0, this.gridHeight - 1);
            
            validPosition = !this.snake.some(segment => 
                segment.x === foodX && segment.y === foodY
            ) && !(this.food.x === foodX && this.food.y === foodY);
        }
        
        // 创建特殊食物
        this.specialFood = {
            x: foodX,
            y: foodY,
            sprite: this.add.star(
                foodX * this.config.gridSize + this.config.gridSize / 2,
                foodY * this.config.gridSize + this.config.gridSize / 2,
                5,
                this.config.gridSize / 2 - 2,
                this.config.gridSize / 4,
                Phaser.Display.Color.HexStringToColor(this.config.colors.specialFood).color
            )
        };
        
        // 特殊食物旋转效果
        this.tweens.add({
            targets: this.specialFood.sprite,
            rotation: Math.PI * 2,
            duration: 1000,
            repeat: -1
        });
        
        // 特殊食物缩放效果
        this.tweens.add({
            targets: this.specialFood.sprite,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 300,
            yoyo: true,
            repeat: -1
        });
        
        // 设置特殊食物消失计时器
        this.specialFoodTimer = this.time.now + this.specialFoodDuration;
    }
    
    setupInput() {
        // 键盘输入
        this.cursors = this.input.keyboard.createCursorKeys();
        this.wasd = this.input.keyboard.addKeys('W,S,A,D');
        
        // 防止重复按键
        this.input.keyboard.on('keydown', (event) => {
            this.handleKeyInput(event.code);
        });
        
        // 触摸/鼠标输入（用于移动设备）
        this.input.on('pointerdown', (pointer) => {
            this.handleTouchInput(pointer);
        });
    }
    
    handleKeyInput(keyCode) {
        switch (keyCode) {
            case 'ArrowUp':
            case 'KeyW':
                if (this.direction.y !== 1) {
                    this.nextDirection = { x: 0, y: -1 };
                }
                break;
            case 'ArrowDown':
            case 'KeyS':
                if (this.direction.y !== -1) {
                    this.nextDirection = { x: 0, y: 1 };
                }
                break;
            case 'ArrowLeft':
            case 'KeyA':
                if (this.direction.x !== 1) {
                    this.nextDirection = { x: -1, y: 0 };
                }
                break;
            case 'ArrowRight':
            case 'KeyD':
                if (this.direction.x !== -1) {
                    this.nextDirection = { x: 1, y: 0 };
                }
                break;
        }
    }
    
    handleTouchInput(pointer) {
        const head = this.snake[0];
        const headX = head.x * this.config.gridSize + this.config.gridSize / 2;
        const headY = head.y * this.config.gridSize + this.config.gridSize / 2;
        
        const deltaX = pointer.x - headX;
        const deltaY = pointer.y - headY;
        
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            // 水平移动
            if (deltaX > 0 && this.direction.x !== -1) {
                this.nextDirection = { x: 1, y: 0 };
            } else if (deltaX < 0 && this.direction.x !== 1) {
                this.nextDirection = { x: -1, y: 0 };
            }
        } else {
            // 垂直移动
            if (deltaY > 0 && this.direction.y !== -1) {
                this.nextDirection = { x: 0, y: 1 };
            } else if (deltaY < 0 && this.direction.y !== 1) {
                this.nextDirection = { x: 0, y: -1 };
            }
        }
    }
    
    handleMobileInput(direction) {
        switch (direction) {
            case 'up':
                if (this.direction.y !== 1) {
                    this.nextDirection = { x: 0, y: -1 };
                }
                break;
            case 'down':
                if (this.direction.y !== -1) {
                    this.nextDirection = { x: 0, y: 1 };
                }
                break;
            case 'left':
                if (this.direction.x !== 1) {
                    this.nextDirection = { x: -1, y: 0 };
                }
                break;
            case 'right':
                if (this.direction.x !== -1) {
                    this.nextDirection = { x: 1, y: 0 };
                }
                break;
        }
    }
    
    setupSounds() {
        // 创建简单的音效（使用Web Audio API）
        this.sounds = {
            eat: () => this.playTone(440, 100),
            specialEat: () => this.playTone(660, 200),
            gameOver: () => this.playTone(220, 500)
        };
    }
    
    playTone(frequency, duration) {
        if (!this.soundEnabled) return;
        
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.value = frequency;
            oscillator.type = 'square';
            
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration / 1000);
        } catch (e) {
            console.log('音效播放失败:', e);
        }
    }
    
    setupParticles() {
        // 创建粒子系统用于特效
        this.particles = this.add.particles(0, 0, 'pixel', {
            scale: { start: 0.5, end: 0 },
            speed: { min: 50, max: 100 },
            lifespan: 300,
            quantity: 5
        });
        this.particles.stop();
    }
    
    startGameLoop() {
        // 重置游戏状态
        this.score = 0;
        this.level = 1;
        this.gameTime = 0;
        this.isGameOver = false;
        this.moveInterval = this.config.initialSpeed;
        
        // 更新UI
        if (window.uiController) {
            window.uiController.updateScore(this.score);
            window.uiController.updateLevel(this.level);
        }
    }
    
    update(time, delta) {
        if (this.isGameOver || this.scene.isPaused()) {
            return;
        }
        
        // 更新游戏时间
        this.gameTime += delta;
        if (window.uiController) {
            window.uiController.updateTime(Math.floor(this.gameTime / 1000));
        }
        
        // 检查特殊食物是否过期
        if (this.specialFood && time > this.specialFoodTimer) {
            this.removeSpecialFood();
        }
        
        // 移动蛇
        this.moveTimer += delta;
        if (this.moveTimer >= this.moveInterval) {
            this.moveSnake();
            this.moveTimer = 0;
        }
    }
    
    moveSnake() {
        // 更新方向
        this.direction = { ...this.nextDirection };
        
        // 计算新的头部位置
        const head = this.snake[0];
        const newHead = {
            x: head.x + this.direction.x,
            y: head.y + this.direction.y,
            sprite: null
        };
        
        // 检查边界碰撞
        if (newHead.x < 0 || newHead.x >= this.gridWidth || 
            newHead.y < 0 || newHead.y >= this.gridHeight) {
            this.gameOver();
            return;
        }
        
        // 检查自身碰撞
        if (this.snake.some(segment => segment.x === newHead.x && segment.y === newHead.y)) {
            this.gameOver();
            return;
        }
        
        // 创建新头部精灵
        newHead.sprite = this.add.rectangle(
            newHead.x * this.config.gridSize + this.config.gridSize / 2,
            newHead.y * this.config.gridSize + this.config.gridSize / 2,
            this.config.gridSize - 2,
            this.config.gridSize - 2,
            Phaser.Display.Color.HexStringToColor(this.config.colors.snake).color
        );
        newHead.sprite.setStrokeStyle(2, 0xffffff);
        
        // 移除旧头部的边框
        if (this.snake[0] && this.snake[0].sprite) {
            this.snake[0].sprite.setStrokeStyle(0);
        }
        
        // 添加新头部
        this.snake.unshift(newHead);
        
        let ateFood = false;
        
        // 检查是否吃到普通食物
        if (newHead.x === this.food.x && newHead.y === this.food.y) {
            this.eatFood();
            ateFood = true;
        }
        
        // 检查是否吃到特殊食物
        if (this.specialFood && newHead.x === this.specialFood.x && newHead.y === this.specialFood.y) {
            this.eatSpecialFood();
            ateFood = true;
        }
        
        // 如果没有吃到食物，移除尾部
        if (!ateFood) {
            const tail = this.snake.pop();
            if (tail && tail.sprite) {
                tail.sprite.destroy();
            }
        }
    }
    
    eatFood() {
        // 增加分数
        this.score += this.config.foodScore;
        
        // 播放音效
        this.sounds.eat();
        
        // 创建粒子效果
        this.particles.emitParticleAt(
            this.food.x * this.config.gridSize + this.config.gridSize / 2,
            this.food.y * this.config.gridSize + this.config.gridSize / 2
        );
        
        // 更新等级和速度
        const newLevel = Math.floor(this.score / 100) + 1;
        if (newLevel > this.level) {
            this.level = newLevel;
            this.moveInterval = Math.max(
                this.config.maxSpeed,
                this.config.initialSpeed - (this.level - 1) * this.config.speedIncrease
            );
        }
        
        // 更新UI
        if (window.uiController) {
            window.uiController.updateScore(this.score);
            window.uiController.updateLevel(this.level);
        }
        
        // 创建新食物
        this.createFood();
    }
    
    eatSpecialFood() {
        // 增加更多分数
        this.score += this.config.specialFoodScore;
        
        // 播放特殊音效
        this.sounds.specialEat();
        
        // 创建更多粒子效果
        for (let i = 0; i < 3; i++) {
            this.time.delayedCall(i * 100, () => {
                this.particles.emitParticleAt(
                    this.specialFood.x * this.config.gridSize + this.config.gridSize / 2,
                    this.specialFood.y * this.config.gridSize + this.config.gridSize / 2
                );
            });
        }
        
        // 更新UI
        if (window.uiController) {
            window.uiController.updateScore(this.score);
        }
        
        // 移除特殊食物
        this.removeSpecialFood();
    }
    
    removeSpecialFood() {
        if (this.specialFood && this.specialFood.sprite) {
            this.specialFood.sprite.destroy();
            this.specialFood = null;
        }
    }
    
    gameOver() {
        this.isGameOver = true;
        
        // 播放游戏结束音效
        this.sounds.gameOver();
        
        // 蛇身闪烁效果
        this.snake.forEach((segment, index) => {
            if (segment.sprite) {
                this.tweens.add({
                    targets: segment.sprite,
                    alpha: 0.3,
                    duration: 200,
                    yoyo: true,
                    repeat: 3,
                    delay: index * 50
                });
            }
        });
        
        // 显示游戏结束界面
        this.time.delayedCall(1000, () => {
            if (window.uiController) {
                window.uiController.showGameOver(this.score);
            }
        });
    }
    
    setSoundEnabled(enabled) {
        this.soundEnabled = enabled;
    }
    
    restart() {
        // 清理现有游戏对象
        this.snake.forEach(segment => {
            if (segment.sprite) {
                segment.sprite.destroy();
            }
        });
        
        if (this.food && this.food.sprite) {
            this.food.sprite.destroy();
        }
        
        this.removeSpecialFood();
        
        // 重新初始化游戏
        this.initSnake();
        this.createFood();
        this.startGameLoop();
    }
}

// Phaser游戏配置
const gameConfig = {
    type: Phaser.AUTO,
    width: {{game_width|default(800)}},
    height: {{game_height|default(600)}},
    parent: 'game-canvas',
    backgroundColor: '{{background_color|default("#000000")}}',
    scene: GameScene,
    physics: {
        default: 'arcade',
        arcade: {
            gravity: { y: 0 },
            debug: false
        }
    },
    scale: {
        mode: Phaser.Scale.FIT,
        autoCenter: Phaser.Scale.CENTER_BOTH,
        min: {
            width: 320,
            height: 240
        },
        max: {
            width: 1200,
            height: 900
        }
    },
    render: {
        pixelArt: true,
        antialias: false
    }
};

// 启动游戏
window.addEventListener('load', () => {
    window.gameInstance = new Phaser.Game(gameConfig);
});

// 导出游戏类供外部使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GameScene, gameConfig };
}