/**
 * ComponentTemplateManager - 标准化组件模板管理器
 * 负责管理和选择最适合的预设组件组合
 * 基于游戏类型和需求提供智能组件推荐
 */

import { CameraPresetType } from '../babylon/presets/CameraPresets';
import { LightingPresetType } from '../babylon/presets/LightingPresets';
import { MaterialPresetType } from '../babylon/presets/MaterialPresets';
import { ParticlePresetType } from '../babylon/presets/ParticlePresets';

// 游戏类型枚举
export enum GameType {
  ACTION = 'action',
  PUZZLE = 'puzzle',
  ADVENTURE = 'adventure',
  STRATEGY = 'strategy',
  CASUAL = 'casual',
  SHOOTER = 'shooter',
  RACING = 'racing',
  PLATFORMER = 'platformer'
}

// 组件模板配置接口
export interface ComponentTemplate {
  name: string;
  description: string;
  gameTypes: GameType[];
  components: {
    camera: {
      type: CameraPresetType;
      config?: any;
      reason: string;
    };
    lighting: {
      type: LightingPresetType;
      config?: any;
      reason: string;
    };
    materials: Array<{
      usage: string;
      type: MaterialPresetType;
      config?: any;
      reason: string;
    }>;
    particles: Array<{
      usage: string;
      type: ParticlePresetType;
      config?: any;
      reason: string;
    }>;
    ui: Array<{
      component: string;
      usage: string;
      config?: any;
    }>;
  };
  performance: {
    complexity: 'low' | 'medium' | 'high';
    mobileOptimized: boolean;
    estimatedFPS: number;
  };
}

// 预定义的组件模板
export const COMPONENT_TEMPLATES: Record<string, ComponentTemplate> = {
  // 第三人称动作游戏模板
  THIRD_PERSON_ACTION: {
    name: '第三人称动作游戏',
    description: '适用于角色扮演、动作冒险等需要跟随角色的游戏',
    gameTypes: [GameType.ACTION, GameType.ADVENTURE],
    components: {
      camera: {
        type: CameraPresetType.THIRD_PERSON,
        reason: '提供最佳的角色跟随视角，适合动作游戏'
      },
      lighting: {
        type: LightingPresetType.OUTDOOR_DAY,
        reason: '自然光照效果，突出角色和环境细节'
      },
      materials: [
        {
          usage: '角色材质',
          type: MaterialPresetType.PBR_PLASTIC_SMOOTH,
          reason: '适合卡通风格角色，性能友好'
        },
        {
          usage: '环境材质',
          type: MaterialPresetType.STONE,
          reason: '自然环境质感，增强沉浸感'
        }
      ],
      particles: [
        {
          usage: '攻击特效',
          type: ParticlePresetType.MAGIC_SPARKLES,
          reason: '增强战斗视觉反馈'
        },
        {
          usage: '环境特效',
          type: ParticlePresetType.DUST,
          reason: '增加环境氛围'
        }
      ],
      ui: [
        {
          component: 'HealthBar',
          usage: '玩家血条显示'
        },
        {
          component: 'GameHUD',
          usage: '完整游戏界面'
        }
      ]
    },
    performance: {
      complexity: 'medium',
      mobileOptimized: true,
      estimatedFPS: 45
    }
  },

  // 俯视角策略游戏模板
  TOP_DOWN_STRATEGY: {
    name: '俯视角策略游戏',
    description: '适用于塔防、RTS、策略类游戏',
    gameTypes: [GameType.STRATEGY],
    components: {
      camera: {
        type: CameraPresetType.TOP_DOWN,
        reason: '俯视角度便于观察全局，适合策略游戏'
      },
      lighting: {
        type: LightingPresetType.STUDIO_THREE_POINT,
        reason: '均匀光照，确保所有区域清晰可见'
      },
      materials: [
        {
          usage: '建筑材质',
          type: MaterialPresetType.PBR_METAL_SMOOTH,
          reason: '现代建筑质感，清晰的视觉区分'
        },
        {
          usage: '地面材质',
          type: MaterialPresetType.BASIC_DIFFUSE,
          reason: '简洁地面，不干扰主要元素'
        }
      ],
      particles: [
        {
          usage: '建造特效',
          type: ParticlePresetType.DUST,
          reason: '建造过程视觉反馈'
        },
        {
          usage: '爆炸特效',
          type: ParticlePresetType.EXPLOSION_SMALL,
          reason: '战斗效果展示'
        }
      ],
      ui: [
        {
          component: 'ScoreDisplay',
          usage: '资源和分数显示'
        },
        {
          component: 'GameButton',
          usage: '建造和控制按钮'
        }
      ]
    },
    performance: {
      complexity: 'high',
      mobileOptimized: true,
      estimatedFPS: 30
    }
  },

  // 第一人称射击游戏模板
  FIRST_PERSON_SHOOTER: {
    name: '第一人称射击游戏',
    description: '适用于FPS、第一人称探索游戏',
    gameTypes: [GameType.SHOOTER, GameType.ACTION],
    components: {
      camera: {
        type: CameraPresetType.FIRST_PERSON,
        reason: '沉浸式第一人称视角，适合射击游戏'
      },
      lighting: {
        type: LightingPresetType.DRAMATIC_HIGH_CONTRAST,
        reason: '戏剧性光照增强紧张感'
      },
      materials: [
        {
          usage: '武器材质',
          type: MaterialPresetType.PBR_METAL_ROUGH,
          reason: '真实金属质感，增强沉浸感'
        },
        {
          usage: '环境材质',
          type: MaterialPresetType.STONE,
          reason: '工业环境质感'
        }
      ],
      particles: [
        {
          usage: '枪口火焰',
          type: ParticlePresetType.FIRE_SMALL,
          reason: '射击视觉反馈'
        },
        {
          usage: '子弹轨迹',
          type: ParticlePresetType.EXPLOSION_SPARKS,
          reason: '弹道视觉效果'
        }
      ],
      ui: [
        {
          component: 'HealthBar',
          usage: '玩家生命值'
        },
        {
          component: 'CrosshairUI',
          usage: '瞄准准星'
        }
      ]
    },
    performance: {
      complexity: 'high',
      mobileOptimized: false,
      estimatedFPS: 60
    }
  },

  // 休闲益智游戏模板
  CASUAL_PUZZLE: {
    name: '休闲益智游戏',
    description: '适用于消除、解谜、休闲类游戏',
    gameTypes: [GameType.PUZZLE, GameType.CASUAL],
    components: {
      camera: {
        type: CameraPresetType.ORBITAL,
        reason: '灵活的观察角度，适合解谜游戏'
      },
      lighting: {
        type: LightingPresetType.INDOOR_BASIC,
        reason: '柔和均匀光照，适合长时间游戏'
      },
      materials: [
        {
          usage: '游戏元素',
          type: MaterialPresetType.CARTOON_FLAT,
          reason: '卡通风格，轻松愉快的视觉效果'
        },
        {
          usage: '背景材质',
          type: MaterialPresetType.BASIC_DIFFUSE,
          reason: '简洁背景，突出游戏元素'
        }
      ],
      particles: [
        {
          usage: '消除特效',
          type: ParticlePresetType.MAGIC_SPARKLES,
          reason: '愉快的消除反馈'
        },
        {
          usage: '成功特效',
          type: ParticlePresetType.MAGIC_ENERGY,
          reason: '成功时的庆祝效果'
        }
      ],
      ui: [
        {
          component: 'ScoreDisplay',
          usage: '分数显示'
        },
        {
          component: 'GameButton',
          usage: '游戏控制按钮'
        }
      ]
    },
    performance: {
      complexity: 'low',
      mobileOptimized: true,
      estimatedFPS: 60
    }
  }
};

/**
 * 组件模板管理器类
 */
export class ComponentTemplateManager {
  /**
   * 根据游戏类型推荐最适合的组件模板
   */
  public static recommendTemplate(gameType: GameType, requirements?: {
    mobileOptimized?: boolean;
    maxComplexity?: 'low' | 'medium' | 'high';
    minFPS?: number;
  }): ComponentTemplate | null {
    const templates = Object.values(COMPONENT_TEMPLATES);
    
    // 筛选适合的模板
    const suitableTemplates = templates.filter(template => {
      // 检查游戏类型匹配
      if (!template.gameTypes.includes(gameType)) {
        return false;
      }
      
      // 检查移动端优化要求
      if (requirements?.mobileOptimized && !template.performance.mobileOptimized) {
        return false;
      }
      
      // 检查复杂度要求
      if (requirements?.maxComplexity) {
        const complexityLevels = { low: 1, medium: 2, high: 3 };
        if (complexityLevels[template.performance.complexity] > complexityLevels[requirements.maxComplexity]) {
          return false;
        }
      }
      
      // 检查FPS要求
      if (requirements?.minFPS && template.performance.estimatedFPS < requirements.minFPS) {
        return false;
      }
      
      return true;
    });
    
    // 返回第一个匹配的模板，或者null
    return suitableTemplates.length > 0 ? suitableTemplates[0] : null;
  }

  /**
   * 获取所有可用的组件模板
   */
  public static getAllTemplates(): ComponentTemplate[] {
    return Object.values(COMPONENT_TEMPLATES);
  }

  /**
   * 根据模板名称获取组件模板
   */
  public static getTemplate(templateName: string): ComponentTemplate | null {
    return COMPONENT_TEMPLATES[templateName] || null;
  }

  /**
   * 根据游戏类型获取所有适合的模板
   */
  public static getTemplatesByGameType(gameType: GameType): ComponentTemplate[] {
    return Object.values(COMPONENT_TEMPLATES).filter(template => 
      template.gameTypes.includes(gameType)
    );
  }
}
