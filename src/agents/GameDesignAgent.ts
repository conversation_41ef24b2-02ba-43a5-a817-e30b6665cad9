import { BaseAgent, AgentConfig, AgentState } from "./BaseAgent";
import { AIMessage } from "@langchain/core/messages";
import { z } from "zod";
import { CustomClaude4Client, DEFAULT_CLAUDE4_CONFIG, AGENT_TEMPERATURE_CONFIGS } from './CustomClaude4Client';
import { SmartTemplateSelector, TemplateSelectionCriteria } from "./SmartTemplateSelector";
import { ComponentTemplateManager, GameType } from "./ComponentTemplateManager";

// 游戏设计规范Schema
const GameDesignSchema = z.object({
  gameType: z.enum(["action", "puzzle", "adventure", "strategy", "casual"]),
  theme: z.string().describe("游戏主题"),
  mechanics: z.array(z.string()).describe("核心游戏机制"),
  objectives: z.array(z.string()).describe("游戏目标"),
  difficulty: z.enum(["easy", "medium", "hard"]),
  estimatedPlayTime: z.string().describe("预计游戏时长"),
  targetAudience: z.string().describe("目标用户群体"),
  visualStyle: z.string().describe("视觉风格描述"),
  controls: z.object({
    primary: z.array(z.string()).describe("主要控制方式"),
    secondary: z.array(z.string()).optional().describe("次要控制方式")
  }),
  features: z.array(z.string()).describe("特色功能列表")
});

export type GameDesign = z.infer<typeof GameDesignSchema>;

// 游戏设计Agent
export class GameDesignAgent extends BaseAgent {
  constructor(config?: Partial<AgentConfig>) {
    const defaultConfig: AgentConfig = {
      name: "GameDesigner",
      role: "游戏设计师",
      systemPrompt: `你是一个专业的游戏设计师，专门设计适合Web平台的小游戏。你拥有完整的标准化组件库，能够基于预设组件快速设计高质量游戏。

## 🎯 你的职责：
1. 分析用户需求，理解他们想要的游戏类型和特点
2. 基于标准化组件库设计完整的游戏概念
3. 选择最适合的预设组件组合（相机、光照、材质、粒子系统等）
4. 确保设计能够在Web环境中高效实现
5. 提供详细的技术实现指导

## 🛠️ 可用的标准化组件库：

### 相机预设 (CameraPresets)：
- THIRD_PERSON: 第三人称相机，围绕目标旋转，适合动作游戏
- FIRST_PERSON: 第一人称相机，FPS风格，适合射击游戏
- TOP_DOWN: 俯视角相机，适合策略游戏、塔防游戏
- FOLLOW: 跟随相机，跟随目标移动，适合跑酷游戏
- FREE: 自由相机，完全自由控制，适合探索游戏
- OVER_SHOULDER: 过肩视角，适合动作冒险游戏

### 光照预设 (LightingPresets)：
- INDOOR_BASIC: 室内基础光照，均匀柔和
- OUTDOOR_DAY: 室外白天光照，自然阳光效果
- OUTDOOR_SUNSET: 室外日落光照，温暖氛围
- OUTDOOR_NIGHT: 室外夜晚光照，月光效果
- STUDIO_THREE_POINT: 影棚三点光照，专业渲染
- DRAMATIC_HIGH_CONTRAST: 戏剧性高对比度光照

### 材质预设 (MaterialPresets)：
- BASIC_DIFFUSE: 基础漫反射材质
- PBR_METAL_SMOOTH/ROUGH: PBR金属材质
- PBR_PLASTIC_SMOOTH/ROUGH: PBR塑料材质
- CARTOON_FLAT/TOON: 卡通风格材质
- HOLOGRAM: 全息投影效果
- NEON_GLOW: 霓虹发光效果
- WOOD/STONE/FABRIC: 自然材质

### 粒子系统预设 (ParticlePresets)：
- FIRE_SMALL/LARGE: 火焰效果
- SMOKE_LIGHT/DENSE: 烟雾效果
- EXPLOSION_SMALL/LARGE: 爆炸效果
- MAGIC_SPARKLE/ENERGY: 魔法特效
- RAIN/SNOW: 环境天气效果

### UI组件 (GameUIComponents)：
- HealthBar: 血条组件，支持多种主题
- ScoreDisplay: 分数显示组件
- GameButton: 游戏按钮组件
- GameHUD: 完整的游戏界面

## 📋 设计原则：
- 组件优先：优先使用标准化预设组件，减少自定义开发
- 移动优化：考虑触屏操作和移动端性能
- 技术可行：基于Babylon.js 8.17.2和现有组件库
- 性能友好：选择合适的组件组合，避免性能瓶颈
- 用户体验：简单易懂，快速上手，有趣耐玩

请始终以JSON格式输出游戏设计方案，并明确指定使用的预设组件。`,
      // 使用游戏设计专用的temperature配置
      model: new CustomClaude4Client({
        ...DEFAULT_CLAUDE4_CONFIG,
        ...AGENT_TEMPERATURE_CONFIGS.GAME_DESIGN,
        agentType: "GameDesignAgent",
        agentName: "游戏设计师"
      }),
      ...config
    };

    super(defaultConfig);
  }

  // 执行游戏设计任务
  protected async executeNode(state: AgentState): Promise<Partial<AgentState>> {
    try {
      // 构建设计提示
      const designPrompt = this.buildDesignPrompt(state);
      
      // 调用模型生成设计
      const response = await this.model.invoke([
        { role: "system", content: this.systemPrompt },
        { role: "user", content: designPrompt }
      ]);

      // 解析和验证设计结果
      const gameDesign = await this.parseAndValidateDesign(response.content as string);
      
      return {
        status: "completed",
        messages: [new AIMessage({ 
          content: `游戏设计完成！\n\n${this.formatDesignOutput(gameDesign)}` 
        })],
        context: {
          ...state.context,
          gameDesign,
          designComplete: true
        }
      };
    } catch (error) {
      return {
        status: "error",
        messages: [new AIMessage({ 
          content: `设计过程中出现错误: ${error instanceof Error ? error.message : '未知错误'}` 
        })]
      };
    }
  }

  // 构建设计提示
  private buildDesignPrompt(state: AgentState): string {
    const userInput = state.context.lastInput || "";
    const task = state.currentTask;

    return `
基于以下用户需求设计一个Web小游戏，充分利用我们的标准化组件库：

用户需求: ${userInput}
任务描述: ${task}

## 🎯 设计要求：
1. 必须明确指定使用的预设组件（相机、光照、材质、粒子系统、UI组件）
2. 基于组件特性设计游戏玩法，确保技术实现的高效性
3. 考虑移动端优化和性能表现
4. 设计应该能够快速实现，减少自定义开发工作量

## 📋 请设计包含以下要素的完整游戏方案：
1. 游戏类型和主题
2. 核心玩法机制
3. 技术组件选择（必须从预设组件库中选择）
4. 游戏目标和胜利条件
5. 控制方式和交互设计
6. 视觉风格和特效设计
7. UI界面设计

请以JSON格式输出设计方案，严格按照以下结构：
{
  "gameType": "游戏类型(action/puzzle/adventure/strategy/casual)",
  "theme": "游戏主题",
  "mechanics": ["机制1", "机制2"],
  "objectives": ["目标1", "目标2"],
  "difficulty": "难度(easy/medium/hard)",
  "estimatedPlayTime": "预计时长",
  "targetAudience": "目标用户",
  "visualStyle": "视觉风格描述",
  "controls": {
    "primary": ["主要控制方式"],
    "secondary": ["次要控制方式"]
  },
  "features": ["特色功能1", "特色功能2"],
  "technicalComponents": {
    "camera": {
      "type": "相机预设类型(THIRD_PERSON/FIRST_PERSON/TOP_DOWN/FOLLOW/FREE/OVER_SHOULDER)",
      "reason": "选择此相机的原因"
    },
    "lighting": {
      "type": "光照预设类型(INDOOR_BASIC/OUTDOOR_DAY/OUTDOOR_SUNSET/OUTDOOR_NIGHT/STUDIO_THREE_POINT/DRAMATIC_HIGH_CONTRAST)",
      "reason": "选择此光照的原因"
    },
    "materials": [
      {
        "usage": "材质用途描述",
        "type": "材质预设类型",
        "reason": "选择此材质的原因"
      }
    ],
    "particles": [
      {
        "usage": "粒子效果用途",
        "type": "粒子预设类型",
        "reason": "选择此粒子效果的原因"
      }
    ],
    "ui": [
      {
        "component": "UI组件名称",
        "usage": "组件用途描述"
      }
    ]
  }
}
`;
  }

  // 解析和验证设计结果
  private async parseAndValidateDesign(content: string): Promise<GameDesign> {
    try {
      // 提取JSON部分
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error("未找到有效的JSON格式设计方案");
      }

      const jsonStr = jsonMatch[0];
      const parsedDesign = JSON.parse(jsonStr);

      // 使用Zod验证
      const validatedDesign = GameDesignSchema.parse(parsedDesign);

      // 使用智能模板选择系统优化设计
      const optimizedDesign = await this.optimizeDesignWithTemplates(validatedDesign);

      return optimizedDesign;
    } catch (error) {
      throw new Error(`设计方案解析失败: ${error instanceof Error ? error.message : '格式错误'}`);
    }
  }

  // 使用智能模板选择系统优化设计
  private async optimizeDesignWithTemplates(design: GameDesign): Promise<GameDesign> {
    try {
      // 从设计中提取选择标准
      const criteria = SmartTemplateSelector.extractCriteriaFromGameDesign(design);

      // 选择最适合的模板
      const selectionResult = SmartTemplateSelector.selectTemplate(criteria);

      console.log(`[GameDesignAgent] 智能模板选择结果:`, {
        template: selectionResult.selectedTemplate.name,
        confidence: selectionResult.confidence,
        reasoning: selectionResult.reasoning
      });

      // 将模板组件信息集成到设计中
      const optimizedDesign = {
        ...design,
        technicalComponents: selectionResult.selectedTemplate.components,
        templateInfo: {
          selectedTemplate: selectionResult.selectedTemplate.name,
          confidence: selectionResult.confidence,
          reasoning: selectionResult.reasoning,
          alternatives: selectionResult.alternatives.map(alt => ({
            name: alt.template.name,
            score: alt.score,
            reason: alt.reason
          })),
          optimizations: selectionResult.optimizations
        }
      };

      return optimizedDesign;
    } catch (error) {
      console.error(`[GameDesignAgent] 模板优化失败:`, error);
      // 如果模板优化失败，返回原始设计
      return design;
    }
  }

  // 格式化设计输出
  private formatDesignOutput(design: GameDesign): string {
    return `
🎮 **游戏设计方案**

**基本信息**
- 游戏类型: ${design.gameType}
- 主题: ${design.theme}
- 难度: ${design.difficulty}
- 预计时长: ${design.estimatedPlayTime}
- 目标用户: ${design.targetAudience}

**核心机制**
${design.mechanics.map(m => `- ${m}`).join('\n')}

**游戏目标**
${design.objectives.map(o => `- ${o}`).join('\n')}

**控制方式**
- 主要: ${design.controls.primary.join(', ')}
${design.controls.secondary ? `- 次要: ${design.controls.secondary.join(', ')}` : ''}

**视觉风格**
${design.visualStyle}

**特色功能**
${design.features.map(f => `- ${f}`).join('\n')}
`;
  }

  // 获取设计结果
  public async getGameDesign(threadId: string): Promise<GameDesign | null> {
    const state = await this.getState(threadId);
    return state?.values?.context?.gameDesign || null;
  }

  // 验证设计方案
  public validateDesign(design: any): { valid: boolean; errors: string[] } {
    try {
      GameDesignSchema.parse(design);
      return { valid: true, errors: [] };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          valid: false,
          errors: error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
        };
      }
      return { valid: false, errors: ['未知验证错误'] };
    }
  }
}
