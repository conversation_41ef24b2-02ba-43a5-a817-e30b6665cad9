/**
 * SmartTemplateSelector - 智能模板选择系统
 * 基于游戏需求和上下文智能选择最适合的组件模板
 * 提供AI驱动的组件推荐和优化建议
 */

import { ComponentTemplateManager, ComponentTemplate, GameType } from './ComponentTemplateManager';
import { GameDesign } from './GameDesignAgent';

// 选择标准接口
export interface TemplateSelectionCriteria {
  gameType: GameType;
  theme?: string;
  targetAudience?: string;
  platform?: 'mobile' | 'desktop' | 'both';
  performanceTarget?: 'low' | 'medium' | 'high';
  visualStyle?: string;
  mechanics?: string[];
  estimatedPlayTime?: string;
}

// 选择结果接口
export interface TemplateSelectionResult {
  selectedTemplate: ComponentTemplate;
  confidence: number; // 0-1之间的置信度
  reasoning: string;
  alternatives: Array<{
    template: ComponentTemplate;
    score: number;
    reason: string;
  }>;
  optimizations: Array<{
    component: string;
    suggestion: string;
    impact: 'low' | 'medium' | 'high';
  }>;
}

// 评分权重配置
interface ScoringWeights {
  gameTypeMatch: number;
  platformCompatibility: number;
  performanceAlignment: number;
  visualStyleMatch: number;
  mechanicsSupport: number;
  audienceAlignment: number;
}

/**
 * 智能模板选择器类
 */
export class SmartTemplateSelector {
  private static readonly DEFAULT_WEIGHTS: ScoringWeights = {
    gameTypeMatch: 0.3,
    platformCompatibility: 0.25,
    performanceAlignment: 0.2,
    visualStyleMatch: 0.1,
    mechanicsSupport: 0.1,
    audienceAlignment: 0.05
  };

  /**
   * 智能选择最适合的组件模板
   */
  public static selectTemplate(
    criteria: TemplateSelectionCriteria,
    weights: Partial<ScoringWeights> = {}
  ): TemplateSelectionResult {
    const finalWeights = { ...this.DEFAULT_WEIGHTS, ...weights };
    const allTemplates = ComponentTemplateManager.getAllTemplates();
    
    // 为每个模板计算分数
    const scoredTemplates = allTemplates.map(template => ({
      template,
      score: this.calculateTemplateScore(template, criteria, finalWeights),
      reasoning: this.generateScoreReasoning(template, criteria)
    }));

    // 按分数排序
    scoredTemplates.sort((a, b) => b.score - a.score);

    const bestTemplate = scoredTemplates[0];
    const alternatives = scoredTemplates.slice(1, 4); // 取前3个备选方案

    return {
      selectedTemplate: bestTemplate.template,
      confidence: bestTemplate.score,
      reasoning: this.generateSelectionReasoning(bestTemplate.template, criteria, bestTemplate.score),
      alternatives: alternatives.map(item => ({
        template: item.template,
        score: item.score,
        reason: item.reasoning
      })),
      optimizations: this.generateOptimizations(bestTemplate.template, criteria)
    };
  }

  /**
   * 从游戏设计中提取选择标准
   */
  public static extractCriteriaFromGameDesign(gameDesign: GameDesign): TemplateSelectionCriteria {
    return {
      gameType: this.mapGameTypeString(gameDesign.gameType),
      theme: gameDesign.theme,
      targetAudience: gameDesign.targetAudience,
      platform: this.inferPlatform(gameDesign),
      performanceTarget: this.inferPerformanceTarget(gameDesign),
      visualStyle: gameDesign.visualStyle,
      mechanics: gameDesign.mechanics,
      estimatedPlayTime: gameDesign.estimatedPlayTime
    };
  }

  /**
   * 计算模板分数
   */
  private static calculateTemplateScore(
    template: ComponentTemplate,
    criteria: TemplateSelectionCriteria,
    weights: ScoringWeights
  ): number {
    let totalScore = 0;

    // 游戏类型匹配度
    const gameTypeScore = template.gameTypes.includes(criteria.gameType) ? 1 : 0;
    totalScore += gameTypeScore * weights.gameTypeMatch;

    // 平台兼容性
    const platformScore = this.calculatePlatformScore(template, criteria.platform);
    totalScore += platformScore * weights.platformCompatibility;

    // 性能对齐度
    const performanceScore = this.calculatePerformanceScore(template, criteria.performanceTarget);
    totalScore += performanceScore * weights.performanceAlignment;

    // 视觉风格匹配度
    const visualScore = this.calculateVisualStyleScore(template, criteria.visualStyle);
    totalScore += visualScore * weights.visualStyleMatch;

    // 机制支持度
    const mechanicsScore = this.calculateMechanicsScore(template, criteria.mechanics);
    totalScore += mechanicsScore * weights.mechanicsSupport;

    // 目标用户对齐度
    const audienceScore = this.calculateAudienceScore(template, criteria.targetAudience);
    totalScore += audienceScore * weights.audienceAlignment;

    return Math.min(totalScore, 1); // 确保分数不超过1
  }

  /**
   * 计算平台兼容性分数
   */
  private static calculatePlatformScore(template: ComponentTemplate, platform?: string): number {
    if (!platform) return 0.5;

    switch (platform) {
      case 'mobile':
        return template.performance.mobileOptimized ? 1 : 0.3;
      case 'desktop':
        return template.performance.complexity === 'high' ? 1 : 0.7;
      case 'both':
        return template.performance.mobileOptimized ? 0.9 : 0.6;
      default:
        return 0.5;
    }
  }

  /**
   * 计算性能对齐分数
   */
  private static calculatePerformanceScore(template: ComponentTemplate, target?: string): number {
    if (!target) return 0.5;

    const complexityMap = { low: 1, medium: 2, high: 3 };
    const templateComplexity = complexityMap[template.performance.complexity];
    const targetComplexity = complexityMap[target as keyof typeof complexityMap] || 2;

    // 性能要求越接近越好
    const difference = Math.abs(templateComplexity - targetComplexity);
    return Math.max(0, 1 - difference * 0.3);
  }

  /**
   * 计算视觉风格匹配分数
   */
  private static calculateVisualStyleScore(template: ComponentTemplate, visualStyle?: string): number {
    if (!visualStyle) return 0.5;

    const style = visualStyle.toLowerCase();
    
    // 基于模板中的材质类型推断视觉风格匹配度
    const materials = template.components.materials;
    
    if (style.includes('卡通') || style.includes('cartoon')) {
      return materials.some(m => m.type.includes('CARTOON')) ? 1 : 0.3;
    }
    
    if (style.includes('现实') || style.includes('realistic')) {
      return materials.some(m => m.type.includes('PBR')) ? 1 : 0.3;
    }
    
    if (style.includes('科幻') || style.includes('sci-fi')) {
      return materials.some(m => m.type.includes('METAL') || m.type.includes('NEON')) ? 1 : 0.3;
    }

    return 0.5; // 默认中等匹配度
  }

  /**
   * 计算机制支持分数
   */
  private static calculateMechanicsScore(template: ComponentTemplate, mechanics?: string[]): number {
    if (!mechanics || mechanics.length === 0) return 0.5;

    // 基于相机类型和游戏类型推断机制支持度
    let supportScore = 0;
    const cameraType = template.components.camera.type;

    mechanics.forEach(mechanic => {
      const lowerMechanic = mechanic.toLowerCase();
      
      if (lowerMechanic.includes('移动') || lowerMechanic.includes('跑动')) {
        supportScore += cameraType.includes('THIRD_PERSON') || cameraType.includes('FOLLOW') ? 0.3 : 0.1;
      }
      
      if (lowerMechanic.includes('射击') || lowerMechanic.includes('瞄准')) {
        supportScore += cameraType.includes('FIRST_PERSON') ? 0.3 : 0.1;
      }
      
      if (lowerMechanic.includes('策略') || lowerMechanic.includes('建造')) {
        supportScore += cameraType.includes('TOP_DOWN') ? 0.3 : 0.1;
      }
    });

    return Math.min(supportScore, 1);
  }

  /**
   * 计算目标用户对齐分数
   */
  private static calculateAudienceScore(template: ComponentTemplate, audience?: string): number {
    if (!audience) return 0.5;

    const audienceLower = audience.toLowerCase();
    
    if (audienceLower.includes('儿童') || audienceLower.includes('休闲')) {
      return template.performance.complexity === 'low' ? 1 : 0.5;
    }
    
    if (audienceLower.includes('核心') || audienceLower.includes('硬核')) {
      return template.performance.complexity === 'high' ? 1 : 0.5;
    }

    return 0.7; // 一般用户默认较高匹配度
  }

  /**
   * 生成选择推理说明
   */
  private static generateSelectionReasoning(
    template: ComponentTemplate,
    criteria: TemplateSelectionCriteria,
    score: number
  ): string {
    const reasons = [];
    
    if (template.gameTypes.includes(criteria.gameType)) {
      reasons.push(`完美匹配${criteria.gameType}游戏类型`);
    }
    
    if (criteria.platform === 'mobile' && template.performance.mobileOptimized) {
      reasons.push('针对移动端优化');
    }
    
    if (template.performance.complexity === 'low') {
      reasons.push('性能友好，适合广泛设备');
    }
    
    reasons.push(`预期帧率${template.performance.estimatedFPS}FPS`);
    
    return `选择此模板的原因：${reasons.join('，')}。总体匹配度：${(score * 100).toFixed(1)}%`;
  }

  /**
   * 生成分数推理说明
   */
  private static generateScoreReasoning(template: ComponentTemplate, criteria: TemplateSelectionCriteria): string {
    return `${template.name}：${template.description}`;
  }

  /**
   * 生成优化建议
   */
  private static generateOptimizations(
    template: ComponentTemplate,
    criteria: TemplateSelectionCriteria
  ): Array<{ component: string; suggestion: string; impact: 'low' | 'medium' | 'high' }> {
    const optimizations = [];

    // 基于平台的优化建议
    if (criteria.platform === 'mobile') {
      if (template.performance.complexity === 'high') {
        optimizations.push({
          component: 'lighting',
          suggestion: '考虑降低阴影质量以提升移动端性能',
          impact: 'medium' as const
        });
      }
      
      optimizations.push({
        component: 'particles',
        suggestion: '减少粒子数量以优化移动端电池续航',
        impact: 'low' as const
      });
    }

    // 基于性能目标的优化建议
    if (criteria.performanceTarget === 'high') {
      optimizations.push({
        component: 'materials',
        suggestion: '使用更高质量的PBR材质以提升视觉效果',
        impact: 'high' as const
      });
    }

    return optimizations;
  }

  /**
   * 辅助方法：映射游戏类型字符串
   */
  private static mapGameTypeString(gameTypeStr: string): GameType {
    const mapping: Record<string, GameType> = {
      'action': GameType.ACTION,
      'puzzle': GameType.PUZZLE,
      'adventure': GameType.ADVENTURE,
      'strategy': GameType.STRATEGY,
      'casual': GameType.CASUAL,
      'shooter': GameType.SHOOTER,
      'racing': GameType.RACING,
      'platformer': GameType.PLATFORMER
    };
    
    return mapping[gameTypeStr.toLowerCase()] || GameType.CASUAL;
  }

  /**
   * 辅助方法：推断平台类型
   */
  private static inferPlatform(gameDesign: GameDesign): 'mobile' | 'desktop' | 'both' {
    const controls = gameDesign.controls;
    
    if (controls.primary.includes('触屏') && !controls.primary.includes('键盘')) {
      return 'mobile';
    }
    
    if (controls.primary.includes('键盘') && !controls.primary.includes('触屏')) {
      return 'desktop';
    }
    
    return 'both';
  }

  /**
   * 辅助方法：推断性能目标
   */
  private static inferPerformanceTarget(gameDesign: GameDesign): 'low' | 'medium' | 'high' {
    if (gameDesign.difficulty === 'easy' && gameDesign.gameType === 'casual') {
      return 'low';
    }
    
    if (gameDesign.gameType === 'action' || gameDesign.gameType === 'shooter') {
      return 'high';
    }
    
    return 'medium';
  }
}
