/**
 * ScriptIterationAgent - 脚本迭代与上下文工程代理
 * 
 * 基于最新的Context Engineering理念实现脚本的持续迭代优化
 * 参考文档：
 * - Context Engineering vs Prompt Engineering (2024)
 * - LangGraph Memory & State Management
 * - Persistent Conversation Context
 */

import { BaseAgent, AgentState } from './BaseAgent';

// 脚本迭代上下文接口
export interface ScriptIterationContext {
  // 当前脚本状态
  currentScript: {
    id: string;
    name: string;
    content: string;
    version: number;
    lastModified: Date;
    functionality: string[];
  };
  
  // 迭代历史
  iterationHistory: Array<{
    version: number;
    timestamp: Date;
    userRequest: string;
    changes: string;
    reasoning: string;
    success: boolean;
    errorMessage?: string;
  }>;
  
  // 上下文记忆（长期记忆）
  contextMemory: {
    // 语义记忆：关于脚本功能的事实
    semanticMemory: {
      scriptPurpose: string;
      coreFeatures: string[];
      dependencies: string[];
      constraints: string[];
      userPreferences: string[];
    };
    
    // 情节记忆：过去的经验和决策
    episodicMemory: Array<{
      decision: string;
      context: string;
      outcome: string;
      lessons: string[];
    }>;
    
    // 程序记忆：编码规范和最佳实践
    proceduralMemory: {
      codingStandards: string[];
      commonPatterns: string[];
      avoidPatterns: string[];
      debuggingMethods: string[];
    };
  };
  
  // 场景上下文
  sceneContext: {
    selectedNode?: {
      id: string;
      name: string;
      type: string;
    };
    availableNodes: Array<{
      id: string;
      name: string;
      type: string;
    }>;
    sceneState: Record<string, unknown>;
  };
}

// 迭代请求接口
export interface IterationRequest {
  userRequest: string;
  scriptId: string;
  iterationType: 'enhance' | 'fix' | 'refactor' | 'extend';
  priority: 'low' | 'medium' | 'high';
  expectedBehavior?: string;
}

// 迭代结果接口
export interface IterationResult {
  success: boolean;
  newVersion: number;
  updatedScript: {
    content: string;
    changes: string;
    reasoning: string;
  };
  contextUpdates: {
    semanticUpdates: string[];
    episodicUpdates: string[];
    proceduralUpdates: string[];
  };
  recommendations: string[];
  errorMessage?: string;
}

export class ScriptIterationAgent extends BaseAgent {
  private contexts: Map<string, ScriptIterationContext> = new Map();
  
  constructor() {
    super({
      name: 'ScriptIterationAgent',
      role: '脚本迭代优化器',
      systemPrompt: '你是专业的Babylon.js脚本迭代优化器，负责对现有脚本进行增量改进。',
      temperature: 0.1
    });
  }

  // 实现抽象方法
  protected async executeNode(state: AgentState): Promise<Partial<AgentState>> {
    // 这个方法暂时不实现，因为我们使用自定义的iterateScript方法
    return state;
  }

  /**
   * 初始化脚本上下文
   */
  async initializeContext(scriptId: string, scriptContent: string, metadata: Record<string, unknown>): Promise<void> {
    console.log(`[ScriptIterationAgent] 初始化脚本上下文: ${scriptId}`);
    
    const context: ScriptIterationContext = {
      currentScript: {
        id: scriptId,
        name: metadata.name as string || 'unknown_script',
        content: scriptContent,
        version: 1,
        lastModified: new Date(),
        functionality: this.extractFunctionality(scriptContent)
      },
      iterationHistory: [],
      contextMemory: {
        semanticMemory: {
          scriptPurpose: metadata.description as string || '',
          coreFeatures: this.extractFunctionality(scriptContent),
          dependencies: metadata.dependencies as string[] || [],
          constraints: [],
          userPreferences: []
        },
        episodicMemory: [],
        proceduralMemory: {
          codingStandards: [
            '使用Babylon.js 8.17.2 API',
            '通过BABYLON全局对象访问API',
            '使用NodeRegistry获取节点',
            '包含错误处理',
            '避免内存泄漏'
          ],
          commonPatterns: [
            'scene.registerBeforeRender()模式',
            'nodeRegistry.getNode()模式',
            'try-catch错误处理模式'
          ],
          avoidPatterns: [
            '避免硬编码值',
            '避免全局变量污染',
            '避免未处理的异步操作'
          ],
          debuggingMethods: [
            'console.log调试信息',
            '验证节点存在性',
            '检查API可用性'
          ]
        }
      },
      sceneContext: {
        availableNodes: [],
        sceneState: {}
      }
    };
    
    this.contexts.set(scriptId, context);
  }

  /**
   * 执行脚本迭代
   */
  async iterateScript(request: IterationRequest): Promise<IterationResult> {
    console.log(`[ScriptIterationAgent] 开始迭代脚本: ${request.scriptId}`);
    
    const context = this.contexts.get(request.scriptId);
    if (!context) {
      throw new Error(`脚本上下文不存在: ${request.scriptId}`);
    }

    try {
      // 构建迭代提示
      const prompt = this.buildIterationPrompt(request, context);
      
      // 调用模型进行迭代
      const response = await this.model.invoke([
        { role: "system", content: this.getIterationSystemPrompt() },
        { role: "user", content: prompt }
      ]);
      
      const content = response.content as string;
      
      // 解析迭代结果
      const result = this.parseIterationResult(content, context);
      
      // 更新上下文
      await this.updateContext(request.scriptId, request, result);
      
      console.log(`[ScriptIterationAgent] 脚本迭代完成: v${result.newVersion}`);
      return result;
      
    } catch (error) {
      console.error('[ScriptIterationAgent] 脚本迭代失败:', error);
      
      // 记录失败的迭代
      context.iterationHistory.push({
        version: context.currentScript.version,
        timestamp: new Date(),
        userRequest: request.userRequest,
        changes: '',
        reasoning: '',
        success: false,
        errorMessage: error instanceof Error ? error.message : '未知错误'
      });
      
      return {
        success: false,
        newVersion: context.currentScript.version,
        updatedScript: {
          content: context.currentScript.content,
          changes: '',
          reasoning: ''
        },
        contextUpdates: {
          semanticUpdates: [],
          episodicUpdates: [],
          proceduralUpdates: []
        },
        recommendations: [],
        errorMessage: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 获取脚本完整上下文（用于新Agent接入）
   */
  async getScriptContext(scriptId: string): Promise<ScriptIterationContext | null> {
    return this.contexts.get(scriptId) || null;
  }

  /**
   * 构建迭代提示
   */
  private buildIterationPrompt(request: IterationRequest, context: ScriptIterationContext): string {
    return `# 脚本迭代任务

## 上下文信息（Context Engineering）

### 当前脚本状态
- **脚本ID**: ${context.currentScript.id}
- **脚本名称**: ${context.currentScript.name}
- **当前版本**: v${context.currentScript.version}
- **核心功能**: ${context.currentScript.functionality.join(', ')}

### 迭代历史（避免重复错误）
${context.iterationHistory.slice(-3).map(h => 
  `- v${h.version}: ${h.userRequest} → ${h.success ? '成功' : '失败: ' + h.errorMessage}`
).join('\n')}

### 语义记忆（脚本目的和约束）
- **脚本目的**: ${context.contextMemory.semanticMemory.scriptPurpose}
- **核心特性**: ${context.contextMemory.semanticMemory.coreFeatures.join(', ')}
- **依赖项**: ${context.contextMemory.semanticMemory.dependencies.join(', ')}
- **用户偏好**: ${context.contextMemory.semanticMemory.userPreferences.join(', ')}

### 情节记忆（过往经验）
${context.contextMemory.episodicMemory.slice(-2).map(e => 
  `- 决策: ${e.decision}\n  结果: ${e.outcome}\n  教训: ${e.lessons.join(', ')}`
).join('\n')}

### 程序记忆（编码规范）
- **必须遵循**: ${context.contextMemory.proceduralMemory.codingStandards.join(', ')}
- **推荐模式**: ${context.contextMemory.proceduralMemory.commonPatterns.join(', ')}
- **避免模式**: ${context.contextMemory.proceduralMemory.avoidPatterns.join(', ')}

## 当前脚本代码
\`\`\`javascript
${context.currentScript.content}
\`\`\`

## 迭代需求
- **类型**: ${request.iterationType}
- **优先级**: ${request.priority}
- **用户请求**: ${request.userRequest}
- **期望行为**: ${request.expectedBehavior || '未指定'}

## 任务要求

1. **基于现有脚本进行增量修改**，不要重写整个脚本
2. **保持已有功能的完整性**，只添加或修改请求的部分
3. **参考迭代历史避免重复错误**
4. **遵循程序记忆中的编码规范**
5. **输出简洁专注的代码，避免冗余**

请返回修改后的完整脚本代码，并在代码前后用如下格式说明：

**变更说明**: [简要描述做了什么修改]
**修改原因**: [为什么这样修改]

\`\`\`javascript
[修改后的完整脚本代码]
\`\`\`

**上下文更新**:
- 语义记忆更新: [如有新的事实或约束需要记录]
- 情节记忆更新: [本次决策的经验总结]
- 程序记忆更新: [如有新的编码模式或规范]`;
  }

  /**
   * 获取迭代系统提示
   */
  private getIterationSystemPrompt(): string {
    return `你是专业的Babylon.js 8.17.2脚本迭代优化器，负责对现有脚本进行增量改进。

## 核心原则
1. **增量迭代**：基于现有代码进行最小化修改，不重写整个脚本
2. **上下文感知**：充分利用提供的语义记忆、情节记忆和程序记忆
3. **避免退化**：绝不撤销之前成功的修改
4. **保持一致性**：遵循已建立的编码模式和约定

## 技术要求
- 使用Babylon.js 8.17.2 API
- 通过BABYLON全局对象访问所有功能
- 使用NodeRegistry.getInstance()获取节点
- 包含适当的错误处理
- 避免内存泄漏

## 输出格式
必须包含变更说明、修改原因、完整代码和上下文更新。
代码必须简洁高效，专注于实现用户请求的功能。`;
  }

  /**
   * 解析迭代结果
   */
  private parseIterationResult(response: string, context: ScriptIterationContext): IterationResult {
    try {
      // 提取变更说明
      const changesMatch = response.match(/\*\*变更说明\*\*:\s*([^\n]+)/);
      const changes = changesMatch ? changesMatch[1].trim() : '未提供变更说明';
      
      // 提取修改原因
      const reasoningMatch = response.match(/\*\*修改原因\*\*:\s*([^\n]+)/);
      const reasoning = reasoningMatch ? reasoningMatch[1].trim() : '未提供修改原因';
      
      // 提取代码内容
      const codeMatch = response.match(/```javascript\s*([\s\S]*?)\s*```/);
      if (!codeMatch) {
        throw new Error('无法从响应中提取代码内容');
      }
      
      const updatedCode = codeMatch[1].trim();
      
      // 提取上下文更新
      const semanticUpdatesMatch = response.match(/语义记忆更新:\s*([^\n]+)/);
      const episodicUpdatesMatch = response.match(/情节记忆更新:\s*([^\n]+)/);
      const proceduralUpdatesMatch = response.match(/程序记忆更新:\s*([^\n]+)/);
      
      const contextUpdates = {
        semanticUpdates: semanticUpdatesMatch ? [semanticUpdatesMatch[1].trim()] : [],
        episodicUpdates: episodicUpdatesMatch ? [episodicUpdatesMatch[1].trim()] : [],
        proceduralUpdates: proceduralUpdatesMatch ? [proceduralUpdatesMatch[1].trim()] : []
      };
      
      return {
        success: true,
        newVersion: context.currentScript.version + 1,
        updatedScript: {
          content: updatedCode,
          changes,
          reasoning
        },
        contextUpdates,
                 recommendations: this.generateRecommendations(updatedCode)
      };
      
    } catch (error) {
      throw new Error(`解析迭代结果失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 更新上下文
   */
  private async updateContext(scriptId: string, request: IterationRequest, result: IterationResult): Promise<void> {
    const context = this.contexts.get(scriptId);
    if (!context) return;
    
    // 更新当前脚本
    context.currentScript.content = result.updatedScript.content;
    context.currentScript.version = result.newVersion;
    context.currentScript.lastModified = new Date();
    context.currentScript.functionality = this.extractFunctionality(result.updatedScript.content);
    
    // 添加迭代历史
    context.iterationHistory.push({
      version: result.newVersion,
      timestamp: new Date(),
      userRequest: request.userRequest,
      changes: result.updatedScript.changes,
      reasoning: result.updatedScript.reasoning,
      success: result.success
    });
    
    // 更新语义记忆
    if (result.contextUpdates.semanticUpdates.length > 0) {
      context.contextMemory.semanticMemory.coreFeatures.push(...result.contextUpdates.semanticUpdates);
    }
    
    // 更新情节记忆
    if (result.contextUpdates.episodicUpdates.length > 0) {
      context.contextMemory.episodicMemory.push({
        decision: request.userRequest,
        context: request.iterationType,
        outcome: result.success ? '成功' : '失败',
        lessons: result.contextUpdates.episodicUpdates
      });
    }
    
    // 更新程序记忆
    if (result.contextUpdates.proceduralUpdates.length > 0) {
      context.contextMemory.proceduralMemory.commonPatterns.push(...result.contextUpdates.proceduralUpdates);
    }
    
    // 限制记忆大小（保持最近的记录）
    if (context.iterationHistory.length > 10) {
      context.iterationHistory = context.iterationHistory.slice(-10);
    }
    if (context.contextMemory.episodicMemory.length > 5) {
      context.contextMemory.episodicMemory = context.contextMemory.episodicMemory.slice(-5);
    }
  }

  /**
   * 提取脚本功能
   */
  private extractFunctionality(scriptContent: string): string[] {
    const functionality: string[] = [];
    
    if (scriptContent.includes('rotation')) functionality.push('旋转功能');
    if (scriptContent.includes('position')) functionality.push('位置控制');
    if (scriptContent.includes('scaling')) functionality.push('缩放功能');
    if (scriptContent.includes('material')) functionality.push('材质控制');
    if (scriptContent.includes('Animation')) functionality.push('动画系统');
    if (scriptContent.includes('registerBeforeRender')) functionality.push('渲染循环');
    if (scriptContent.includes('addEventListener')) functionality.push('交互事件');
    
    return functionality;
  }

        /**
    * 生成推荐
    */
   private generateRecommendations(scriptContent: string): string[] {
     const recommendations: string[] = [];
     
     // 基于脚本分析生成推荐
     if (scriptContent.includes('setInterval') || scriptContent.includes('setTimeout')) {
       recommendations.push('考虑使用scene.registerBeforeRender()替代定时器');
     }
     
     if (!scriptContent.includes('try') && !scriptContent.includes('catch')) {
       recommendations.push('建议添加错误处理机制');
     }
     
     if (scriptContent.split('\n').length > 50) {
       recommendations.push('脚本较长，建议考虑模块化拆分');
     }
     
     return recommendations;
   }
} 