import { BaseAgent, AgentState, AgentConfig } from "./BaseAgent";
import { AIMessage } from "@langchain/core/messages";
import { GameDesign } from "./GameDesignAgent";
import { CustomClaude4Client, DEFAULT_CLAUDE4_CONFIG } from "./CustomClaude4Client";
import { AGENT_CONFIG } from '../config/agent-config';
import { mcpDocClient } from '../utils/MCPDocumentationClient';

// 代码生成配置
export interface CodeGenerationConfig {
  framework: "three.js" | "babylon.js";
  outputFormat: "tsx" | "module";
  includeAssets: boolean;
  minify: boolean;
}

// 生成的代码结构
export interface GeneratedCode {
  // 完整的TSX组件代码（包含样式和类型）
  component: string;
  // 组件文件名
  fileName: string;
  // 依赖项
  dependencies: string[];
  // 资源文件
  assets: {
    textures: string[];
    models: string[];
    sounds: string[];
    other: string[];
  };
  // 组件元数据
  metadata: {
    componentName: string;
    gameType: string;
    description: string;
  };
}

// 脚本文件接口
export interface GeneratedScript {
  id: string;
  name: string;
  content: string;
  nodeId?: string;
  createdAt: Date;
  lastModified: Date;
  isActive: boolean;
  metadata: {
    description: string;
    targetNodeTypes: string[];
    dependencies: string[];
    functionType: 'animation' | 'interaction' | 'behavior' | 'utility' | 'effect';
  };
}

// 场景上下文接口
export interface SceneContext {
  totalNodes: number;
  nodesByType: Record<string, number>;
  nodes: Array<{
    id: string;
    name: string;
    type: string;
    position: { x: number; y: number; z: number };
    rotation: { x: number; y: number; z: number };
    scaling: { x: number; y: number; z: number };
  }>;
  selectedNode: {
    id: string;
    name: string;
    type: string;
  } | null;
  availableAssets: {
    models: Array<{ name: string; path: string }>;
    textures: Array<{ name: string; path: string }>;
  };
}

// 代码生成Agent
export class CodeGenerationAgent extends BaseAgent {
  private generationConfig: CodeGenerationConfig;
  private apiContext: string;
  
  constructor(config?: Partial<AgentConfig & { generationConfig: CodeGenerationConfig }>) {
    const defaultConfig: AgentConfig = {
      name: "CodeGenerator",
      role: "代码生成器",
      systemPrompt: `你是专业的Three.js 0.178.0游戏开发工程师，专精于节点化架构设计和现代3D Web开发。

## 🎯 核心能力
1. 使用Three.js 0.178.0的最新特性构建高性能3D游戏
2. 实现基于节点的可配置游戏架构
3. 自动注册和管理所有游戏对象到节点系统
4. 遇到API不确定时主动查询最新文档
5. 优化移动端和WebGL性能

## 🏗️ 技术栈 (Three.js 0.178.0)
- **引擎**: Three.js 0.178.0 + WebGL支持
- **框架**: React 18 + TypeScript
- **架构**: 基于NodeRegistry的节点化管理
- **状态**: useRef(游戏状态) + useState(UI同步)
- **渲染循环**: requestAnimationFrame + renderer.render()
- **性能**: 现代着色器 + 优化的渲染管线

## 🎮 节点化架构核心规范
### 必须导入节点系统：
\`\`\`typescript
import { NodeFactory } from '../../../src/utils/NodeFactory';
import { NodeRegistry } from '../../../src/utils/NodeRegistry';
import { GameNodeType } from '../../../src/types/NodeTypes';
\`\`\`

### 使用NodeFactory创建节点（推荐方式）：
优先使用NodeFactory标准化创建节点，自动处理注册：

\`\`\`typescript
// 获取NodeFactory实例
const nodeFactory = NodeFactory.getInstance();

// 创建玩家方块（自动注册到NodeRegistry）
const playerBox = nodeFactory.createMeshNode(scene, {
  id: "player_box",
  name: "玩家",
  geometryType: "box",
  geometryParams: { size: 2 },
  position: new Vector3(0, 1, 0),
  materialType: "pbr",
  materialConfig: {
    baseColor: Color3.Blue(),
    metallic: 0.1,
    roughness: 0.5
  }
});

// 创建主摄像机
const camera = nodeFactory.createCameraNode(scene, {
  id: "main_camera",
  name: "主摄像机",
  cameraType: "arc_rotate",
  position: new Vector3(0, 5, -10),
  target: Vector3.Zero(),
  fov: 0.8
});

// 创建光源
const light = nodeFactory.createLightNode(scene, {
  id: "main_light",
  name: "主光源",
  lightType: "hemispheric",
  intensity: 1.0,
  color: Color3.White()
});
\`\`\`

### 使用游戏模板（高效方式）：
对于标准游戏类型，优先使用预定义模板：

\`\`\`typescript
import { GameTemplateGenerator, getBestTemplateForGameType } from '../../../src/templates/GameTemplates';

// 根据游戏类型获取最佳模板
const template = getBestTemplateForGameType(gameType);

// 生成完整的游戏组件代码
const gameCode = GameTemplateGenerator.generateGameComponent(template, gameId);
\`\`\`

## 🚨 Three.js 0.178.0 关键API规范
- **渲染器初始化**: \`new THREE.WebGLRenderer({ canvas, antialias: true })\`
- **场景创建**: \`new THREE.Scene()\`
- **相机控制**: \`new OrbitControls(camera, renderer.domElement)\`
- **材质系统**: 优先使用MeshStandardMaterial和PBR材质
- **性能优化**: 启用阴影、雾效和几何体合并
- **资源管理**: 正确dispose几何体和材质

## 📋 代码生成要求
生成的组件必须包含：

1. **完整导入声明**
\`\`\`typescript
import React, { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { NodeRegistry } from '../utils/NodeRegistry';
import { GameNodeType, MeshNodeProperties, LightNodeProperties, CameraNodeProperties } from '../types/NodeTypes';
\`\`\`

2. **节点注册函数**
\`\`\`typescript
const registerGameNode = (babylonObject: any, nodeData: any) => {
  const nodeRegistry = NodeRegistry.getInstance();
  nodeRegistry.register(nodeData);
};
\`\`\`

3. **游戏状态与节点同步**
\`\`\`typescript
const gameState = useRef({
  score: 0,
  level: 1,
  nodeRegistry: NodeRegistry.getInstance()
});
\`\`\`

4. **资源清理与节点注销**
\`\`\`typescript
useEffect(() => {
  return () => {
    if (scene) {
      const nodeRegistry = NodeRegistry.getInstance();
      nodeRegistry.clear(); // 清理所有注册的节点
      scene.dispose();
    }
  };
}, []);
\`\`\`

## 🎯 节点类型指南
- **MESH**: 所有可见几何体（玩家、敌人、道具、环境）
- **LIGHT**: 光源系统（方向光、点光源、聚光灯）  
- **CAMERA**: 相机节点（第三人称、第一人称、跟随相机）
- **PARTICLE_SYSTEM**: 粒子效果（爆炸、火焰、烟雾）
- **SOUND**: 音频节点（背景音乐、音效）
- **ANIMATION_GROUP**: 动画组合

## 🔄 实时同步机制
确保Three.js对象属性变化时同步到NodeRegistry：
\`\`\`typescript
// 在游戏循环中同步节点状态
function animate() {
  // 游戏逻辑...

  // 同步节点位置到NodeRegistry
  const nodeRegistry = NodeRegistry.getInstance();
  nodeRegistry.updateNode("player_box", {
    position: playerMesh.position,
    rotation: playerMesh.rotation
  });
});
\`\`\`

## ⚡ 性能优化要求
- 使用WebGPU引擎（如果可用）
- 启用实例化渲染大量相同对象
- 使用LOD系统处理复杂模型
- 实现视锥剔除优化
- 启用纹理压缩和mipmapping

## 🎮 交互支持
- 移动端：触控手势、陀螺仪
- 桌面端：鼠标、键盘、滚轮
- 响应式UI：自适应屏幕尺寸

## 📝 代码质量要求
- TypeScript严格模式，完整类型定义
- 清晰的函数命名和注释
- 模块化设计，职责分离
- 错误处理和边界情况处理
- 性能监控和调试友好

如果遇到Three.js 0.178.0 API不确定的情况，立即说明"需要查询最新Three.js 0.178.0文档以确保API正确性"。

直接输出完整TSX代码，无需markdown标记。`,
      // 代码生成需要更长时间，使用专门的客户端配置和优化的temperature
      model: new CustomClaude4Client({
        ...DEFAULT_CLAUDE4_CONFIG,
        temperature: 0.1, // 降低temperature确保输出简洁准确
        timeout: AGENT_CONFIG.TIMEOUT.LONG, // 使用环境变量配置的长任务超时
        maxTokens: AGENT_CONFIG.MAX_TOKENS.CODE_GENERATION, // 使用环境变量配置的代码生成token数
        agentType: 'CodeGenerationAgent',
        agentName: '代码生成器'
      }),
      ...config
    };

    super(defaultConfig);
    
    this.generationConfig = config?.generationConfig || {
      framework: "three.js",
      outputFormat: "tsx",
      includeAssets: true,
      minify: false
    };

    this.apiContext = this.buildAPIContext();
  }

  /**
   * 构建API上下文信息 - 提供Babylon.js基础知识和最佳实践
   */
  private buildAPIContext(): string {
    return `
## 🛠️ Babylon.js 8.17.2 基础API参考

你可以自由使用所有Babylon.js的原生API来实现游戏功能。以下是一些重要的API用法提醒：

### 🚨 关键API正确用法

#### 引擎和场景初始化
\`\`\`typescript
import * as React from 'react';
import { useEffect, useRef, useState } from 'react';
import * as BABYLON from '@babylonjs/core';
import * as CANNON from 'cannon'; // 如果需要物理引擎

// 引擎初始化
const engine = new BABYLON.Engine(canvas, true);
const scene = new BABYLON.Scene(engine);
\`\`\`

#### 相机控制（重要修复）
\`\`\`typescript
// ✅ 正确用法
camera.attachControl(canvas, true);  // 不是 attachToCanvas
camera.setTarget(BABYLON.Vector3.Zero());
\`\`\`

#### 游戏循环（必须使用）
\`\`\`typescript
// ✅ 正确的游戏循环 - 使用scene.onBeforeRenderObservable
scene.onBeforeRenderObservable.add(() => {
  // 游戏逻辑更新
});

// ❌ 不要使用 requestAnimationFrame
\`\`\`

#### Action系统（重要修复）
\`\`\`typescript
// ✅ 正确用法 - 带参数的触发器
scene.actionManager.registerAction(new BABYLON.ExecuteCodeAction(
  {
    trigger: BABYLON.ActionManager.OnKeyDownTrigger,
    parameter: "w"  // 键盘参数
  },
  () => { /* 回调函数 */ }
));

// ✅ 正确用法 - 简单触发器（无参数）
scene.actionManager.registerAction(new BABYLON.ExecuteCodeAction(
  BABYLON.ActionManager.OnPickTrigger,
  () => { /* 回调函数 */ }
));
\`\`\`

#### 物理引擎设置
\`\`\`typescript
import * as CANNON from 'cannon';

// ✅ 正确的物理引擎启用方式
scene.enablePhysics(new BABYLON.Vector3(0, -9.81, 0), new BABYLON.CannonJSPlugin(true, 10, CANNON));

// 设置物理属性
mesh.physicsImpostor = new BABYLON.PhysicsImpostor(mesh, BABYLON.PhysicsImpostor.BoxImpostor, { mass: 1, restitution: 0.7 }, scene);
\`\`\`

### 🎮 推荐的开发模式

1. **状态管理**: 使用 useRef 存储游戏状态，useState 管理UI显示
2. **资源清理**: 在 useEffect 返回函数中 dispose 所有 Babylon.js 对象
3. **移动端优化**: 支持触屏操作和响应式设计
4. **性能考虑**: 合理使用LOD、instancing、材质共享等技术

### 📦 可选的辅助组件

项目中有一些预设组件可以加速开发，但不是必须使用：
- CameraPresets: 相机快速配置
- LightingPresets: 光照快速配置  
- MaterialPresets: 材质快速配置
- ParticlePresets: 粒子系统快速配置

这些组件完全可选，你可以选择使用或直接使用原生Babylon.js API实现。

### 🎯 输出要求

生成完整的React TSX组件，包含：
1. 所有必需的imports
2. 完整的TypeScript类型定义
3. 游戏状态管理逻辑
4. Babylon.js场景初始化
5. 游戏逻辑实现
6. UI组件和交互
7. 移动端适配
8. 资源清理机制

**重要**：如果遇到不确定的API用法，请说明你需要查询最新文档。

直接输出完整TSX代码，无需markdown标记。
`;
  }

  /**
   * 生成代码时包含完整上下文
   */
  async generateCode(prompt: string, additionalContext?: string): Promise<string> {
    const fullPrompt = `
${this.apiContext}

## 代码生成要求:
${prompt}

${additionalContext || ''}

## 重要提醒:
1. 使用最新的Babylon.js 8.17.2 API
2. 确保所有导入路径正确
3. 可以自由选择使用预设组件或原生Babylon.js API
4. 如果使用资源文件，确认它在资源列表中存在
5. 如果需要的资源不存在，使用程序化生成或默认值
6. 保持代码简洁、可读、高性能
`;

    // 使用模型直接生成代码
    const response = await this.model.invoke([{ 
      role: 'user', 
      content: fullPrompt 
    }]);
    
    return typeof response.content === 'string' ? response.content : JSON.stringify(response.content);
  }

  // 执行代码生成任务
  protected async executeNode(state: AgentState): Promise<Partial<AgentState>> {
    try {
      // 直接从输入消息获取用户需求
      const lastMessage = state.messages[state.messages.length - 1];
      if (!lastMessage || !lastMessage.content) {
        throw new Error("缺少用户需求，无法生成代码");
      }

      const userRequirement = lastMessage.content as string;
      console.log(`[CodeGenerationAgent] 开始直接处理用户需求`);

      // 直接使用新的智能代码生成方法
      const generatedCode = await this.generateGameCodeFromRequirement(userRequirement);

      console.log(`[CodeGenerationAgent] 代码生成完成: ${generatedCode.fileName}`);

      return {
        status: "completed",
        messages: [new AIMessage({
          content: `代码生成完成！\n\n生成了以下文件：\n- TSX组件: ${generatedCode.fileName} (${generatedCode.component.length} 字符)\n- 组件名称: ${generatedCode.metadata.componentName}\n- 游戏类型: ${generatedCode.metadata.gameType}\n- 依赖项: ${generatedCode.dependencies.join(', ')}`
        })],
        context: {
          ...state.context,
          generatedCode: generatedCode,
          gameDesign: null // 不再需要gameDesign
        }
      };
    } catch (error) {
      console.error(`[CodeGenerationAgent] 代码生成失败:`, error);
      return {
        status: "error",
        messages: [new AIMessage({
          content: `代码生成失败: ${error instanceof Error ? error.message : '未知错误'}`
        })],
        context: {
          ...state.context,
          error: error instanceof Error ? error.message : '代码生成失败'
        }
      };
    }
  }

  // 尝试使用模板生成代码
  private async tryGenerateFromTemplate(gameDesign: GameDesign): Promise<{
    success: boolean;
    content?: GeneratedCode;
    error?: string;
  }> {
    try {
      // 导入模板系统（动态导入避免编译时错误）
      const { GameTemplateGenerator, getBestTemplateForGameType } = await import('../templates/GameTemplates');
      
      // 获取游戏类型描述
      const gameTypeDescription = gameDesign.mechanics?.join(' ') || gameDesign.gameType || 'basic3d';
      
      // 获取最佳模板
      const template = getBestTemplateForGameType(gameTypeDescription);
      
      // 生成游戏ID
      const gameId = `game_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      
      // 生成代码
      const componentCode = GameTemplateGenerator.generateGameComponent(template, gameId);
      
      // 构建GeneratedCode结构
      const generatedCode: GeneratedCode = {
        component: componentCode,
        fileName: `${gameId}.tsx`,
        dependencies: [
          'react',
          '@types/react',
          '@babylonjs/core',
          '@babylonjs/materials',
          '@babylonjs/loaders',
          'typescript'
        ],
        assets: {
          textures: [],
          models: [],
          sounds: [],
          other: []
        },
        metadata: {
          componentName: GameTemplateGenerator['toPascalCase'](gameId),
          gameType: template.name,
          description: template.description
        }
      };

      console.log(`[CodeGenerationAgent] 模板生成成功: ${template.name}`);
      
      return {
        success: true,
        content: generatedCode
      };

    } catch (error) {
      console.log(`[CodeGenerationAgent] 模板生成失败:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '模板生成失败'
      };
    }
  }

  // 直接从用户需求生成游戏代码
  private async generateGameCodeFromRequirement(userRequirement: string): Promise<GeneratedCode> {
    console.log('[CodeGenerationAgent] 开始从用户需求生成代码...');

    // 第一次尝试：不查询文档，让AI自主生成
    const initialPrompt = this.buildCodePromptFromRequirement(userRequirement);
    let response = await this.model.invoke([
      { role: "system", content: this.systemPrompt },
      { role: "user", content: initialPrompt }
    ]);

    let content = response.content as string;
    
    // 检查AI是否表示需要查询文档
    const needsDocumentation = this.checkIfNeedsDocumentation(content);
    
    if (needsDocumentation) {
      console.log('[CodeGenerationAgent] AI表示需要查询文档，正在获取Babylon.js API文档...');
      
      // 按需查询相关API文档
      const apiDocs = await this.queryRelevantAPIFromRequirement(userRequirement, content);
      
      // 使用文档重新生成
      const enhancedPrompt = this.buildCodePromptFromRequirement(userRequirement, apiDocs);
      response = await this.model.invoke([
        { role: "system", content: this.systemPrompt },
        { role: "user", content: enhancedPrompt }
      ]);
      content = response.content as string;
    }

    // 解析生成的代码
    return this.parseGeneratedCodeFromRequirement(content, userRequirement);
  }

  // 从用户需求构建代码提示
  private buildCodePromptFromRequirement(userRequirement: string, apiDocs?: string): string {
    return `
根据以下用户需求，生成一个完整的3D游戏React TSX组件：

用户需求：
${userRequirement}

${apiDocs ? `\n参考API文档：\n${apiDocs}\n` : ''}

请生成一个完整的React TSX组件，包含：
1. 所有必需的imports
2. 完整的TypeScript类型定义
3. 游戏状态管理逻辑
4. Babylon.js场景初始化
5. 游戏逻辑实现
6. UI组件和交互
7. 移动端适配
8. 资源清理机制

直接输出完整TSX代码，无需markdown标记。
`;
  }

  // 从用户需求查询相关API
  private async queryRelevantAPIFromRequirement(userRequirement: string, initialResponse: string): Promise<string> {
    try {
      // 分析需要查询的API主题
      const topics = this.analyzeRequiredTopicsFromRequirement(userRequirement, initialResponse);
      
      if (topics.length === 0) {
        return this.getDefaultBabylonJSPractices(['basic']);
      }

      console.log(`[CodeGenerationAgent] 查询API主题: ${topics.join(', ')}`);
      
      // 查询Babylon.js文档 - 使用现有的MCP查询方法
      const topicsStr = topics.join(' ');
      const fakeGameDesign: GameDesign = { gameType: 'action' } as GameDesign;
      const docs = await this.queryRelevantAPI(fakeGameDesign, `topics: ${topicsStr}`);
      return docs;
    } catch (error) {
      console.error(`[CodeGenerationAgent] MCP查询失败:`, error);
      return this.getDefaultBabylonJSPractices(['basic', 'camera', 'materials']);
    }
  }

  // 分析用户需求中需要的API主题
  private analyzeRequiredTopicsFromRequirement(userRequirement: string, initialResponse: string): string[] {
    const allTopics = ['engine', 'scene', 'camera', 'lighting', 'materials', 'textures', 'physics', 'collision', 'animation', 'particles', 'input', 'mobile', 'audio', 'UI'];
    const requiredTopics: string[] = [];
    
    // 基于用户需求分析
    const requirement = userRequirement.toLowerCase();
    if (requirement.includes('minecraft') || requirement.includes('block') || requirement.includes('voxel')) {
      requiredTopics.push('physics', 'collision', 'materials', 'input');
    }
    if (requirement.includes('移动') || requirement.includes('move') || requirement.includes('控制') || requirement.includes('control')) {
      requiredTopics.push('camera', 'input');
    }
    if (requirement.includes('射击') || requirement.includes('shoot') || requirement.includes('attack')) {
      requiredTopics.push('physics', 'collision', 'particles');
    }
    if (requirement.includes('3d') || requirement.includes('三维')) {
      requiredTopics.push('engine', 'scene', 'camera', 'lighting');
    }
    
    // 基于AI初始响应分析
    const response = initialResponse.toLowerCase();
    allTopics.forEach(topic => {
      if (this.isTopicMentioned(response, topic)) {
        if (!requiredTopics.includes(topic)) {
          requiredTopics.push(topic);
        }
      }
    });
    
    // 确保基础主题
    if (requiredTopics.length === 0) {
      requiredTopics.push('basic', 'camera', 'scene');
    }
    
    return requiredTopics.slice(0, 5); // 限制最多5个主题
  }

  // 生成游戏代码
  private async generateGameCode(gameDesign: GameDesign): Promise<GeneratedCode> {
    console.log('[CodeGenerationAgent] 开始生成代码...');

    // 第一次尝试：不查询文档，让AI自主生成
    const initialPrompt = this.buildCodePrompt(gameDesign);
    let response = await this.model.invoke([
      { role: "system", content: this.systemPrompt },
      { role: "user", content: initialPrompt }
    ]);

    let content = response.content as string;
    
    // 检查AI是否表示需要查询文档
    const needsDocumentation = this.checkIfNeedsDocumentation(content);
    
    if (needsDocumentation) {
      console.log('[CodeGenerationAgent] AI表示需要查询文档，正在获取Babylon.js API文档...');
      
      // 按需查询相关API文档
      const apiDocs = await this.queryRelevantAPI(gameDesign, content);
      
      // 使用文档重新生成
      const enhancedPrompt = this.buildCodePrompt(gameDesign, apiDocs);
      response = await this.model.invoke([
        { role: "system", content: this.systemPrompt },
        { role: "user", content: enhancedPrompt }
      ]);
      
      content = response.content as string;
    }

    console.log('[CodeGenerationAgent] 代码生成完成，内容长度:', content.length);

    return this.parseGeneratedCode(content, gameDesign);
  }

  // 检查AI是否需要查询文档
  private checkIfNeedsDocumentation(content: string): boolean {
    const needsDocKeywords = [
      '需要查询',
      '不确定',
      '查询最新文档',
      '需要文档',
      'API用法',
      '不太清楚',
      '需要确认'
    ];
    
    return needsDocKeywords.some(keyword => content.includes(keyword));
  }

  // 智能查询相关API文档
  private async queryRelevantAPI(gameDesign: GameDesign, initialResponse: string): Promise<string> {
    try {
      // 分析需要查询的API主题
      const topics = this.analyzeRequiredTopics(gameDesign, initialResponse);
      console.log(`[CodeGenerationAgent] 查询Babylon.js 8.0 API主题: ${topics.join(', ')}`);

      // 使用现有MCP查询方法获取最新Babylon.js 8.0文档
      const docs = await mcpDocClient.queryBabylonJSDocumentation(topics);
      
      if (docs && docs.length > 0) {
        console.log(`[CodeGenerationAgent] 获取到Babylon.js 8.0文档，长度: ${docs.length}`);
        return `## Babylon.js 8.0 最新API文档\n\n${docs}`;
      } else {
        console.log(`[CodeGenerationAgent] Context7查询无结果，使用Babylon.js 8.0默认实践`);
        return this.getBabylonJS8BestPractices(topics);
      }
    } catch (error) {
      console.error(`[CodeGenerationAgent] Context7查询失败:`, error);
      return this.getBabylonJS8BestPractices(['engine', 'scene', 'camera', 'materials', 'nodes']);
    }
  }

  // 分析需要查询的API主题
  private analyzeRequiredTopics(gameDesign: GameDesign, initialResponse: string): string[] {
    const allTopics = ['engine', 'scene', 'camera', 'lighting', 'materials', 'textures', 'physics', 'collision', 'animation', 'particles', 'input', 'mobile', 'audio', 'UI'];
    const requiredTopics: string[] = [];
    
    // 基于游戏设计分析
    const gameType = gameDesign.gameType?.toLowerCase() || '';
    if (gameType.includes('action') || gameType.includes('射击')) {
      requiredTopics.push('physics', 'collision', 'particles', 'input');
    }
    if (gameType.includes('puzzle') || gameType.includes('益智')) {
      requiredTopics.push('picking', 'animation', 'materials');
    }
    if (gameType.includes('3d') || gameType.includes('三维')) {
      requiredTopics.push('camera', 'lighting', 'materials');
    }
    
    // 基于AI初始响应分析
    const responseText = initialResponse.toLowerCase();
    allTopics.forEach(topic => {
      if (responseText.includes(topic.toLowerCase()) || 
          responseText.includes(topic) ||
          this.isTopicMentioned(responseText, topic)) {
        if (!requiredTopics.includes(topic)) {
          requiredTopics.push(topic);
        }
      }
    });
    
    // 确保至少有基础主题
    const basicTopics = ['engine', 'scene', 'camera', 'materials'];
    basicTopics.forEach(topic => {
      if (!requiredTopics.includes(topic)) {
        requiredTopics.push(topic);
      }
    });
    
    return requiredTopics.slice(0, 8); // 限制查询主题数量
  }

  // 检查主题是否在响应中被提及
  private isTopicMentioned(text: string, topic: string): boolean {
    const topicAliases: { [key: string]: string[] } = {
      'physics': ['物理', '重力', '碰撞检测', 'gravity', 'collision'],
      'camera': ['相机', '视角', '摄像机', 'view', 'perspective'],
      'lighting': ['光照', '灯光', '阴影', 'light', 'shadow'],
      'materials': ['材质', '纹理', '着色器', 'shader', 'texture'],
      'animation': ['动画', '移动', '旋转', 'tween', 'rotate'],
      'particles': ['粒子', '特效', '爆炸', 'effect', 'explosion'],
      'input': ['输入', '控制', '鼠标', '键盘', '触摸', 'mouse', 'keyboard', 'touch'],
      'audio': ['音频', '声音', '音效', 'sound', 'music']
    };
    
    const aliases = topicAliases[topic] || [];
    return aliases.some(alias => text.includes(alias.toLowerCase()));
  }

  // 获取Babylon.js 8.0的最佳实践
  private getBabylonJS8BestPractices(topics: string[]): string {
    const practices: Record<string, string> = {
      engine: `
## Babylon.js 8.0 引擎初始化最佳实践

\`\`\`typescript
// 检测WebGPU支持并优先使用
const webGPUSupported = await BABYLON.WebGPUEngine.IsSupportedAsync;
const engine = webGPUSupported 
  ? new BABYLON.WebGPUEngine(canvas, { 
      adaptToDeviceRatio: true,
      powerPreference: 'high-performance'
    })
  : new BABYLON.Engine(canvas, true, { 
      adaptToDeviceRatio: true,
      powerPreference: 'high-performance'
    });

if (webGPUSupported) {
  await engine.initAsync();
}
\`\`\``,
      
      scene: `
## 场景管理和性能优化

\`\`\`typescript
const scene = new BABYLON.Scene(engine);

// 启用性能优化
scene.skipPointerMovePicking = true;
scene.autoClear = false;
scene.autoClearDepthAndStencil = false;

// 使用AssetsContainer管理资源
const assetsContainer = new BABYLON.AssetsContainer(scene);
\`\`\``,

      camera: `
## 相机系统最佳实践

\`\`\`typescript
// ArcRotate相机（推荐用于游戏）
const camera = new BABYLON.ArcRotateCamera(
  "camera", 
  -Math.PI / 2, 
  Math.PI / 2.5, 
  10, 
  BABYLON.Vector3.Zero(), 
  scene
);

camera.attachControl(canvas, true);
camera.setTarget(BABYLON.Vector3.Zero());

// 性能优化
camera.useBouncingBehavior = true;
camera.useAutoRotationBehavior = false;
\`\`\``,

      materials: `
## 材质系统优化

\`\`\`typescript
// 使用PBR材质获得最佳视觉效果
const material = new BABYLON.PBRMaterial("pbr", scene);
material.baseColor = new BABYLON.Color3(0.5, 0.5, 0.8);
material.metallic = 0.1;
material.roughness = 0.5;

// 启用纹理压缩
if (engine.getCaps().textureFloat) {
  material.forceCompilation(scene);
}
\`\`\``,

      physics: `
## Havok物理引擎集成

\`\`\`typescript
// 使用最新的Havok Physics V2
const hk = new BABYLON.HavokPlugin();
scene.enablePhysics(new BABYLON.Vector3(0, -9.81, 0), hk);

// 创建物理聚合体
const sphereAggregate = new BABYLON.PhysicsAggregate(
  sphere, 
  BABYLON.PhysicsShapeType.SPHERE, 
  { mass: 1, restitution: 0.75 }, 
  scene
);
\`\`\``,

      nodes: `
## 节点系统集成

\`\`\`typescript
import { NodeRegistry } from '../utils/NodeRegistry';
import { GameNodeType, MeshNodeProperties } from '../types/NodeTypes';

// 创建并注册节点
const nodeRegistry = NodeRegistry.getInstance();

// 创建mesh后立即注册
const box = BABYLON.MeshBuilder.CreateBox("box", {size: 2}, scene);
nodeRegistry.register({
  id: "game_box",
  name: "游戏方块",
  type: GameNodeType.MESH,
  visible: true,
  enabled: true,
  position: box.position,
  rotation: box.rotation,
  scaling: box.scaling,
  parent: undefined,
  children: [],
  geometry: {
    type: 'box',
    parameters: { size: 2 }
  }
} as MeshNodeProperties);
\`\`\``,

      default: `
## Babylon.js 8.0 通用最佳实践

\`\`\`typescript
// 使用现代ES6+语法
import * as BABYLON from '@babylonjs/core';

// 错误处理
try {
  const scene = new BABYLON.Scene(engine);
  // ... 场景设置
} catch (error) {
  console.error('Babylon.js初始化失败:', error);
}

// 资源清理
useEffect(() => {
  return () => {
    scene?.dispose();
    engine?.dispose();
  };
}, []);
\`\`\``
    };

    const selectedPractices = topics
      .map(topic => practices[topic] || practices.default)
      .join('\n\n');

    return `# Babylon.js 8.0 API最佳实践指南

${selectedPractices}

## 🚨 关键注意事项
- 优先使用WebGPU引擎获得更好性能
- 所有游戏对象必须注册到NodeRegistry
- 使用PBR材质和最新的物理引擎
- 启用各种性能优化选项
- 确保资源正确清理

## 📋 完整导入示例
\`\`\`typescript
import React, { useEffect, useRef } from 'react';
import { 
  Engine, WebGPUEngine, Scene, Vector3, Color3, 
  MeshBuilder, PBRMaterial, ArcRotateCamera, 
  HemisphericLight, HavokPlugin, PhysicsAggregate, 
  PhysicsShapeType 
} from '@babylonjs/core';
import { NodeRegistry } from '../utils/NodeRegistry';
import { GameNodeType, MeshNodeProperties } from '../types/NodeTypes';
\`\`\``;
  }

  // 获取默认的Babylon.js最佳实践（保留向后兼容）
  private getDefaultBabylonJSPractices(topics: string[]): string {
    const practices: { [key: string]: string } = {
      basic: "基础：engine.runRenderLoop(() => scene.render())，标准初始化流程",
      engine: "引擎：new BABYLON.Engine(canvas, antialias, options)",
      scene: "场景：new BABYLON.Scene(engine)，scene.render()",
      camera: "相机：BABYLON.ArcRotateCamera 或 FreeCamera，camera.attachControl()",
      materials: "材质：StandardMaterial, PBRMaterial，正确设置diffuseTexture",
      lighting: "光照：HemisphericLight, DirectionalLight，适当的强度设置",
      physics: "物理：scene.enablePhysics(gravity, plugin)，HavokPlugin或CannonJSPlugin",
      particles: "粒子：BABYLON.ParticleSystem，正确配置发射器和纹理",
      animation: "动画：BABYLON.Animation.CreateAndStartAnimation",
      input: "输入：scene.onPointerObservable，键盘事件处理",
      mobile: "移动端：触摸控制，性能优化设置"
    };

    return `Babylon.js 8.17.2最佳实践参考：\n${topics.map(topic => practices[topic] || '').filter(Boolean).join('\n')}`;
  }

  // 构建代码生成提示
  private buildCodePrompt(gameDesign: GameDesign, babylonApiDocs?: string): string {
    return `根据以下游戏设计，生成完整可运行的React TSX游戏组件：

## 🎮 游戏设计
${JSON.stringify(gameDesign, null, 2)}

${babylonApiDocs ? `## 🔧 Babylon.js API参考
${babylonApiDocs}

` : ''}## 🎯 实现要求
- 使用标准预设组件（CameraPresets、LightingPresets、MaterialPresets、ParticlePresets）
- 实现完整游戏逻辑和UI交互
- 支持移动端触控和桌面端鼠标操作
- 包含完整TypeScript接口定义
- 确保所有资源路径正确

直接输出TSX代码，无需任何标记。`;
  }

  // 解析生成的代码
  private parseGeneratedCode(content: string, gameDesign: GameDesign): GeneratedCode {
    // 清理Agent输出的内容，去除markdown代码块标记
    let componentCode = content.trim();

    // 去除开头的markdown代码块标记
    if (componentCode.startsWith('```typescript') || componentCode.startsWith('```tsx') || componentCode.startsWith('```')) {
      const lines = componentCode.split('\n');
      lines.shift(); // 移除第一行的```标记
      componentCode = lines.join('\n');
    }

    // 去除结尾的markdown代码块标记
    if (componentCode.endsWith('```')) {
      const lines = componentCode.split('\n');
      lines.pop(); // 移除最后一行的```标记
      componentCode = lines.join('\n').trim();
    }

    // 验证Agent是否生成了有效的代码
    if (!componentCode || componentCode.length < 500 || !componentCode.includes('export default')) {
      throw new Error('Agent生成的代码无效或不完整，请重新生成');
    }

    // 生成组件名称和文件名
    const componentName = this.extractComponentNameFromCode(componentCode) ||
                         this.generateComponentNameFromGameDesign(gameDesign);
    const fileName = `${componentName}.tsx`;

    console.log(`[CodeGenerationAgent] 生成组件名: ${componentName}, 文件名: ${fileName}`);

    console.log(`[CodeGenerationAgent] 生成组件: ${fileName} (${componentCode.length} 字符)`);

    return {
      component: componentCode,
      fileName,
      dependencies: this.getDefaultDependencies(),
      assets: {
        textures: [],
        models: [],
        sounds: [],
        other: []
      },
      metadata: {
        componentName,
        gameType: gameDesign.gameType,
        description: gameDesign.theme || '游戏组件'
      }
    };
  }

  // 从代码中提取组件名称
  private extractComponentNameFromCode(code: string): string | null {
    // 尝试匹配 export default function ComponentName
    const functionMatch = code.match(/export\s+default\s+function\s+(\w+)/);
    if (functionMatch) {
      return functionMatch[1];
    }

    // 尝试匹配 const ComponentName = () =>
    const constMatch = code.match(/const\s+(\w+)\s*=\s*\(/);
    if (constMatch) {
      return constMatch[1];
    }

    return null;
  }

  // 根据游戏设计生成组件名称
  private generateComponentNameFromGameDesign(gameDesign: GameDesign): string {
    const gameType = gameDesign.gameType || 'Game';
    const theme = gameDesign.theme || '';

    // 生成驼峰命名的组件名
    let componentName = gameType.charAt(0).toUpperCase() + gameType.slice(1);

    if (theme) {
      const cleanTheme = theme.replace(/[^a-zA-Z0-9]/g, '');
      if (cleanTheme) {
        componentName = cleanTheme.charAt(0).toUpperCase() + cleanTheme.slice(1) + 'Game';
      }
    }

    return componentName + 'Component';
  }

  // 从输入中提取游戏设计
  private extractGameDesignFromInput(content: string): GameDesign | null {
    try {
      // 尝试解析JSON格式的游戏设计
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      console.warn('[CodeGenerationAgent] 无法解析游戏设计JSON:', error);
    }
    return null;
  }

  // 获取默认依赖项
  private getDefaultDependencies(): string[] {
    const deps = [
      "react",
      "@types/react",
      "typescript"
    ];

    if (this.generationConfig.framework === "three.js") {
      deps.push("three", "@types/three");
    } else if (this.generationConfig.framework === "babylon.js") {
      deps.push("@babylonjs/core", "@babylonjs/materials", "@babylonjs/loaders");
    }

    return deps;
  }

  // 从用户需求解析生成的代码
  private parseGeneratedCodeFromRequirement(content: string, userRequirement: string): GeneratedCode {
    try {
      // 从用户需求中提取游戏类型
      const req = userRequirement.toLowerCase();
      let gameType: 'action' | 'puzzle' | 'adventure' | 'strategy' | 'casual' = 'action';
      
      if (req.includes('puzzle') || req.includes('益智')) {
        gameType = 'puzzle';
      } else if (req.includes('adventure') || req.includes('冒险')) {
        gameType = 'adventure';
      } else if (req.includes('strategy') || req.includes('策略')) {
        gameType = 'strategy';
      } else if (req.includes('casual') || req.includes('休闲')) {
        gameType = 'casual';
      }

      // 生成文件名
      const timestamp = Date.now();
      const fileName = `GameComponent_${timestamp}.tsx`;
      const componentName = `GameComponent${timestamp}`;

      return {
        component: content,
        fileName: fileName,
        dependencies: [
          '@babylonjs/core',
          'react',
          'cannon'
        ],
        assets: { textures: [], models: [], sounds: [], other: [] }, // 添加assets字段
        metadata: {
          componentName: componentName,
          gameType: gameType,
          description: userRequirement
        }
      };
    } catch (error) {
      console.error('[CodeGenerationAgent] 解析代码失败:', error);
      // 返回基础结构
      const timestamp = Date.now();
      return {
        component: content,
        fileName: `GameComponent_${timestamp}.tsx`,
        dependencies: ['@babylonjs/core', 'react'],
        assets: { textures: [], models: [], sounds: [], other: [] }, // 添加assets字段
        metadata: {
          componentName: `GameComponent${timestamp}`,
          gameType: 'action',
          description: userRequirement
        }
      };
    }
  }

  // 获取生成的代码
  public async getGeneratedCode(threadId: string): Promise<GeneratedCode | null> {
    const state = await this.getState(threadId);
    return state?.values?.context?.generatedCode || null;
  }

  // 更新生成配置
  public updateConfig(config: Partial<CodeGenerationConfig>): void {
    this.generationConfig = { ...this.generationConfig, ...config };
  }

  // ==================== 脚本生成功能 ====================

  /**
   * 生成脚本代码 - 新增功能用于持续迭代开发
   */
  public async generateScript(
    userRequirement: string,
    sceneContext: SceneContext | null = null
  ): Promise<GeneratedScript> {
    console.log(`[CodeGenerationAgent] 开始生成脚本：${userRequirement.substring(0, 100)}...`);

    try {
      // 构建脚本生成提示
      const prompt = this.buildScriptPrompt(userRequirement, sceneContext);
      
      // 调用模型生成脚本
      const response = await this.model.invoke([
        { role: "system", content: this.getScriptSystemPrompt() },
        { role: "user", content: prompt }
      ]);
      
      const content = response.content as string;
      
      // 解析生成的脚本
      const generatedScript = this.parseGeneratedScript(content, userRequirement, sceneContext);

      console.log(`[CodeGenerationAgent] 脚本生成完成：${generatedScript.name}`);
      return generatedScript;

    } catch (error) {
      console.error('[CodeGenerationAgent] 脚本生成失败:', error);
      throw new Error(`脚本生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 流式生成脚本代码 - 支持token级别的实时输出
   * 使用Promise + 事件机制实现真正的流式输出
   */
  public async *generateScriptStream(
    userRequirement: string,
    sceneContext: SceneContext | null = null
  ): AsyncGenerator<{type: 'token' | 'complete', data: string | {filename: string, description: string, category: string}}, void, unknown> {
    console.log(`[CodeGenerationAgent] 开始流式生成脚本：${userRequirement.substring(0, 100)}...`);

    try {
      // 构建脚本生成提示
      const prompt = this.buildScriptPrompt(userRequirement, sceneContext);
      
      let fullContent = '';
      let isComplete = false;
      const tokenQueue: string[] = [];
      
      // 创建一个Promise来处理流式输出
      const streamPromise = new Promise<void>((resolve, reject) => {
        const streamCallback = {
          onToken: (token: string) => {
            console.log('[CodeGenerationAgent] 收到token:', token);
            fullContent += token;
            tokenQueue.push(token);
          },
          onStatus: (status: string) => {
            console.log(`[CodeGenerationAgent] ${status}`);
          }
        };

        // 异步调用invokeStream
        this.model.invokeStream([
          { role: "system", content: this.getScriptSystemPrompt() },
          { role: "user", content: prompt }
        ], streamCallback)
        .then(() => {
          isComplete = true;
          resolve();
        })
        .catch(reject);
      });

      // 实时处理token队列
      const startTime = Date.now();
      while (!isComplete || tokenQueue.length > 0) {
        if (tokenQueue.length > 0) {
          const token = tokenQueue.shift()!;
          yield { type: 'token', data: token };
        } else {
          // 没有新token时短暂等待
          await new Promise(resolve => setTimeout(resolve, 50));
        }
        
        // 超时保护（3分钟，适配长脚本生成）
        if (Date.now() - startTime > 180000) {
          console.warn('[CodeGenerationAgent] 流式生成超时，强制结束');
          break;
        }
      }

      // 等待流式调用完全结束
      await streamPromise;

      console.log(`[CodeGenerationAgent] 流式脚本生成完成，总长度: ${fullContent.length}`);

      // 生成完成信号
      yield {
        type: 'complete',
        data: {
          filename: `script_${Date.now()}.js`,
          description: userRequirement.substring(0, 100),
          category: 'script'
        }
      };

    } catch (error) {
      console.error('[CodeGenerationAgent] 流式脚本生成失败:', error);
      throw new Error(`流式脚本生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取脚本生成专用的系统提示
   */
  private getScriptSystemPrompt(): string {
    return `你是专业的Babylon.js 8.17.2脚本生成器，专门为3D场景生成简洁、可执行的脚本代码。

## 技术栈要求
- **引擎**: Babylon.js 8.17.2
- **访问方式**: 通过BABYLON全局对象访问所有API
- **语言**: 纯JavaScript，禁止import/export语句
- **API规范**: 严格使用Babylon.js 8.17.2的API和语法
- **类型安全**: 代码在浏览器JavaScript环境中运行，不需要TypeScript类型声明

## 可用的核心API（在BABYLON全局对象中）
- **动画系统**: BABYLON.Animation.CreateAndStartAnimation()
- **交互系统**: BABYLON.ActionManager, BABYLON.ExecuteCodeAction  
- **渲染循环**: scene.registerBeforeRender(), scene.unregisterBeforeRender()
- **网格属性**: mesh.position, mesh.rotation, mesh.scaling (Vector3)
- **动画常量**: BABYLON.Animation.ANIMATIONLOOPMODE_CYCLE, ANIMATIONLOOPMODE_CONSTANT

## 运行环境说明
代码将在浏览器的JavaScript环境中执行，BABYLON对象已正确加载并可用。所有Babylon.js API都通过BABYLON全局对象访问，如：BABYLON.Animation、BABYLON.ActionManager等。

## 核心要求
1. 生成完整可执行的代码，禁止TODO和占位符
2. 代码简洁，专注核心功能，避免冗余
3. 优先使用BABYLON.Animation系统实现流畅动画
4. 使用scene.registerBeforeRender()作为备选方案
5. 适当添加交互功能（如键盘控制）

## 最佳实践模板

### 节点访问模板
\`\`\`javascript
// 正确的节点访问方式
const targetNode = nodeRegistry.getNode('节点ID');
if (!targetNode) return;
const mesh = targetNode.babylonNode || targetNode; // 兼容性处理
if (!mesh) return;
\`\`\`

### 键盘事件处理模板（用于交互脚本）
\`\`\`javascript
// 正确的键盘事件设置
const canvas = scene.getEngine().getRenderingCanvas();
if (canvas) {
  canvas.focus(); // 确保Canvas获得焦点
}

const handleKeyDown = (event) => {
  if (event.code === 'Space') {
    event.preventDefault();
    // 执行功能
  }
};

// 双重事件监听确保可靠性
document.addEventListener('keydown', handleKeyDown);
if (canvas) {
  canvas.addEventListener('keydown', handleKeyDown);
}

// 使用registerCleanup注册清理函数
if (typeof registerCleanup === 'function') {
  registerCleanup(() => {
    document.removeEventListener('keydown', handleKeyDown);
    if (canvas) {
      canvas.removeEventListener('keydown', handleKeyDown);
    }
  });
}
\`\`\`

## 输出要求
**重要：直接输出纯JavaScript代码，不要使用JSON格式，不要使用代码块标记！**

必须包含以下结构：
\`\`\`javascript
function executeScript(scene) {
  try {
    const nodeRegistry = NodeRegistry.getInstance();
    const targetNode = nodeRegistry.getNode('节点ID');
    if (!targetNode) return;
    
    // 使用兼容性节点访问
    const mesh = targetNode.babylonNode || targetNode;
    if (!mesh) return;
    
    // 脚本功能实现...
    
    // 如果需要清理资源，使用registerCleanup函数
    if (typeof registerCleanup === 'function') {
      registerCleanup(() => {
        // 在这里添加清理代码
        console.log('脚本清理完成');
      });
    }
    
  } catch (error) {
    console.error('脚本执行失败:', error);
  }
}
\`\`\`

**输出格式要求：**
- 直接输出JavaScript代码，不要JSON包装
- 不要使用markdown代码块标记
- 使用完整的Babylon.js API功能`;
  }

  /**
   * 构建脚本生成提示
   */
  private buildScriptPrompt(userRequirement: string, sceneContext: SceneContext | null): string {
    let contextInfo = '';
    
    if (sceneContext?.selectedNode) {
      contextInfo = `当前选中节点：${sceneContext.selectedNode.name} (${sceneContext.selectedNode.type})`;
    }

    return `${contextInfo}

用户需求：${userRequirement}

请生成一个简洁的JavaScript脚本实现该功能。`;
  }

  /**
   * 解析生成的脚本
   */
  private parseGeneratedScript(
    response: string, 
    userRequirement: string, 
    sceneContext: SceneContext | null
  ): GeneratedScript {
    try {
      // 清理响应内容
      let code = response.trim();
      
      // 移除可能的markdown代码块标记
      if (code.startsWith('```')) {
        const lines = code.split('\n');
        lines.shift(); // 移除开头的```
        if (lines[lines.length - 1].trim() === '```') {
          lines.pop(); // 移除结尾的```
        }
        code = lines.join('\n').trim();
      }
      
      // 验证代码不为空
      if (!code) {
        throw new Error('Agent返回的代码内容为空');
      }
      
      // 从用户需求生成脚本名称
      const generateScriptName = (requirement: string): string => {
        if (requirement.includes('旋转')) return '节点旋转动画';
        if (requirement.includes('移动')) return '节点移动动画';
        if (requirement.includes('缩放')) return '节点缩放动画';
        if (requirement.includes('颜色') || requirement.includes('材质')) return '材质变化效果';
        if (requirement.includes('光')) return '光照效果';
        if (requirement.includes('交互') || requirement.includes('点击')) return '交互功能';
        return '自定义脚本';
      };
      
      return {
        id: `script_${Date.now()}`,
        name: generateScriptName(userRequirement),
        content: code,
        nodeId: sceneContext?.selectedNode?.id,
        createdAt: new Date(),
        lastModified: new Date(),
        isActive: false,
        metadata: {
          description: userRequirement,
          targetNodeTypes: ['mesh'],
          dependencies: ['@babylonjs/core'],
          functionType: 'animation'
        }
      };
      
    } catch (error) {
      console.error('[CodeGenerationAgent] 脚本解析失败:', error);
      throw new Error(`脚本解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

    /**
   * 包装生成的代码（仅在Agent返回纯代码时添加基础模板）
   */
  private wrapGeneratedCode(code: string, userRequirement: string): string {
    // 如果代码已经包含完整结构，直接返回
    if (code.includes('executeScript') || code.includes('export')) {
      return code;
    }
    
    // 只为纯代码片段添加最小包装
    return `/**
 * 脚本描述：${userRequirement}
 * 作者：AI Agent
 * 创建时间：${new Date().toLocaleString()}
 */

import { NodeRegistry } from '../../../src/utils/NodeRegistry';
import { NodeCommunicationService } from '../../../src/utils/NodeCommunicationService';
import { Scene } from '@babylonjs/core';

export function executeScript(scene: Scene): void {
  const nodeRegistry = NodeRegistry.getInstance();
  const commService = new NodeCommunicationService();
  
  try {
    console.log('[Script] 开始执行生成的脚本');
    
    ${code}
    
    console.log('[Script] 脚本执行完成');
  } catch (error) {
    console.error('[Script] 脚本执行失败:', error);
  }
}`;
  }
}
