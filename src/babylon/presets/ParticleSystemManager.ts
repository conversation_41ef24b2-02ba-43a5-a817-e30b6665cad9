/**
 * ParticleSystemManager - 粒子系统管理器
 * 负责创建、配置和管理各种粒子系统，提供统一的粒子系统创建接口
 * 基于Babylon.js 8.17.2最新API实现
 */

import * as BABYLON from '@babylonjs/core';
import { ParticlePresetType, ParticlePresetConfig, PARTICLE_PRESETS } from './ParticlePresets';

/**
 * 粒子系统创建结果接口
 */
export interface ParticleSystemResult {
  particleSystem: BABYLON.ParticleSystem | BABYLON.GPUParticleSystem;
  config: ParticlePresetConfig;
  texture?: BABYLON.Texture;
  dispose: () => void;
}

/**
 * 粒子系统创建选项
 */
export interface ParticleSystemOptions {
  /** 发射器位置或网格 */
  emitter?: BABYLON.AbstractMesh | BABYLON.Vector3;
  /** 是否使用GPU粒子系统 */
  useGPU?: boolean;
  /** 是否自动开始 */
  autoStart?: boolean;
  /** 自定义纹理路径 */
  customTexture?: string;
  /** 缩放比例 */
  scale?: number;
  /** 位置偏移 */
  position?: BABYLON.Vector3;
  /** 颜色覆盖 */
  colorOverride?: {
    color1?: BABYLON.Color4;
    color2?: BABYLON.Color4;
    colorDead?: BABYLON.Color4;
  };
}

/**
 * 粒子系统管理器类
 */
export class ParticleSystemManager {
  private scene: BABYLON.Scene;
  private activeSystems: Map<string, ParticleSystemResult> = new Map();
  private defaultTextures: Map<string, BABYLON.Texture> = new Map();

  constructor(scene: BABYLON.Scene) {
    this.scene = scene;
    this.initializeDefaultTextures();
  }

  /**
   * 初始化默认纹理
   */
  private initializeDefaultTextures(): void {
    try {
      // 创建火焰纹理
      const fireTexture = this.createFireTexture();
      this.defaultTextures.set('fire', fireTexture);

      // 创建烟雾纹理
      const smokeTexture = this.createSmokeTexture();
      this.defaultTextures.set('smoke', smokeTexture);

      // 创建闪光纹理
      const sparkleTexture = this.createSparkleTexture();
      this.defaultTextures.set('sparkle', sparkleTexture);

      // 创建雨滴纹理
      const rainTexture = this.createRainTexture();
      this.defaultTextures.set('rain', rainTexture);

      console.log('[ParticleSystemManager] 默认纹理初始化完成');
    } catch (error) {
      console.error('[ParticleSystemManager] 默认纹理初始化失败:', error);
    }
  }

  /**
   * 创建火焰纹理
   */
  private createFireTexture(): BABYLON.Texture {
    const texture = new BABYLON.DynamicTexture('fireTexture', { width: 64, height: 64 }, this.scene);
    const context = texture.getContext();
    
    // 创建径向渐变
    const gradient = context.createRadialGradient(32, 32, 0, 32, 32, 32);
    gradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
    gradient.addColorStop(0.3, 'rgba(255, 200, 100, 0.8)');
    gradient.addColorStop(0.7, 'rgba(255, 100, 50, 0.4)');
    gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');
    
    context.fillStyle = gradient;
    context.fillRect(0, 0, 64, 64);
    texture.update();
    
    return texture;
  }

  /**
   * 创建烟雾纹理
   */
  private createSmokeTexture(): BABYLON.Texture {
    const texture = new BABYLON.DynamicTexture('smokeTexture', { width: 64, height: 64 }, this.scene);
    const context = texture.getContext();
    
    // 创建径向渐变
    const gradient = context.createRadialGradient(32, 32, 0, 32, 32, 32);
    gradient.addColorStop(0, 'rgba(200, 200, 200, 0.8)');
    gradient.addColorStop(0.5, 'rgba(150, 150, 150, 0.4)');
    gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');
    
    context.fillStyle = gradient;
    context.fillRect(0, 0, 64, 64);
    texture.update();
    
    return texture;
  }

  /**
   * 创建闪光纹理
   */
  private createSparkleTexture(): BABYLON.Texture {
    const texture = new BABYLON.DynamicTexture('sparkleTexture', { width: 32, height: 32 }, this.scene);
    const context = texture.getContext();
    
    // 创建星形闪光
    context.fillStyle = 'rgba(255, 255, 255, 1)';
    context.fillRect(15, 0, 2, 32);
    context.fillRect(0, 15, 32, 2);
    
    // 添加对角线
    context.beginPath();
    context.moveTo(8, 8);
    context.lineTo(24, 24);
    context.moveTo(24, 8);
    context.lineTo(8, 24);
    context.strokeStyle = 'rgba(255, 255, 255, 0.8)';
    context.lineWidth = 2;
    context.stroke();
    
    texture.update();
    return texture;
  }

  /**
   * 创建雨滴纹理
   */
  private createRainTexture(): BABYLON.Texture {
    const texture = new BABYLON.DynamicTexture('rainTexture', { width: 16, height: 32 }, this.scene);
    const context = texture.getContext();
    
    // 创建垂直渐变
    const gradient = context.createLinearGradient(0, 0, 0, 32);
    gradient.addColorStop(0, 'rgba(200, 220, 255, 0)');
    gradient.addColorStop(0.3, 'rgba(200, 220, 255, 0.8)');
    gradient.addColorStop(0.7, 'rgba(150, 180, 255, 0.8)');
    gradient.addColorStop(1, 'rgba(100, 150, 255, 0)');
    
    context.fillStyle = gradient;
    context.fillRect(6, 0, 4, 32);
    texture.update();
    
    return texture;
  }

  /**
   * 根据预设类型创建粒子系统
   */
  public createParticleSystem(
    presetType: ParticlePresetType,
    options: ParticleSystemOptions = {}
  ): ParticleSystemResult {
    const config = PARTICLE_PRESETS[presetType];
    if (!config) {
      throw new Error(`Unknown particle preset type: ${presetType}`);
    }

    try {
      // 创建粒子系统
      const particleSystem = options.useGPU 
        ? new BABYLON.GPUParticleSystem(`gpu_${presetType}`, { capacity: config.capacity }, this.scene)
        : new BABYLON.ParticleSystem(presetType, config.capacity, this.scene);

      // 应用基础配置
      this.applyBaseConfiguration(particleSystem, config, options);

      // 应用高级配置
      this.applyAdvancedConfiguration(particleSystem, config);

      // 设置发射器
      if (options.emitter) {
        particleSystem.emitter = options.emitter;
      } else if (options.position) {
        particleSystem.emitter = options.position;
      } else {
        particleSystem.emitter = BABYLON.Vector3.Zero();
      }

      // 获取或创建纹理
      const texture = this.getTextureForPreset(config, options.customTexture);

      // 设置纹理
      if (texture) {
        particleSystem.particleTexture = texture;
      }

      // 自动启动
      if (options.autoStart !== false) {
        particleSystem.start();
      }

      // 创建结果对象
      const result: ParticleSystemResult = {
        particleSystem,
        config,
        texture,
        dispose: () => {
          // 根据Babylon.js文档，需要先停止和重置粒子系统
          particleSystem.stop();
          particleSystem.reset(); // 清除所有当前活跃的粒子
          particleSystem.dispose();
          this.activeSystems.delete(presetType);
        }
      };

      // 记录活跃系统
      this.activeSystems.set(presetType, result);

      console.log(`[ParticleSystemManager] 创建粒子系统: ${presetType}`);
      return result;
    } catch (error) {
      console.error(`[ParticleSystemManager] 创建粒子系统失败: ${presetType}`, error);
      throw error;
    }
  }

  /**
   * 应用基础配置
   */
  private applyBaseConfiguration(
    particleSystem: BABYLON.ParticleSystem | BABYLON.GPUParticleSystem,
    config: ParticlePresetConfig,
    options: ParticleSystemOptions
  ): void {
    // 基础属性
    particleSystem.emitRate = config.emitRate;
    particleSystem.minSize = config.minSize * (options.scale || 1);
    particleSystem.maxSize = config.maxSize * (options.scale || 1);
    particleSystem.minLifeTime = config.minLifeTime;
    particleSystem.maxLifeTime = config.maxLifeTime;
    particleSystem.minEmitPower = config.minEmitPower;
    particleSystem.maxEmitPower = config.maxEmitPower;

    // 方向
    particleSystem.direction1 = config.direction1;
    particleSystem.direction2 = config.direction2;

    // 物理属性
    particleSystem.gravity = config.gravity;
    particleSystem.updateSpeed = config.updateSpeed;

    // 颜色
    const colors = options.colorOverride || {};
    particleSystem.color1 = colors.color1 || config.color1;
    particleSystem.color2 = colors.color2 || config.color2;
    particleSystem.colorDead = colors.colorDead || config.colorDead;

    // 发射区域
    particleSystem.minEmitBox = config.minEmitBox;
    particleSystem.maxEmitBox = config.maxEmitBox;
  }

  /**
   * 应用高级配置
   */
  private applyAdvancedConfiguration(
    particleSystem: BABYLON.ParticleSystem | BABYLON.GPUParticleSystem,
    config: ParticlePresetConfig
  ): void {
    // 混合模式
    if (config.blendMode !== undefined) {
      particleSystem.blendMode = config.blendMode;
    }

    // 停止持续时间 - 只有当明确设置了数值时才应用
    if (config.targetStopDuration !== undefined && typeof config.targetStopDuration === 'number') {
      particleSystem.targetStopDuration = config.targetStopDuration;
    }

    // 停止时销毁
    if (config.disposeOnStop !== undefined) {
      particleSystem.disposeOnStop = config.disposeOnStop;
    }
  }

  /**
   * 获取预设对应的纹理
   */
  private getTextureForPreset(config: ParticlePresetConfig, customTexture?: string): BABYLON.Texture | null {
    if (customTexture) {
      try {
        return new BABYLON.Texture(customTexture, this.scene);
      } catch (error) {
        console.warn('[ParticleSystemManager] 自定义纹理加载失败，使用默认纹理:', error);
      }
    }

    // 根据类别选择默认纹理
    switch (config.category) {
      case 'fire':
      case 'explosion':
        return this.defaultTextures.get('fire') || null;
      case 'smoke':
        return this.defaultTextures.get('smoke') || null;
      case 'magic':
      case 'energy':
        return this.defaultTextures.get('sparkle') || null;
      case 'environment':
        if (config.name.includes('雨')) {
          return this.defaultTextures.get('rain') || null;
        }
        return this.defaultTextures.get('smoke') || null;
      default:
        return this.defaultTextures.get('sparkle') || null;
    }
  }

  /**
   * 获取所有活跃的粒子系统
   */
  public getActiveSystems(): Map<string, ParticleSystemResult> {
    return new Map(this.activeSystems);
  }

  /**
   * 获取活跃粒子系统数量
   */
  public getActiveSystemsCount(): number {
    return this.activeSystems.size;
  }

  /**
   * 停止指定的粒子系统
   */
  public stopSystem(presetType: ParticlePresetType): void {
    const result = this.activeSystems.get(presetType);
    if (result) {
      result.particleSystem.stop();
    }
  }

  /**
   * 停止所有活跃的粒子系统
   */
  public stopAllSystems(): void {
    this.activeSystems.forEach(result => {
      result.particleSystem.stop();
    });
  }

  /**
   * 销毁所有活跃的粒子系统
   */
  public disposeAllSystems(): void {
    this.activeSystems.forEach(result => {
      result.dispose();
    });
    this.activeSystems.clear();
  }

  /**
   * 销毁指定的粒子系统
   */
  public disposeSystem(presetType: ParticlePresetType): void {
    const result = this.activeSystems.get(presetType);
    if (result) {
      result.dispose();
    }
  }

  /**
   * 销毁所有粒子系统和资源
   */
  public dispose(): void {
    // 销毁所有活跃系统
    this.activeSystems.forEach(result => {
      result.dispose();
    });
    this.activeSystems.clear();

    // 销毁默认纹理
    this.defaultTextures.forEach(texture => {
      texture.dispose();
    });
    this.defaultTextures.clear();

    console.log('[ParticleSystemManager] 资源清理完成');
  }
}
