/**
 * Babylon.js预设组件库统一导出
 * 提供所有预设组件的类型和实现的统一入口
 */

// 相机预设
export { 
  CameraPresets, 
  CameraPresetType,
  type CameraPresetConfig 
} from './CameraPresets';

// 光照预设
export { 
  LightingPresets, 
  LightingPresetType,
  type LightingPresetConfig,
  type LightingPresetResult 
} from './LightingPresets';

// 材质预设
export { 
  MaterialPresets, 
  MaterialPresetType,
  type MaterialPresetConfig,
  type TextureConfig 
} from './MaterialPresets';

// 粒子系统预设
export { 
  ParticlePresets, 
  ParticlePresetType,
  type ParticlePresetConfig 
} from './ParticlePresets';

// 粒子系统管理器
export { ParticleSystemManager } from './ParticleSystemManager';

// 纹理管理器
export { TextureManager } from './TextureManager';
