/**
 * CameraPresets - 相机预设组件库
 * 提供标准化的相机预设配置，支持移动端触控和PC端键鼠控制
 * 基于Babylon.js 8.17.2最新API实现
 */

import {
  Scene,
  ArcRotateCamera,
  UniversalCamera,
  FreeCamera,
  FollowCamera,
  DeviceOrientationCamera,
  Vector3,
  Camera
} from "@babylonjs/core";

/**
 * 相机预设类型枚举
 */
export enum CameraPresetType {
  /** 第三人称相机 - 围绕目标旋转 */
  THIRD_PERSON = 'third_person',
  /** 第一人称相机 - FPS风格 */
  FIRST_PERSON = 'first_person',
  /** 俯视角相机 - 策略游戏风格 */
  TOP_DOWN = 'top_down',
  /** 跟随相机 - 跟随目标移动 */
  FOLLOW = 'follow',
  /** 自由相机 - 完全自由控制 */
  FREE = 'free',
  /** 设备方向相机 - 移动端陀螺仪 */
  DEVICE_ORIENTATION = 'device_orientation',
  /** 过肩视角相机 - 动作游戏风格 */
  OVER_SHOULDER = 'over_shoulder',
  /** 轨道相机 - 电影级别控制 */
  ORBITAL = 'orbital'
}

/**
 * 相机预设配置接口
 */
export interface CameraPresetConfig {
  /** 相机名称 */
  name: string;
  /** 相机类型 */
  type: CameraPresetType;
  /** 初始位置 */
  position?: Vector3;
  /** 目标位置 */
  target?: Vector3;
  /** 旋转角度（ArcRotateCamera专用） */
  alpha?: number;
  beta?: number;
  radius?: number;
  /** 视野角度 */
  fov?: number;
  /** 近裁剪面 */
  minZ?: number;
  /** 远裁剪面 */
  maxZ?: number;
  /** 移动速度 */
  speed?: number;
  /** 惯性 */
  inertia?: number;
  /** 是否启用碰撞检测 */
  checkCollisions?: boolean;
  /** 是否应用重力 */
  applyGravity?: boolean;
  /** 是否启用触控控制 */
  enableTouch?: boolean;
  /** 是否启用键盘控制 */
  enableKeyboard?: boolean;
  /** 是否启用鼠标控制 */
  enableMouse?: boolean;
  /** 自定义控制配置 */
  controlConfig?: {
    /** 平移敏感度 */
    panningSensibility?: number;
    /** 缩放敏感度 */
    wheelPrecision?: number;
    /** 旋转敏感度 (兼容旧版本) */
    angularSensibility?: number;
    /** X轴旋转敏感度 (Babylon.js 8.x) */
    angularSensibilityX?: number;
    /** Y轴旋转敏感度 (Babylon.js 8.x) */
    angularSensibilityY?: number;
    /** 是否使用Ctrl键平移 */
    useCtrlForPanning?: boolean;
  };
}

/**
 * 相机预设组件库
 */
export class CameraPresets {
  
  /**
   * 创建第三人称相机预设
   * 适用于动作游戏、RPG等需要围绕角色旋转的场景
   */
  public static createThirdPersonCamera(
    scene: Scene,
    config: Partial<CameraPresetConfig> = {}
  ): ArcRotateCamera {
    const defaultConfig: CameraPresetConfig = {
      name: "ThirdPersonCamera",
      type: CameraPresetType.THIRD_PERSON,
      alpha: -Math.PI / 2,
      beta: Math.PI / 2.5,
      radius: 10,
      target: Vector3.Zero(),
      fov: 0.8,
      minZ: 0.1,
      maxZ: 1000,
      enableTouch: true,
      enableMouse: true,
      enableKeyboard: false,
      controlConfig: {
        panningSensibility: 1000,
        wheelPrecision: 3,
        angularSensibility: 1000,
        useCtrlForPanning: false
      }
    };

    const finalConfig = { ...defaultConfig, ...config };
    
    const camera = new ArcRotateCamera(
      finalConfig.name,
      finalConfig.alpha!,
      finalConfig.beta!,
      finalConfig.radius!,
      finalConfig.target!,
      scene
    );

    // 设置基础属性
    camera.fov = finalConfig.fov!;
    camera.minZ = finalConfig.minZ!;
    camera.maxZ = finalConfig.maxZ!;

    // 配置控制
    if (finalConfig.controlConfig) {
      camera.panningSensibility = finalConfig.controlConfig.panningSensibility || 1000;
      camera.wheelPrecision = finalConfig.controlConfig.wheelPrecision || 3;
      // Babylon.js 8.x 中分离了X和Y轴灵敏度
      camera.angularSensibilityX = finalConfig.controlConfig.angularSensibilityX || finalConfig.controlConfig.angularSensibility || 1000;
      camera.angularSensibilityY = finalConfig.controlConfig.angularSensibilityY || finalConfig.controlConfig.angularSensibility || 1000;
    }

    // 启用自动旋转行为（空闲时）
    camera.useAutoRotationBehavior = true;
    if (camera.autoRotationBehavior) {
      camera.autoRotationBehavior.idleRotationSpeed = 0.1;
      camera.autoRotationBehavior.idleRotationWaitTime = 3000;
    }

    // 启用弹跳行为
    camera.useBouncingBehavior = true;

    return camera;
  }

  /**
   * 创建第一人称相机预设
   * 适用于FPS游戏、第一人称探索等场景
   */
  public static createFirstPersonCamera(
    scene: Scene,
    config: Partial<CameraPresetConfig> = {}
  ): UniversalCamera {
    const defaultConfig: CameraPresetConfig = {
      name: "FirstPersonCamera",
      type: CameraPresetType.FIRST_PERSON,
      position: new Vector3(0, 1.8, -5),
      target: Vector3.Zero(),
      fov: 0.8,
      minZ: 0.1,
      maxZ: 1000,
      speed: 1,
      inertia: 0.9,
      checkCollisions: true,
      applyGravity: true,
      enableTouch: true,
      enableMouse: true,
      enableKeyboard: true
    };

    const finalConfig = { ...defaultConfig, ...config };
    
    const camera = new UniversalCamera(
      finalConfig.name,
      finalConfig.position!,
      scene
    );

    // 设置基础属性
    camera.setTarget(finalConfig.target!);
    camera.fov = finalConfig.fov!;
    camera.minZ = finalConfig.minZ!;
    camera.maxZ = finalConfig.maxZ!;
    camera.speed = finalConfig.speed!;
    camera.inertia = finalConfig.inertia!;

    // 碰撞和重力
    camera.checkCollisions = finalConfig.checkCollisions!;
    camera.applyGravity = finalConfig.applyGravity!;
    
    // 设置碰撞椭球体（人体大小）
    camera.ellipsoid = new Vector3(0.5, 0.9, 0.5);
    camera.ellipsoidOffset = new Vector3(0, 0.9, 0);

    return camera;
  }

  /**
   * 创建俯视角相机预设
   * 适用于策略游戏、塔防游戏等需要俯视视角的场景
   */
  public static createTopDownCamera(
    scene: Scene,
    config: Partial<CameraPresetConfig> = {}
  ): ArcRotateCamera {
    const defaultConfig: CameraPresetConfig = {
      name: "TopDownCamera",
      type: CameraPresetType.TOP_DOWN,
      alpha: 0,
      beta: 0.1, // 接近垂直俯视
      radius: 20,
      target: Vector3.Zero(),
      fov: 0.6,
      minZ: 0.1,
      maxZ: 1000,
      enableTouch: true,
      enableMouse: true,
      enableKeyboard: false,
      controlConfig: {
        panningSensibility: 50,
        wheelPrecision: 10,
        angularSensibility: 2000,
        useCtrlForPanning: false
      }
    };

    const finalConfig = { ...defaultConfig, ...config };
    
    const camera = new ArcRotateCamera(
      finalConfig.name,
      finalConfig.alpha!,
      finalConfig.beta!,
      finalConfig.radius!,
      finalConfig.target!,
      scene
    );

    // 设置基础属性
    camera.fov = finalConfig.fov!;
    camera.minZ = finalConfig.minZ!;
    camera.maxZ = finalConfig.maxZ!;

    // 配置控制
    if (finalConfig.controlConfig) {
      camera.panningSensibility = finalConfig.controlConfig.panningSensibility || 50;
      camera.wheelPrecision = finalConfig.controlConfig.wheelPrecision || 10;
      // Babylon.js 8.x 中分离了X和Y轴灵敏度
      camera.angularSensibilityX = finalConfig.controlConfig.angularSensibilityX || finalConfig.controlConfig.angularSensibility || 2000;
      camera.angularSensibilityY = finalConfig.controlConfig.angularSensibilityY || finalConfig.controlConfig.angularSensibility || 2000;
    }

    // 限制垂直旋转角度（保持俯视角）
    camera.lowerBetaLimit = 0.05;
    camera.upperBetaLimit = Math.PI / 3;

    // 限制缩放范围
    camera.lowerRadiusLimit = 5;
    camera.upperRadiusLimit = 50;

    return camera;
  }

  /**
   * 创建跟随相机预设
   * 适用于需要跟随特定目标的场景
   */
  public static createFollowCamera(
    scene: Scene,
    config: Partial<CameraPresetConfig> = {}
  ): FollowCamera {
    const defaultConfig: CameraPresetConfig = {
      name: "FollowCamera",
      type: CameraPresetType.FOLLOW,
      position: new Vector3(0, 10, -10),
      fov: 0.8,
      minZ: 0.1,
      maxZ: 1000,
      speed: 1,
      enableTouch: true,
      enableMouse: true,
      enableKeyboard: false
    };

    const finalConfig = { ...defaultConfig, ...config };
    
    const camera = new FollowCamera(
      finalConfig.name,
      finalConfig.position!,
      scene
    );

    // 设置基础属性
    camera.fov = finalConfig.fov!;
    camera.minZ = finalConfig.minZ!;
    camera.maxZ = finalConfig.maxZ!;

    // 跟随相机特有属性
    camera.radius = 15; // 与目标的距离
    camera.heightOffset = 5; // 高度偏移
    camera.rotationOffset = 0; // 旋转偏移
    camera.cameraAcceleration = 0.02; // 相机加速度
    camera.maxCameraSpeed = 20; // 最大相机速度

    return camera;
  }

  /**
   * 创建自由相机预设
   * 适用于需要完全自由控制的场景，如编辑器模式
   */
  public static createFreeCamera(
    scene: Scene,
    config: Partial<CameraPresetConfig> = {}
  ): FreeCamera {
    const defaultConfig: CameraPresetConfig = {
      name: "FreeCamera",
      type: CameraPresetType.FREE,
      position: new Vector3(0, 5, -10),
      target: Vector3.Zero(),
      fov: 0.8,
      minZ: 0.1,
      maxZ: 1000,
      speed: 2,
      inertia: 0.8,
      checkCollisions: false,
      applyGravity: false,
      enableTouch: true,
      enableMouse: true,
      enableKeyboard: true
    };

    const finalConfig = { ...defaultConfig, ...config };
    
    const camera = new FreeCamera(
      finalConfig.name,
      finalConfig.position!,
      scene
    );

    // 设置基础属性
    camera.setTarget(finalConfig.target!);
    camera.fov = finalConfig.fov!;
    camera.minZ = finalConfig.minZ!;
    camera.maxZ = finalConfig.maxZ!;
    camera.speed = finalConfig.speed!;
    camera.inertia = finalConfig.inertia!;

    // 碰撞和重力
    camera.checkCollisions = finalConfig.checkCollisions!;
    camera.applyGravity = finalConfig.applyGravity!;

    return camera;
  }

  /**
   * 创建设备方向相机预设
   * 适用于移动端VR或AR应用，使用设备陀螺仪控制
   */
  public static createDeviceOrientationCamera(
    scene: Scene,
    config: Partial<CameraPresetConfig> = {}
  ): DeviceOrientationCamera {
    const defaultConfig: CameraPresetConfig = {
      name: "DeviceOrientationCamera",
      type: CameraPresetType.DEVICE_ORIENTATION,
      position: new Vector3(0, 1.8, -5),
      target: new Vector3(0, 0, -10),
      fov: 0.8,
      minZ: 0.1,
      maxZ: 1000,
      speed: 1,
      enableTouch: true,
      enableMouse: false,
      enableKeyboard: false
    };

    const finalConfig = { ...defaultConfig, ...config };

    const camera = new DeviceOrientationCamera(
      finalConfig.name,
      finalConfig.position!,
      scene
    );

    // 设置基础属性
    camera.setTarget(finalConfig.target!);
    camera.fov = finalConfig.fov!;
    camera.minZ = finalConfig.minZ!;
    camera.maxZ = finalConfig.maxZ!;

    // 设备方向敏感度
    camera.angularSensibility = 10;
    // DeviceOrientationCamera没有moveSensibility属性，使用speed代替
    camera.speed = finalConfig.speed!;

    return camera;
  }

  /**
   * 创建过肩视角相机预设
   * 适用于第三人称动作游戏，提供过肩视角
   */
  public static createOverShoulderCamera(
    scene: Scene,
    config: Partial<CameraPresetConfig> = {}
  ): ArcRotateCamera {
    const defaultConfig: CameraPresetConfig = {
      name: "OverShoulderCamera",
      type: CameraPresetType.OVER_SHOULDER,
      alpha: Math.PI / 2,
      beta: Math.PI / 2.5,
      radius: 8,
      target: new Vector3(0, 1.5, 0),
      fov: 0.9,
      minZ: 0.1,
      maxZ: 1000,
      enableTouch: true,
      enableMouse: true,
      enableKeyboard: false,
      controlConfig: {
        panningSensibility: 1000,
        wheelPrecision: 5,
        angularSensibility: 800,
        useCtrlForPanning: false
      }
    };

    const finalConfig = { ...defaultConfig, ...config };

    const camera = new ArcRotateCamera(
      finalConfig.name,
      finalConfig.alpha!,
      finalConfig.beta!,
      finalConfig.radius!,
      finalConfig.target!,
      scene
    );

    // 设置基础属性
    camera.fov = finalConfig.fov!;
    camera.minZ = finalConfig.minZ!;
    camera.maxZ = finalConfig.maxZ!;

    // 配置控制
    if (finalConfig.controlConfig) {
      camera.panningSensibility = finalConfig.controlConfig.panningSensibility || 1000;
      camera.wheelPrecision = finalConfig.controlConfig.wheelPrecision || 5;
      // ArcRotateCamera使用angularSensibilityX和angularSensibilityY
      const angularSensibility = finalConfig.controlConfig.angularSensibility || 800;
      camera.angularSensibilityX = angularSensibility;
      camera.angularSensibilityY = angularSensibility;
    }

    // 限制旋转角度（过肩视角特有）
    camera.lowerBetaLimit = Math.PI / 6;
    camera.upperBetaLimit = Math.PI / 1.5;
    camera.lowerRadiusLimit = 3;
    camera.upperRadiusLimit = 15;

    // 启用框架行为
    camera.useFramingBehavior = true;

    return camera;
  }

  /**
   * 创建轨道相机预设
   * 适用于展示场景、产品展示等需要平滑轨道运动的场景
   */
  public static createOrbitalCamera(
    scene: Scene,
    config: Partial<CameraPresetConfig> = {}
  ): ArcRotateCamera {
    const defaultConfig: CameraPresetConfig = {
      name: "OrbitalCamera",
      type: CameraPresetType.ORBITAL,
      alpha: 0,
      beta: Math.PI / 3,
      radius: 15,
      target: Vector3.Zero(),
      fov: 0.7,
      minZ: 0.1,
      maxZ: 1000,
      enableTouch: true,
      enableMouse: true,
      enableKeyboard: false,
      controlConfig: {
        panningSensibility: 2000,
        wheelPrecision: 1,
        angularSensibility: 500,
        useCtrlForPanning: true
      }
    };

    const finalConfig = { ...defaultConfig, ...config };

    const camera = new ArcRotateCamera(
      finalConfig.name,
      finalConfig.alpha!,
      finalConfig.beta!,
      finalConfig.radius!,
      finalConfig.target!,
      scene
    );

    // 设置基础属性
    camera.fov = finalConfig.fov!;
    camera.minZ = finalConfig.minZ!;
    camera.maxZ = finalConfig.maxZ!;

    // 配置控制
    if (finalConfig.controlConfig) {
      camera.panningSensibility = finalConfig.controlConfig.panningSensibility || 2000;
      camera.wheelPrecision = finalConfig.controlConfig.wheelPrecision || 1;
      // ArcRotateCamera使用angularSensibilityX和angularSensibilityY
      const angularSensibility = finalConfig.controlConfig.angularSensibility || 500;
      camera.angularSensibilityX = angularSensibility;
      camera.angularSensibilityY = angularSensibility;
    }

    // 启用自动旋转（轨道运动）
    camera.useAutoRotationBehavior = true;
    if (camera.autoRotationBehavior) {
      camera.autoRotationBehavior.idleRotationSpeed = 0.2;
      camera.autoRotationBehavior.idleRotationWaitTime = 1000;
      camera.autoRotationBehavior.idleRotationSpinupTime = 2000;
      camera.autoRotationBehavior.zoomStopsAnimation = false;
    }

    // 启用弹跳行为
    camera.useBouncingBehavior = true;
    if (camera.bouncingBehavior) {
      camera.bouncingBehavior.transitionDuration = 450;
      camera.bouncingBehavior.lowerRadiusTransitionRange = 2;
      camera.bouncingBehavior.upperRadiusTransitionRange = -2;
    }

    return camera;
  }

  /**
   * 根据预设类型创建相机
   * 统一的相机创建入口
   */
  public static createCamera(
    type: CameraPresetType,
    scene: Scene,
    config: Partial<CameraPresetConfig> = {}
  ): ArcRotateCamera | UniversalCamera | FreeCamera | FollowCamera | DeviceOrientationCamera {
    switch (type) {
      case CameraPresetType.THIRD_PERSON:
        return this.createThirdPersonCamera(scene, config);
      case CameraPresetType.FIRST_PERSON:
        return this.createFirstPersonCamera(scene, config);
      case CameraPresetType.TOP_DOWN:
        return this.createTopDownCamera(scene, config);
      case CameraPresetType.FOLLOW:
        return this.createFollowCamera(scene, config);
      case CameraPresetType.FREE:
        return this.createFreeCamera(scene, config);
      case CameraPresetType.DEVICE_ORIENTATION:
        return this.createDeviceOrientationCamera(scene, config);
      case CameraPresetType.OVER_SHOULDER:
        return this.createOverShoulderCamera(scene, config);
      case CameraPresetType.ORBITAL:
        return this.createOrbitalCamera(scene, config);
      default:
        throw new Error(`不支持的相机预设类型: ${type}`);
    }
  }

  /**
   * 配置相机控制
   * 统一配置相机的输入控制
   */
  public static configureControls(
    camera: Camera,
    canvas: HTMLCanvasElement,
    config: Partial<CameraPresetConfig> = {}
  ): void {
    const { controlConfig = {} } = config;

    // 直接附加控制，Babylon.js会自动处理输入类型
    camera.attachControl(canvas, true);

    // 对于ArcRotateCamera，可以配置特定的控制选项
    if (camera instanceof ArcRotateCamera && controlConfig.useCtrlForPanning !== undefined) {
      // 重新附加控制以应用useCtrlForPanning设置
      camera.detachControl();
      camera.attachControl(canvas, true, controlConfig.useCtrlForPanning);
    }
  }

  /**
   * 获取所有可用的相机预设类型
   */
  public static getAvailablePresets(): CameraPresetType[] {
    return Object.values(CameraPresetType);
  }
}
