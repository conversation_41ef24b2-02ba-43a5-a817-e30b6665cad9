/**
 * Babylon.js预设模块验证测试
 * 用于验证所有预设模块是否能正常工作，与最新的Babylon.js API兼容
 */

import * as BABYLON from '@babylonjs/core';
import { CameraPresets, CameraPresetType } from './CameraPresets';
import { LightingPresets, LightingPresetType } from './LightingPresets';
import { MaterialPresets, MaterialPresetType } from './MaterialPresets';
import { ParticlePresets, ParticlePresetType, PARTICLE_PRESETS } from './ParticlePresets';

/**
 * 预设模块验证结果
 */
export interface ValidationResult {
  success: boolean;
  errors: string[];
  warnings: string[];
  testedComponents: string[];
}

/**
 * 预设模块验证器
 */
export class PresetValidator {
  private scene: BABYLON.Scene;
  private engine: BABYLON.Engine;
  private canvas: HTMLCanvasElement;

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.engine = new BABYLON.Engine(canvas, true);
    this.scene = new BABYLON.Scene(this.engine);
  }

  /**
   * 运行完整的预设模块验证
   */
  public async validateAllPresets(): Promise<ValidationResult> {
    const result: ValidationResult = {
      success: true,
      errors: [],
      warnings: [],
      testedComponents: []
    };

    try {
      // 验证相机预设
      await this.validateCameraPresets(result);
      
      // 验证光照预设
      await this.validateLightingPresets(result);
      
      // 验证材质预设
      await this.validateMaterialPresets(result);
      
      // 验证粒子预设
      await this.validateParticlePresets(result);

      console.log('[PresetValidator] 验证完成:', result);
      
    } catch (error) {
      result.success = false;
      result.errors.push(`验证过程中发生错误: ${error instanceof Error ? error.message : '未知错误'}`);
    }

    return result;
  }

  /**
   * 验证相机预设
   */
  private async validateCameraPresets(result: ValidationResult): Promise<void> {
    try {
      console.log('[PresetValidator] 开始验证相机预设...');

      // 测试第三人称相机
      const thirdPersonCamera = CameraPresets.createCamera(CameraPresetType.THIRD_PERSON, this.scene, {
        position: new BABYLON.Vector3(0, 5, -10),
        target: new BABYLON.Vector3(0, 0, 0)
      });
      
      if (!thirdPersonCamera || !(thirdPersonCamera instanceof BABYLON.ArcRotateCamera)) {
        result.errors.push('第三人称相机创建失败或类型不正确');
        result.success = false;
      } else {
        result.testedComponents.push('CameraPresets.THIRD_PERSON');
      }

      // 测试第一人称相机
      const firstPersonCamera = CameraPresets.createCamera(CameraPresetType.FIRST_PERSON, this.scene, {
        position: new BABYLON.Vector3(0, 2, 0)
      });
      
      if (!firstPersonCamera || !(firstPersonCamera instanceof BABYLON.UniversalCamera)) {
        result.errors.push('第一人称相机创建失败或类型不正确');
        result.success = false;
      } else {
        result.testedComponents.push('CameraPresets.FIRST_PERSON');
      }

      console.log('[PresetValidator] 相机预设验证完成');
      
    } catch (error) {
      result.errors.push(`相机预设验证失败: ${error instanceof Error ? error.message : '未知错误'}`);
      result.success = false;
    }
  }

  /**
   * 验证光照预设
   */
  private async validateLightingPresets(result: ValidationResult): Promise<void> {
    try {
      console.log('[PresetValidator] 开始验证光照预设...');

      // 测试室外白天光照
      const outdoorLighting = LightingPresets.createLighting(LightingPresetType.OUTDOOR_DAY, this.scene, {
        intensity: 1.0,
        direction: new BABYLON.Vector3(-1, -1, 1)
      });
      
      if (!outdoorLighting || !outdoorLighting.lights || outdoorLighting.lights.length === 0) {
        result.errors.push('室外白天光照创建失败');
        result.success = false;
      } else {
        result.testedComponents.push('LightingPresets.OUTDOOR_DAY');
      }

      // 测试室内基础光照
      const indoorLighting = LightingPresets.createLighting(LightingPresetType.INDOOR_BASIC, this.scene, {
        intensity: 0.8
      });
      
      if (!indoorLighting || !indoorLighting.lights || indoorLighting.lights.length === 0) {
        result.errors.push('室内基础光照创建失败');
        result.success = false;
      } else {
        result.testedComponents.push('LightingPresets.INDOOR_BASIC');
      }

      console.log('[PresetValidator] 光照预设验证完成');
      
    } catch (error) {
      result.errors.push(`光照预设验证失败: ${error instanceof Error ? error.message : '未知错误'}`);
      result.success = false;
    }
  }

  /**
   * 验证材质预设
   */
  private async validateMaterialPresets(result: ValidationResult): Promise<void> {
    try {
      console.log('[PresetValidator] 开始验证材质预设...');

      // 测试PBR金属材质
      const metalMaterial = MaterialPresets.createMaterial(
        MaterialPresetType.PBR_METAL_SMOOTH, 
        this.scene, 
        "testMetal", 
        {
          baseColor: new BABYLON.Color3(0.8, 0.8, 0.8),
          metallic: 0.9,
          roughness: 0.1
        }
      );
      
      if (!metalMaterial || !metalMaterial.material) {
        result.errors.push('PBR金属材质创建失败');
        result.success = false;
      } else {
        result.testedComponents.push('MaterialPresets.PBR_METAL_SMOOTH');
      }

      // 测试卡通平面材质
      const cartoonMaterial = MaterialPresets.createMaterial(
        MaterialPresetType.CARTOON_FLAT, 
        this.scene, 
        "testCartoon", 
        {
          baseColor: new BABYLON.Color3(1, 0.5, 0.2)
        }
      );
      
      if (!cartoonMaterial || !cartoonMaterial.material) {
        result.errors.push('卡通平面材质创建失败');
        result.success = false;
      } else {
        result.testedComponents.push('MaterialPresets.CARTOON_FLAT');
      }

      console.log('[PresetValidator] 材质预设验证完成');
      
    } catch (error) {
      result.errors.push(`材质预设验证失败: ${error instanceof Error ? error.message : '未知错误'}`);
      result.success = false;
    }
  }

  /**
   * 验证粒子预设
   */
  private async validateParticlePresets(result: ValidationResult): Promise<void> {
    try {
      console.log('[PresetValidator] 开始验证粒子预设...');

      // 测试火焰粒子预设配置
      const fireConfig = PARTICLE_PRESETS[ParticlePresetType.FIRE_SMALL];
      
      if (!fireConfig || !fireConfig.capacity || !fireConfig.emitRate) {
        result.errors.push('火焰粒子预设配置无效');
        result.success = false;
      } else {
        result.testedComponents.push('ParticlePresets.FIRE_SMALL');
      }

      // 测试爆炸粒子预设配置
      const explosionConfig = PARTICLE_PRESETS[ParticlePresetType.EXPLOSION_LARGE];
      
      if (!explosionConfig || !explosionConfig.capacity || !explosionConfig.emitRate) {
        result.errors.push('爆炸粒子预设配置无效');
        result.success = false;
      } else {
        result.testedComponents.push('ParticlePresets.EXPLOSION_LARGE');
      }

      console.log('[PresetValidator] 粒子预设验证完成');
      
    } catch (error) {
      result.errors.push(`粒子预设验证失败: ${error instanceof Error ? error.message : '未知错误'}`);
      result.success = false;
    }
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.scene.dispose();
    this.engine.dispose();
  }
}

/**
 * 运行预设模块验证的便捷函数
 */
export async function validateBabylonPresets(canvas: HTMLCanvasElement): Promise<ValidationResult> {
  const validator = new PresetValidator(canvas);
  const result = await validator.validateAllPresets();
  validator.dispose();
  return result;
}
