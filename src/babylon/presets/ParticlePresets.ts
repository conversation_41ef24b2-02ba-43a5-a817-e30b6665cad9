/**
 * ParticlePresets - 粒子系统预设组件库
 * 提供标准化的粒子效果预设配置，包括火焰、烟雾、爆炸、魔法特效等
 * 基于Babylon.js 8.17.2最新API实现
 */

import * as BABYLON from '@babylonjs/core';

/**
 * 粒子预设类型枚举
 */
export enum ParticlePresetType {
  // 火焰效果
  FIRE_SMALL = 'fire_small',
  FIRE_LARGE = 'fire_large',
  FIRE_TORCH = 'fire_torch',
  
  // 烟雾效果
  SMOKE_LIGHT = 'smoke_light',
  SMOKE_HEAVY = 'smoke_heavy',
  SMOKE_COLORED = 'smoke_colored',
  
  // 爆炸效果
  EXPLOSION_SMALL = 'explosion_small',
  EXPLOSION_LARGE = 'explosion_large',
  EXPLOSION_SPARKS = 'explosion_sparks',
  
  // 魔法特效
  MAGIC_SPARKLES = 'magic_sparkles',
  MAGIC_ENERGY = 'magic_energy',
  MAGIC_PORTAL = 'magic_portal',
  MAGIC_HEALING = 'magic_healing',
  
  // 环境效果
  RAIN = 'rain',
  SNOW = 'snow',
  DUST = 'dust',
  LEAVES = 'leaves',
  
  // 能量效果
  ENERGY_BEAM = 'energy_beam',
  ENERGY_FIELD = 'energy_field',
  ENERGY_ORBS = 'energy_orbs'
}

/**
 * 粒子预设配置接口
 */
export interface ParticlePresetConfig {
  name: string;
  description: string;
  category: 'fire' | 'smoke' | 'explosion' | 'magic' | 'environment' | 'energy';
  
  // 基础配置
  capacity: number;
  emitRate: number;
  
  // 粒子属性
  minSize: number;
  maxSize: number;
  minLifeTime: number;
  maxLifeTime: number;
  
  // 发射属性
  minEmitPower: number;
  maxEmitPower: number;
  direction1: BABYLON.Vector3;
  direction2: BABYLON.Vector3;
  
  // 物理属性
  gravity: BABYLON.Vector3;
  updateSpeed: number;
  
  // 视觉属性
  color1: BABYLON.Color4;
  color2: BABYLON.Color4;
  colorDead: BABYLON.Color4;
  
  // 发射区域
  minEmitBox: BABYLON.Vector3;
  maxEmitBox: BABYLON.Vector3;
  
  // 纹理路径
  texturePath?: string;
  
  // 高级配置
  blendMode?: number;
  targetStopDuration?: number;
  disposeOnStop: boolean;
}

/**
 * 粒子预设配置数据
 */
export const PARTICLE_PRESETS: Record<ParticlePresetType, ParticlePresetConfig> = {
  // 火焰效果
  [ParticlePresetType.FIRE_SMALL]: {
    name: '小火焰',
    description: '小型火焰效果，适用于蜡烛、火把等',
    category: 'fire',
    capacity: 200,
    emitRate: 50,
    minSize: 0.3,
    maxSize: 0.8,
    minLifeTime: 2.0,
    maxLifeTime: 4.0,
    minEmitPower: 1,
    maxEmitPower: 3,
    direction1: new BABYLON.Vector3(-0.5, 1, -0.5),
    direction2: new BABYLON.Vector3(0.5, 2, 0.5),
    gravity: new BABYLON.Vector3(0, -2, 0),
    updateSpeed: 0.02,
    color1: new BABYLON.Color4(1, 0.8, 0.2, 1),
    color2: new BABYLON.Color4(1, 0.4, 0, 1),
    colorDead: new BABYLON.Color4(0.5, 0.1, 0, 0),
    minEmitBox: new BABYLON.Vector3(-0.1, 0, -0.1),
    maxEmitBox: new BABYLON.Vector3(0.1, 0, 0.1),
    blendMode: BABYLON.ParticleSystem.BLENDMODE_ONEONE,
    targetStopDuration: 2.0,
    disposeOnStop: false
  },

  [ParticlePresetType.FIRE_LARGE]: {
    name: '大火焰',
    description: '大型火焰效果，适用于篝火、爆炸等',
    category: 'fire',
    capacity: 500,
    emitRate: 120,
    minSize: 0.8,
    maxSize: 2.0,
    minLifeTime: 2.0,
    maxLifeTime: 4.0,
    minEmitPower: 1,
    maxEmitPower: 3,
    direction1: new BABYLON.Vector3(-0.8, 1.5, -0.8),
    direction2: new BABYLON.Vector3(0.8, 2.5, 0.8), // 降低向上发射力度
    gravity: new BABYLON.Vector3(0, -1.5, 0), // 增加重力以平衡发射力
    updateSpeed: 0.02,
    color1: new BABYLON.Color4(1, 0.7, 0.1, 1),
    color2: new BABYLON.Color4(1, 0.3, 0, 1),
    colorDead: new BABYLON.Color4(0.3, 0.1, 0, 0),
    minEmitBox: new BABYLON.Vector3(-0.3, 0, -0.3),
    maxEmitBox: new BABYLON.Vector3(0.3, 0, 0.3),
    blendMode: BABYLON.ParticleSystem.BLENDMODE_ONEONE,
    targetStopDuration: undefined, // 持续燃烧，不自动停止
    disposeOnStop: false
  },

  [ParticlePresetType.FIRE_TORCH]: {
    name: '火把',
    description: '火把火焰效果，稳定向上燃烧',
    category: 'fire',
    capacity: 300,
    emitRate: 80,
    minSize: 0.4,
    maxSize: 1.2,
    minLifeTime: 2.0,
    maxLifeTime: 3.5,
    minEmitPower: 1,
    maxEmitPower: 2.5,
    direction1: new BABYLON.Vector3(-0.3, 1.2, -0.3),
    direction2: new BABYLON.Vector3(0.3, 2.0, 0.3), // 降低向上发射力度
    gravity: new BABYLON.Vector3(0, -1.0, 0), // 增加重力以平衡发射力
    updateSpeed: 0.015,
    color1: new BABYLON.Color4(1, 0.9, 0.3, 1),
    color2: new BABYLON.Color4(1, 0.5, 0.1, 1),
    colorDead: new BABYLON.Color4(0.4, 0.1, 0, 0),
    minEmitBox: new BABYLON.Vector3(-0.05, 0, -0.05),
    maxEmitBox: new BABYLON.Vector3(0.05, 0, 0.05),
    blendMode: BABYLON.ParticleSystem.BLENDMODE_ONEONE,
    targetStopDuration: undefined, // 持续燃烧，不自动停止
    disposeOnStop: false
  },

  // 烟雾效果
  [ParticlePresetType.SMOKE_LIGHT]: {
    name: '轻烟',
    description: '轻薄的烟雾效果',
    category: 'smoke',
    capacity: 400,
    emitRate: 60,
    minSize: 1.0,
    maxSize: 3.0,
    minLifeTime: 3.0,
    maxLifeTime: 6.0,
    minEmitPower: 0.5,
    maxEmitPower: 2,
    direction1: new BABYLON.Vector3(-0.5, 0.5, -0.5),
    direction2: new BABYLON.Vector3(0.5, 2, 0.5),
    gravity: new BABYLON.Vector3(0, 1, 0),
    updateSpeed: 0.01,
    color1: new BABYLON.Color4(0.8, 0.8, 0.8, 0.6),
    color2: new BABYLON.Color4(0.6, 0.6, 0.6, 0.4),
    colorDead: new BABYLON.Color4(0.4, 0.4, 0.4, 0),
    minEmitBox: new BABYLON.Vector3(-0.2, 0, -0.2),
    maxEmitBox: new BABYLON.Vector3(0.2, 0, 0.2),
    blendMode: BABYLON.ParticleSystem.BLENDMODE_STANDARD,
    targetStopDuration: 2.0,
    disposeOnStop: false
  },

  [ParticlePresetType.SMOKE_HEAVY]: {
    name: '浓烟',
    description: '浓重的烟雾效果',
    category: 'smoke',
    capacity: 600,
    emitRate: 100,
    minSize: 1.5,
    maxSize: 4.0,
    minLifeTime: 4.0,
    maxLifeTime: 8.0,
    minEmitPower: 1,
    maxEmitPower: 3,
    direction1: new BABYLON.Vector3(-1, 0.5, -1),
    direction2: new BABYLON.Vector3(1, 2.5, 1),
    gravity: new BABYLON.Vector3(0, 0.5, 0),
    updateSpeed: 0.015,
    color1: new BABYLON.Color4(0.3, 0.3, 0.3, 0.8),
    color2: new BABYLON.Color4(0.2, 0.2, 0.2, 0.6),
    colorDead: new BABYLON.Color4(0.1, 0.1, 0.1, 0),
    minEmitBox: new BABYLON.Vector3(-0.4, 0, -0.4),
    maxEmitBox: new BABYLON.Vector3(0.4, 0, 0.4),
    blendMode: BABYLON.ParticleSystem.BLENDMODE_STANDARD,
    targetStopDuration: 3.0,
    disposeOnStop: false
  },

  [ParticlePresetType.SMOKE_COLORED]: {
    name: '彩色烟雾',
    description: '彩色的烟雾效果，适用于魔法场景',
    category: 'smoke',
    capacity: 350,
    emitRate: 70,
    minSize: 0.8,
    maxSize: 2.5,
    minLifeTime: 2.5,
    maxLifeTime: 5.0,
    minEmitPower: 0.8,
    maxEmitPower: 2.5,
    direction1: new BABYLON.Vector3(-0.8, 1, -0.8),
    direction2: new BABYLON.Vector3(0.8, 2.5, 0.8),
    gravity: new BABYLON.Vector3(0, 0.8, 0),
    updateSpeed: 0.012,
    color1: new BABYLON.Color4(0.8, 0.3, 0.8, 0.7),
    color2: new BABYLON.Color4(0.3, 0.8, 0.8, 0.5),
    colorDead: new BABYLON.Color4(0.2, 0.2, 0.6, 0),
    minEmitBox: new BABYLON.Vector3(-0.3, 0, -0.3),
    maxEmitBox: new BABYLON.Vector3(0.3, 0, 0.3),
    blendMode: BABYLON.ParticleSystem.BLENDMODE_STANDARD,
    targetStopDuration: 2.5,
    disposeOnStop: false
  },

  // 爆炸效果
  [ParticlePresetType.EXPLOSION_SMALL]: {
    name: '小爆炸',
    description: '小型爆炸效果',
    category: 'explosion',
    capacity: 300,
    emitRate: 200,
    minSize: 0.5,
    maxSize: 1.5,
    minLifeTime: 2.0,
    maxLifeTime: 4.0,
    minEmitPower: 1.5, // 降低发射功率
    maxEmitPower: 3,
    direction1: new BABYLON.Vector3(-1, 0, -1),
    direction2: new BABYLON.Vector3(1, 2, 1),
    gravity: new BABYLON.Vector3(0, -1, 0),
    updateSpeed: 0.02,
    color1: new BABYLON.Color4(1, 0.8, 0.2, 1),
    color2: new BABYLON.Color4(1, 0.4, 0.1, 1),
    colorDead: new BABYLON.Color4(0.3, 0.1, 0, 0),
    minEmitBox: new BABYLON.Vector3(-0.1, -0.1, -0.1),
    maxEmitBox: new BABYLON.Vector3(0.1, 0.1, 0.1),
    blendMode: BABYLON.ParticleSystem.BLENDMODE_ONEONE,
    targetStopDuration: 3.0, // 增加持续时间
    disposeOnStop: false
  },

  [ParticlePresetType.EXPLOSION_LARGE]: {
    name: '大爆炸',
    description: '大型爆炸效果',
    category: 'explosion',
    capacity: 800,
    emitRate: 500,
    minSize: 1.0,
    maxSize: 3.0,
    minLifeTime: 2.5,
    maxLifeTime: 3.0,
    minEmitPower: 2, // 降低发射功率
    maxEmitPower: 4,
    direction1: new BABYLON.Vector3(-2, 0, -2),
    direction2: new BABYLON.Vector3(2, 3, 2),
    gravity: new BABYLON.Vector3(0, -1.5, 0),
    updateSpeed: 0.02,
    color1: new BABYLON.Color4(1, 0.7, 0.1, 1),
    color2: new BABYLON.Color4(1, 0.3, 0, 1),
    colorDead: new BABYLON.Color4(0.2, 0.1, 0, 0),
    minEmitBox: new BABYLON.Vector3(-0.2, -0.2, -0.2),
    maxEmitBox: new BABYLON.Vector3(0.2, 0.2, 0.2),
    blendMode: BABYLON.ParticleSystem.BLENDMODE_ONEONE,
    targetStopDuration: 3.0, // 增加持续时间
    disposeOnStop: false
  },

  [ParticlePresetType.EXPLOSION_SPARKS]: {
    name: '火花爆炸',
    description: '火花四溅的爆炸效果',
    category: 'explosion',
    capacity: 400,
    emitRate: 300,
    minSize: 0.1,
    maxSize: 0.4,
    minLifeTime: 2.0,
    maxLifeTime: 5.0,
    minEmitPower: 3, // 降低发射功率
    maxEmitPower: 6,
    direction1: new BABYLON.Vector3(-1.5, -0.5, -1.5),
    direction2: new BABYLON.Vector3(1.5, 1.5, 1.5),
    gravity: new BABYLON.Vector3(0, -5, 0), // 降低重力
    updateSpeed: 0.025,
    color1: new BABYLON.Color4(1, 1, 0.3, 1),
    color2: new BABYLON.Color4(1, 0.5, 0.1, 1),
    colorDead: new BABYLON.Color4(0.5, 0.2, 0, 0),
    minEmitBox: new BABYLON.Vector3(-0.05, -0.05, -0.05),
    maxEmitBox: new BABYLON.Vector3(0.05, 0.05, 0.05),
    blendMode: BABYLON.ParticleSystem.BLENDMODE_ONEONE,
    targetStopDuration: 3.0, // 增加持续时间
    disposeOnStop: false // 改为false以便测试观察
  },

  // 魔法特效
  [ParticlePresetType.MAGIC_SPARKLES]: {
    name: '魔法闪光',
    description: '闪闪发光的魔法粒子',
    category: 'magic',
    capacity: 250,
    emitRate: 40,
    minSize: 0.2,
    maxSize: 0.6,
    minLifeTime: 2.0,
    maxLifeTime: 4.0,
    minEmitPower: 1,
    maxEmitPower: 3,
    direction1: new BABYLON.Vector3(-0.5, 0.5, -0.5),
    direction2: new BABYLON.Vector3(0.5, 1.5, 0.5),
    gravity: new BABYLON.Vector3(0, 0.5, 0),
    updateSpeed: 0.015,
    color1: new BABYLON.Color4(0.8, 0.8, 1, 1),
    color2: new BABYLON.Color4(0.6, 0.9, 1, 0.8),
    colorDead: new BABYLON.Color4(0.4, 0.6, 0.8, 0),
    minEmitBox: new BABYLON.Vector3(-0.5, -0.5, -0.5),
    maxEmitBox: new BABYLON.Vector3(0.5, 0.5, 0.5),
    texturePath: undefined, // 使用默认程序化纹理
    blendMode: BABYLON.ParticleSystem.BLENDMODE_ONEONE,
    targetStopDuration: 1.0,
    disposeOnStop: false
  },

  [ParticlePresetType.MAGIC_ENERGY]: {
    name: '魔法能量',
    description: '流动的魔法能量效果',
    category: 'magic',
    capacity: 300,
    emitRate: 60,
    minSize: 0.3,
    maxSize: 0.8,
    minLifeTime: 2.5,
    maxLifeTime: 4.0,
    minEmitPower: 1,
    maxEmitPower: 2.5,
    direction1: new BABYLON.Vector3(-1, 0.5, -1),
    direction2: new BABYLON.Vector3(1, 2, 1),
    gravity: new BABYLON.Vector3(0, 0.2, 0),
    updateSpeed: 0.015,
    color1: new BABYLON.Color4(0.9, 0.3, 0.9, 1),
    color2: new BABYLON.Color4(0.6, 0.8, 1, 0.8),
    colorDead: new BABYLON.Color4(0.3, 0.5, 0.7, 0),
    minEmitBox: new BABYLON.Vector3(-0.3, 0, -0.3),
    maxEmitBox: new BABYLON.Vector3(0.3, 0, 0.3),
    blendMode: BABYLON.ParticleSystem.BLENDMODE_ONEONE,
    targetStopDuration: 1.5,
    disposeOnStop: false
  },

  [ParticlePresetType.MAGIC_PORTAL]: {
    name: '魔法传送门',
    description: '传送门的魔法粒子效果',
    category: 'magic',
    capacity: 400,
    emitRate: 80,
    minSize: 0.4,
    maxSize: 1.0,
    minLifeTime: 2.0,
    maxLifeTime: 4.0,
    minEmitPower: 1.5,
    maxEmitPower: 3.5,
    direction1: new BABYLON.Vector3(-0.8, -0.2, -0.8),
    direction2: new BABYLON.Vector3(0.8, 0.8, 0.8),
    gravity: new BABYLON.Vector3(0, 0, 0),
    updateSpeed: 0.018,
    color1: new BABYLON.Color4(0.2, 0.8, 1, 1),
    color2: new BABYLON.Color4(0.8, 0.2, 1, 0.8),
    colorDead: new BABYLON.Color4(0.1, 0.1, 0.5, 0),
    minEmitBox: new BABYLON.Vector3(-1, -1, -1),
    maxEmitBox: new BABYLON.Vector3(1, 1, 1),
    blendMode: BABYLON.ParticleSystem.BLENDMODE_ONEONE,
    targetStopDuration: 2.0,
    disposeOnStop: false
  },

  [ParticlePresetType.MAGIC_HEALING]: {
    name: '治疗魔法',
    description: '治疗法术的粒子效果',
    category: 'magic',
    capacity: 200,
    emitRate: 50,
    minSize: 0.3,
    maxSize: 0.7,
    minLifeTime: 2.5,
    maxLifeTime: 4.5,
    minEmitPower: 0.5,
    maxEmitPower: 2,
    direction1: new BABYLON.Vector3(-0.3, 1, -0.3),
    direction2: new BABYLON.Vector3(0.3, 2.5, 0.3),
    gravity: new BABYLON.Vector3(0, 0.3, 0), // 降低向上重力，避免粒子快速飞出视野
    updateSpeed: 0.012,
    color1: new BABYLON.Color4(0.3, 1, 0.3, 1),
    color2: new BABYLON.Color4(0.6, 1, 0.8, 0.8),
    colorDead: new BABYLON.Color4(0.2, 0.6, 0.4, 0),
    minEmitBox: new BABYLON.Vector3(-0.2, 0, -0.2),
    maxEmitBox: new BABYLON.Vector3(0.2, 0, 0.2),
    blendMode: BABYLON.ParticleSystem.BLENDMODE_ONEONE,
    targetStopDuration: undefined, // 持续效果，不自动停止
    disposeOnStop: false
  },

  // 环境效果
  [ParticlePresetType.RAIN]: {
    name: '雨滴',
    description: '下雨效果，垂直下落的雨滴',
    category: 'environment',
    capacity: 1000,
    emitRate: 300,
    minSize: 0.05,
    maxSize: 0.15,
    minLifeTime: 2.5,
    maxLifeTime: 5.0,
    minEmitPower: 3,
    maxEmitPower: 5,
    direction1: new BABYLON.Vector3(-0.2, -1, -0.2),
    direction2: new BABYLON.Vector3(0.2, -1, 0.2),
    gravity: new BABYLON.Vector3(0, -8, 0),
    updateSpeed: 0.025,
    color1: new BABYLON.Color4(0.7, 0.8, 1, 0.8),
    color2: new BABYLON.Color4(0.5, 0.7, 0.9, 0.6),
    colorDead: new BABYLON.Color4(0.3, 0.5, 0.7, 0),
    minEmitBox: new BABYLON.Vector3(-3, 3, -3),
    maxEmitBox: new BABYLON.Vector3(3, 3, 3),
    blendMode: BABYLON.ParticleSystem.BLENDMODE_STANDARD,
    targetStopDuration: 1.0,
    disposeOnStop: false
  },

  [ParticlePresetType.SNOW]: {
    name: '雪花',
    description: '飘雪效果，缓慢飘落的雪花',
    category: 'environment',
    capacity: 800,
    emitRate: 100,
    minSize: 0.1,
    maxSize: 0.3,
    minLifeTime: 8.0,
    maxLifeTime: 15.0,
    minEmitPower: 0.5,
    maxEmitPower: 2,
    direction1: new BABYLON.Vector3(-1, -0.5, -1),
    direction2: new BABYLON.Vector3(1, -0.2, 1),
    gravity: new BABYLON.Vector3(0, -1, 0),
    updateSpeed: 0.008,
    color1: new BABYLON.Color4(1, 1, 1, 0.9),
    color2: new BABYLON.Color4(0.9, 0.9, 1, 0.7),
    colorDead: new BABYLON.Color4(0.8, 0.8, 0.9, 0),
    minEmitBox: new BABYLON.Vector3(-15, 15, -15),
    maxEmitBox: new BABYLON.Vector3(15, 15, 15),
    blendMode: BABYLON.ParticleSystem.BLENDMODE_STANDARD,
    targetStopDuration: 2.0,
    disposeOnStop: false
  },

  [ParticlePresetType.DUST]: {
    name: '灰尘',
    description: '飘浮的灰尘粒子',
    category: 'environment',
    capacity: 300,
    emitRate: 30,
    minSize: 0.1,
    maxSize: 0.4,
    minLifeTime: 5.0,
    maxLifeTime: 10.0,
    minEmitPower: 0.2,
    maxEmitPower: 1,
    direction1: new BABYLON.Vector3(-0.5, -0.1, -0.5),
    direction2: new BABYLON.Vector3(0.5, 0.5, 0.5),
    gravity: new BABYLON.Vector3(0, -0.2, 0),
    updateSpeed: 0.005,
    color1: new BABYLON.Color4(0.8, 0.7, 0.6, 0.4),
    color2: new BABYLON.Color4(0.6, 0.5, 0.4, 0.3),
    colorDead: new BABYLON.Color4(0.4, 0.3, 0.2, 0),
    minEmitBox: new BABYLON.Vector3(-2, 0, -2),
    maxEmitBox: new BABYLON.Vector3(2, 2, 2),
    blendMode: BABYLON.ParticleSystem.BLENDMODE_STANDARD,
    targetStopDuration: 3.0,
    disposeOnStop: false
  },

  [ParticlePresetType.LEAVES]: {
    name: '落叶',
    description: '飘落的树叶效果',
    category: 'environment',
    capacity: 200,
    emitRate: 20,
    minSize: 0.3,
    maxSize: 0.8,
    minLifeTime: 6.0,
    maxLifeTime: 12.0,
    minEmitPower: 0.5,
    maxEmitPower: 2,
    direction1: new BABYLON.Vector3(-1, -0.3, -1),
    direction2: new BABYLON.Vector3(1, 0.2, 1),
    gravity: new BABYLON.Vector3(0, -0.8, 0),
    updateSpeed: 0.01,
    color1: new BABYLON.Color4(0.8, 0.6, 0.2, 1),
    color2: new BABYLON.Color4(0.6, 0.4, 0.1, 0.8),
    colorDead: new BABYLON.Color4(0.4, 0.2, 0.1, 0),
    minEmitBox: new BABYLON.Vector3(-5, 8, -5),
    maxEmitBox: new BABYLON.Vector3(5, 10, 5),
    blendMode: BABYLON.ParticleSystem.BLENDMODE_STANDARD,
    targetStopDuration: 2.5,
    disposeOnStop: false
  },

  // 能量效果
  [ParticlePresetType.ENERGY_BEAM]: {
    name: '能量光束',
    description: '集中的能量光束效果',
    category: 'energy',
    capacity: 150,
    emitRate: 100,
    minSize: 0.2,
    maxSize: 0.5,
    minLifeTime: 2.0,
    maxLifeTime: 3.0,
    minEmitPower: 2, // 降低发射功率，避免粒子瞬间飞出视野
    maxEmitPower: 4,
    direction1: new BABYLON.Vector3(-0.3, 0.5, -0.3), // 扩大发射方向范围
    direction2: new BABYLON.Vector3(0.3, 1.5, 0.3),
    gravity: new BABYLON.Vector3(0, 0, 0),
    updateSpeed: 0.04,
    color1: new BABYLON.Color4(0.2, 0.8, 1, 1),
    color2: new BABYLON.Color4(0.8, 1, 1, 0.8),
    colorDead: new BABYLON.Color4(0.1, 0.4, 0.6, 0),
    minEmitBox: new BABYLON.Vector3(-0.05, -0.05, -0.05),
    maxEmitBox: new BABYLON.Vector3(0.05, 0.05, 0.05),
    blendMode: BABYLON.ParticleSystem.BLENDMODE_ONEONE,
    targetStopDuration: undefined, // 持续效果，不自动停止
    disposeOnStop: false
  },

  [ParticlePresetType.ENERGY_FIELD]: {
    name: '能量场',
    description: '环绕的能量场效果',
    category: 'energy',
    capacity: 300,
    emitRate: 60,
    minSize: 0.3,
    maxSize: 0.8,
    minLifeTime: 2.0,
    maxLifeTime: 4.0,
    minEmitPower: 1,
    maxEmitPower: 3,
    direction1: new BABYLON.Vector3(-1, -0.5, -1),
    direction2: new BABYLON.Vector3(1, 0.5, 1),
    gravity: new BABYLON.Vector3(0, 0, 0),
    updateSpeed: 0.015,
    color1: new BABYLON.Color4(1, 0.8, 0.2, 0.8),
    color2: new BABYLON.Color4(1, 1, 0.6, 0.6),
    colorDead: new BABYLON.Color4(0.6, 0.4, 0.1, 0),
    minEmitBox: new BABYLON.Vector3(-1.5, -1.5, -1.5),
    maxEmitBox: new BABYLON.Vector3(1.5, 1.5, 1.5),
    blendMode: BABYLON.ParticleSystem.BLENDMODE_ONEONE,
    targetStopDuration: undefined, // 持续效果，不自动停止
    disposeOnStop: false
  },

  [ParticlePresetType.ENERGY_ORBS]: {
    name: '能量球',
    description: '浮动的能量球粒子',
    category: 'energy',
    capacity: 100,
    emitRate: 20,
    minSize: 0.4,
    maxSize: 1.0,
    minLifeTime: 3.0,
    maxLifeTime: 6.0,
    minEmitPower: 0.5,
    maxEmitPower: 2,
    direction1: new BABYLON.Vector3(-0.5, -0.5, -0.5),
    direction2: new BABYLON.Vector3(0.5, 0.5, 0.5),
    gravity: new BABYLON.Vector3(0, 0.2, 0),
    updateSpeed: 0.01,
    color1: new BABYLON.Color4(0.8, 0.2, 1, 1),
    color2: new BABYLON.Color4(0.4, 0.8, 1, 0.8),
    colorDead: new BABYLON.Color4(0.2, 0.4, 0.6, 0),
    minEmitBox: new BABYLON.Vector3(-2, -2, -2),
    maxEmitBox: new BABYLON.Vector3(2, 2, 2),
    blendMode: BABYLON.ParticleSystem.BLENDMODE_ONEONE,
    targetStopDuration: undefined, // 持续效果，不自动停止
    disposeOnStop: false
  }
};

/**
 * 粒子预设管理类
 * 提供创建和管理粒子系统预设的静态方法
 */
export class ParticlePresets {
  
  /**
   * 根据类型创建粒子系统预设
   */
  static createParticleSystemByType(
    type: ParticlePresetType,
    scene: BABYLON.Scene,
    name?: string,
    customConfig?: Partial<ParticlePresetConfig>
  ): BABYLON.ParticleSystem {
    const config = this.getParticleConfig(type, customConfig);
    const particleName = name || `${type}_particle`;
    
    return this.createParticleSystem(particleName, config, scene);
  }
  
  /**
   * 获取粒子预设配置
   */
  private static getParticleConfig(
    type: ParticlePresetType,
    customConfig?: Partial<ParticlePresetConfig>
  ): ParticlePresetConfig {
    const baseConfig = PARTICLE_PRESETS[type];
    if (!baseConfig) {
      throw new Error(`Unknown particle preset type: ${type}`);
    }
    
    const config = { ...baseConfig, ...customConfig };
    
    // 确保必要的属性有默认值
    if (config.targetStopDuration === undefined) {
      config.targetStopDuration = 5; // 默认5秒
    }
    if (config.disposeOnStop === undefined) {
      config.disposeOnStop = false;
    }
    
    return config as ParticlePresetConfig;
  }
  
  /**
   * 创建默认的粒子纹理
   */
  private static createDefaultParticleTexture(scene: BABYLON.Scene): BABYLON.DynamicTexture {
    const size = 64;
    const texture = new BABYLON.DynamicTexture("defaultParticleTexture", size, scene);
    const context = texture.getContext();
    
    // 创建一个白色到透明的径向渐变圆形
    const centerX = size / 2;
    const centerY = size / 2;
    const radius = size / 2 - 2;
    
    const gradient = context.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
    gradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
    gradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.8)');
    gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
    
    context.fillStyle = gradient;
    context.fillRect(0, 0, size, size);
    
    texture.update();
    return texture;
  }
  
  /**
   * 创建粒子系统
   */
  private static createParticleSystem(
    name: string,
    config: ParticlePresetConfig,
    scene: BABYLON.Scene
  ): BABYLON.ParticleSystem {
    const particleSystem = new BABYLON.ParticleSystem(name, config.capacity, scene);
    
    // 基础配置 - 纹理
    if (config.texturePath) {
      particleSystem.particleTexture = new BABYLON.Texture(config.texturePath, scene);
    } else {
      // 创建默认的程序化纹理
      particleSystem.particleTexture = this.createDefaultParticleTexture(scene);
    }
    particleSystem.emitter = BABYLON.Vector3.Zero();
    particleSystem.minEmitBox = config.minEmitBox;
    particleSystem.maxEmitBox = config.maxEmitBox;
    
    // 发射配置
    particleSystem.emitRate = config.emitRate;
    particleSystem.minEmitPower = config.minEmitPower;
    particleSystem.maxEmitPower = config.maxEmitPower;
    particleSystem.direction1 = config.direction1;
    particleSystem.direction2 = config.direction2;
    
    // 粒子属性
    particleSystem.minSize = config.minSize;
    particleSystem.maxSize = config.maxSize;
    particleSystem.minLifeTime = config.minLifeTime;
    particleSystem.maxLifeTime = config.maxLifeTime;
    
    // 颜色配置
    particleSystem.color1 = config.color1;
    particleSystem.color2 = config.color2;
    particleSystem.colorDead = config.colorDead;
    
    // 物理配置
    particleSystem.gravity = config.gravity;
    particleSystem.updateSpeed = config.updateSpeed;
    if (config.blendMode !== undefined) {
      particleSystem.blendMode = config.blendMode;
    }
    
    // 停止配置
    if (typeof config.targetStopDuration === 'number') {
      particleSystem.targetStopDuration = config.targetStopDuration;
    }
    particleSystem.disposeOnStop = config.disposeOnStop;
    
    return particleSystem;
  }
  
  /**
   * 获取所有可用的粒子预设类型
   */
  static getAvailableTypes(): ParticlePresetType[] {
    return Object.values(ParticlePresetType);
  }
  
  /**
   * 获取预设配置信息
   */
  static getPresetInfo(type: ParticlePresetType): ParticlePresetConfig | undefined {
    return PARTICLE_PRESETS[type];
  }
  
  /**
   * 批量创建多个粒子系统
   */
  static createMultipleParticleSystems(
    types: ParticlePresetType[],
    scene: BABYLON.Scene,
    namePrefix?: string
  ): BABYLON.ParticleSystem[] {
    return types.map((type, index) => {
      const name = namePrefix ? `${namePrefix}_${type}_${index}` : `${type}_${index}`;
      return this.createParticleSystemByType(type, scene, name);
    });
  }
}
