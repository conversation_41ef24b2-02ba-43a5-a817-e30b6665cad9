/**
 * BabylonEngineManager - Babylon.js 8.17.2 引擎管理器
 * 提供标准化的引擎创建、配置和生命周期管理
 * 基于最新Babylon.js 8.17.2 API实现
 */

import { Engine, EngineOptions, Scene } from "@babylonjs/core";

export interface BabylonEngineConfig extends EngineOptions {
  /** 是否启用抗锯齿 */
  antialias?: boolean;
  /** 是否适配设备像素比 */
  adaptToDeviceRatio?: boolean;
  /** 是否启用音频引擎 */
  audioEngine?: boolean;
  /** 是否启用离线支持 */
  deterministicLockstep?: boolean;
  /** 锁步最大步数 */
  lockstepMaxSteps?: number;
  /** 时间步长 */
  timeStep?: number;
  /** 是否启用调试模式 */
  debugMode?: boolean;
}

export interface EngineMetrics {
  /** FPS */
  fps: number;
  /** 渲染时间（毫秒） */
  renderTime: number;
  /** 绘制调用次数 */
  drawCalls: number;
  /** 三角形数量 */
  triangles: number;
  /** 纹理数量 */
  textures: number;
  /** 材质数量 */
  materials: number;
  /** 网格数量 */
  meshes: number;
  /** 内存使用（MB） */
  memoryUsage: number;
}

/**
 * Babylon.js引擎管理器 - 单例模式
 * 负责引擎的创建、配置、性能监控和生命周期管理
 */
export class BabylonEngineManager {
  private static instance: BabylonEngineManager;
  private engines: Map<string, Engine> = new Map();
  private activeEngine: Engine | null = null;
  private resizeHandlers: Map<string, () => void> = new Map();
  private performanceMonitor: {
    enabled: boolean;
    interval: number;
    callback?: (metrics: EngineMetrics) => void;
  } = {
    enabled: false,
    interval: 1000
  };

  private constructor() {
    // 私有构造函数，确保单例模式
  }

  /**
   * 获取引擎管理器实例
   */
  public static getInstance(): BabylonEngineManager {
    if (!BabylonEngineManager.instance) {
      BabylonEngineManager.instance = new BabylonEngineManager();
    }
    return BabylonEngineManager.instance;
  }

  /**
   * 创建标准化的Babylon.js引擎
   * @param canvas HTML Canvas元素
   * @param engineId 引擎唯一标识符
   * @param config 引擎配置选项
   * @returns 创建的引擎实例
   */
  public createEngine(
    canvas: HTMLCanvasElement,
    engineId: string = 'default',
    config: BabylonEngineConfig = {}
  ): Engine {
    // 如果引擎已存在，先销毁
    if (this.engines.has(engineId)) {
      this.destroyEngine(engineId);
    }

    // 默认配置
    const defaultConfig: BabylonEngineConfig = {
      antialias: true,
      adaptToDeviceRatio: true,
      audioEngine: true,
      deterministicLockstep: false,
      lockstepMaxSteps: 4,
      timeStep: 1/60,
      debugMode: process.env.NODE_ENV === 'development',
      ...config
    };

    try {
      // 创建引擎实例
      const engine = new Engine(
        canvas,
        defaultConfig.antialias,
        {
          adaptToDeviceRatio: defaultConfig.adaptToDeviceRatio,
          audioEngine: defaultConfig.audioEngine,
          deterministicLockstep: defaultConfig.deterministicLockstep,
          lockstepMaxSteps: defaultConfig.lockstepMaxSteps,
          timeStep: defaultConfig.timeStep,
          ...defaultConfig
        },
        defaultConfig.adaptToDeviceRatio
      );

      // 存储引擎实例
      this.engines.set(engineId, engine);
      this.activeEngine = engine;

      // 设置标准事件监听
      this.setupStandardEventListeners(engine, engineId);

      // 启用性能监控（如果配置了）
      if (this.performanceMonitor.enabled) {
        this.startPerformanceMonitoring(engine);
      }

      console.log(`[BabylonEngineManager] 引擎创建成功: ${engineId}`);
      
      return engine;
    } catch (error) {
      console.error(`[BabylonEngineManager] 引擎创建失败: ${engineId}`, error);
      throw error;
    }
  }

  /**
   * 获取指定引擎实例
   */
  public getEngine(engineId: string = 'default'): Engine | null {
    return this.engines.get(engineId) || null;
  }

  /**
   * 获取当前活跃引擎
   */
  public getActiveEngine(): Engine | null {
    return this.activeEngine;
  }

  /**
   * 设置活跃引擎
   */
  public setActiveEngine(engineId: string): boolean {
    const engine = this.engines.get(engineId);
    if (engine) {
      this.activeEngine = engine;
      return true;
    }
    return false;
  }

  /**
   * 销毁指定引擎
   */
  public destroyEngine(engineId: string): boolean {
    const engine = this.engines.get(engineId);
    if (!engine) {
      return false;
    }

    try {
      // 移除事件监听器
      const resizeHandler = this.resizeHandlers.get(engineId);
      if (resizeHandler) {
        window.removeEventListener('resize', resizeHandler);
        this.resizeHandlers.delete(engineId);
      }

      // 停止渲染循环
      engine.stopRenderLoop();

      // 销毁引擎
      engine.dispose();

      // 从管理器中移除
      this.engines.delete(engineId);

      // 如果是活跃引擎，清空引用
      if (this.activeEngine === engine) {
        this.activeEngine = null;
      }

      console.log(`[BabylonEngineManager] 引擎销毁成功: ${engineId}`);
      return true;
    } catch (error) {
      console.error(`[BabylonEngineManager] 引擎销毁失败: ${engineId}`, error);
      return false;
    }
  }

  /**
   * 销毁所有引擎
   */
  public destroyAllEngines(): void {
    const engineIds = Array.from(this.engines.keys());
    engineIds.forEach(id => this.destroyEngine(id));
  }

  /**
   * 启用性能监控
   */
  public enablePerformanceMonitoring(
    interval: number = 1000,
    callback?: (metrics: EngineMetrics) => void
  ): void {
    this.performanceMonitor = {
      enabled: true,
      interval,
      callback
    };

    // 为现有引擎启动监控
    this.engines.forEach(engine => {
      this.startPerformanceMonitoring(engine);
    });
  }

  /**
   * 禁用性能监控
   */
  public disablePerformanceMonitoring(): void {
    this.performanceMonitor.enabled = false;
  }

  /**
   * 获取引擎性能指标
   */
  public getEngineMetrics(engineId: string = 'default'): EngineMetrics | null {
    const engine = this.engines.get(engineId);
    if (!engine) {
      return null;
    }

    return {
      fps: engine.getFps(),
      renderTime: engine.getDeltaTime(),
      drawCalls: engine.getGlInfo().drawCalls || 0,
      triangles: engine.getGlInfo().triangles || 0,
      textures: engine.getLoadedTexturesCache().length,
      materials: 0, // 需要从场景获取
      meshes: 0, // 需要从场景获取
      memoryUsage: (performance as any).memory?.usedJSHeapSize / 1024 / 1024 || 0
    };
  }

  /**
   * 设置标准事件监听器
   */
  private setupStandardEventListeners(engine: Engine, engineId: string): void {
    // 窗口大小变化处理
    const resizeHandler = () => {
      try {
        engine.resize();
      } catch (error) {
        console.error(`[BabylonEngineManager] 窗口大小调整失败: ${engineId}`, error);
      }
    };

    window.addEventListener('resize', resizeHandler);
    this.resizeHandlers.set(engineId, resizeHandler);

    // 引擎错误处理
    engine.onContextLostObservable.add(() => {
      console.warn(`[BabylonEngineManager] WebGL上下文丢失: ${engineId}`);
    });

    engine.onContextRestoredObservable.add(() => {
      console.log(`[BabylonEngineManager] WebGL上下文恢复: ${engineId}`);
    });
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(engine: Engine): void {
    if (!this.performanceMonitor.enabled) {
      return;
    }

    const monitoringInterval = setInterval(() => {
      if (engine.isDisposed) {
        clearInterval(monitoringInterval);
        return;
      }

      const metrics: EngineMetrics = {
        fps: engine.getFps(),
        renderTime: engine.getDeltaTime(),
        drawCalls: engine.getGlInfo().drawCalls || 0,
        triangles: engine.getGlInfo().triangles || 0,
        textures: engine.getLoadedTexturesCache().length,
        materials: 0,
        meshes: 0,
        memoryUsage: (performance as any).memory?.usedJSHeapSize / 1024 / 1024 || 0
      };

      if (this.performanceMonitor.callback) {
        this.performanceMonitor.callback(metrics);
      }
    }, this.performanceMonitor.interval);
  }
}

// 导出单例实例
export const babylonEngineManager = BabylonEngineManager.getInstance();
