import { 
  Scene, 
  Mesh, 
  Light, 
  Camera, 
  Vector3, 
  Color3, 
  MeshBuilder, 
  StandardMaterial,
  PBRMaterial,
  ArcRotateCamera,
  UniversalCamera,
  FreeCamera,
  FollowCamera,
  HemisphericLight,
  DirectionalLight,
  PointLight,
  SpotLight,
  TransformNode,
  Node
} from '@babylonjs/core';

import { NodeRegistry } from './NodeRegistry';
import { 
  GameNodeType, 
  MeshNodeProperties, 
  LightNodeProperties, 
  CameraNodeProperties,
  TransformNodeProperties
} from '../types/NodeTypes';

/**
 * 节点创建配置接口
 */
export interface NodeCreationConfig {
  id: string;
  name: string;
  position?: Vector3;
  rotation?: Vector3;
  scaling?: Vector3;
  parent?: string;
  visible?: boolean;
  enabled?: boolean;
}

/**
 * Mesh节点创建配置
 */
export interface MeshCreationConfig extends NodeCreationConfig {
  geometryType: 'box' | 'sphere' | 'cylinder' | 'plane' | 'ground' | 'custom';
  geometryParams?: Record<string, unknown>;
  materialType?: 'standard' | 'pbr';
  materialConfig?: {
    diffuseColor?: Color3;
    emissiveColor?: Color3;
    specularColor?: Color3;
    baseColor?: Color3;
    metallic?: number;
    roughness?: number;
  };
}

/**
 * Light节点创建配置
 */
export interface LightCreationConfig extends NodeCreationConfig {
  lightType: 'hemispheric' | 'directional' | 'point' | 'spot';
  intensity?: number;
  color?: Color3;
  direction?: Vector3;
  range?: number;
  angle?: number;
  exponent?: number;
}

/**
 * Camera节点创建配置
 */
export interface CameraCreationConfig extends NodeCreationConfig {
  cameraType: 'arc_rotate' | 'universal' | 'free' | 'follow';
  target?: Vector3;
  fov?: number;
  minZ?: number;
  maxZ?: number;
  radius?: number;
  alpha?: number;
  beta?: number;
}

/**
 * 节点工厂类
 * 负责标准化创建和配置不同类型的Babylon.js节点
 */
export class NodeFactory {
  private static instance: NodeFactory;
  private nodeRegistry: NodeRegistry;

  private constructor() {
    this.nodeRegistry = NodeRegistry.getInstance();
  }

  public static getInstance(): NodeFactory {
    if (!NodeFactory.instance) {
      NodeFactory.instance = new NodeFactory();
    }
    return NodeFactory.instance;
  }

  /**
   * 创建Mesh节点
   */
  public createMeshNode(scene: Scene, config: MeshCreationConfig): Mesh {
    console.log(`[NodeFactory] 创建Mesh节点: ${config.name} (${config.geometryType})`);

    // 创建几何体
    const mesh = this.createGeometry(scene, config);
    
    // 设置基础属性
    this.applyBaseNodeProperties(mesh, config);

    // 创建和应用材质
    if (config.materialType || config.materialConfig) {
      const material = this.createMaterial(scene, config);
      mesh.material = material;
    }

    // 注册到NodeRegistry
    const nodeProperties: MeshNodeProperties = {
      id: config.id,
      name: config.name,
      type: GameNodeType.MESH,
      visible: config.visible !== false,
      enabled: config.enabled !== false,
      position: config.position || Vector3.Zero(),
      rotation: config.rotation || Vector3.Zero(),
      scaling: config.scaling || Vector3.One(),
      parent: config.parent,
      children: [],
      geometry: {
        type: config.geometryType,
        parameters: config.geometryParams || {}
      },
      material: {
        type: config.materialType || 'standard',
        ...(config.materialConfig || {})
      }
    };

    this.nodeRegistry.register(nodeProperties);

    console.log(`[NodeFactory] Mesh节点创建完成: ${config.id}`);
    return mesh;
  }

  /**
   * 创建Light节点
   */
  public createLightNode(scene: Scene, config: LightCreationConfig): Light {
    console.log(`[NodeFactory] 创建Light节点: ${config.name} (${config.lightType})`);

    let light: Light;

    // 根据类型创建不同的光源
    switch (config.lightType) {
      case 'hemispheric':
        light = new HemisphericLight(config.id, Vector3.Up(), scene);
        break;
      case 'directional':
        light = new DirectionalLight(
          config.id, 
          config.direction || new Vector3(0, -1, 0), 
          scene
        );
        break;
      case 'point':
        light = new PointLight(
          config.id,
          config.position || Vector3.Zero(),
          scene
        );
        if (config.range) {
          (light as PointLight).range = config.range;
        }
        break;
      case 'spot':
        light = new SpotLight(
          config.id,
          config.position || Vector3.Zero(),
          config.direction || new Vector3(0, -1, 0),
          config.angle || Math.PI / 3,
          config.exponent || 2,
          scene
        );
        if (config.range) {
          (light as SpotLight).range = config.range;
        }
        break;
      default:
        throw new Error(`不支持的光源类型: ${config.lightType}`);
    }

    // 设置基础属性
    this.applyBaseNodeProperties(light, config);

    // 设置光源特有属性
    if (config.intensity !== undefined) {
      light.intensity = config.intensity;
    }
    if (config.color) {
      light.diffuse = config.color;
    }

    // 注册到NodeRegistry
    const nodeProperties: LightNodeProperties = {
      id: config.id,
      name: config.name,
      type: GameNodeType.LIGHT,
      visible: config.visible !== false,
      enabled: config.enabled !== false,
      position: config.position || Vector3.Zero(),
      rotation: config.rotation || Vector3.Zero(),
      scaling: config.scaling || Vector3.One(),
      parent: config.parent,
      children: [],
      lightType: config.lightType,
      intensity: config.intensity || 1.0,
      color: config.color || Color3.White(),
      direction: config.direction,
      range: config.range,
      angle: config.angle,
      exponent: config.exponent
    };

    this.nodeRegistry.register(nodeProperties);

    console.log(`[NodeFactory] Light节点创建完成: ${config.id}`);
    return light;
  }

  /**
   * 创建Camera节点
   */
  public createCameraNode(scene: Scene, config: CameraCreationConfig): Camera {
    console.log(`[NodeFactory] 创建Camera节点: ${config.name} (${config.cameraType})`);

    let camera: Camera;

    // 根据类型创建不同的相机
    switch (config.cameraType) {
      case 'arc_rotate':
        camera = new ArcRotateCamera(
          config.id,
          config.alpha || -Math.PI / 2,
          config.beta || Math.PI / 2.5,
          config.radius || 10,
          config.target || Vector3.Zero(),
          scene
        );
        break;
      case 'universal':
        camera = new UniversalCamera(
          config.id,
          config.position || new Vector3(0, 5, -10),
          scene
        );
        if (config.target) {
          (camera as UniversalCamera).setTarget(config.target);
        }
        break;
      case 'free':
        camera = new FreeCamera(
          config.id,
          config.position || new Vector3(0, 5, -10),
          scene
        );
        if (config.target) {
          (camera as FreeCamera).setTarget(config.target);
        }
        break;
      case 'follow':
        camera = new FollowCamera(
          config.id,
          config.position || new Vector3(0, 5, -10),
          scene
        );
        break;
      default:
        throw new Error(`不支持的相机类型: ${config.cameraType}`);
    }

    // 设置基础属性
    this.applyBaseNodeProperties(camera, config);

    // 设置相机特有属性
    if (config.fov !== undefined) {
      camera.fov = config.fov;
    }
    if (config.minZ !== undefined) {
      camera.minZ = config.minZ;
    }
    if (config.maxZ !== undefined) {
      camera.maxZ = config.maxZ;
    }

    // 注册到NodeRegistry
    const nodeProperties: CameraNodeProperties = {
      id: config.id,
      name: config.name,
      type: GameNodeType.CAMERA,
      visible: config.visible !== false,
      enabled: config.enabled !== false,
      position: config.position || Vector3.Zero(),
      rotation: config.rotation || Vector3.Zero(),
      scaling: config.scaling || Vector3.One(),
      parent: config.parent,
      children: [],
      cameraType: config.cameraType,
      fov: config.fov || 0.8,
      target: config.target || Vector3.Zero(),
      minZ: config.minZ || 0.1,
      maxZ: config.maxZ || 1000
    };

    this.nodeRegistry.register(nodeProperties);

    console.log(`[NodeFactory] Camera节点创建完成: ${config.id}`);
    return camera;
  }

  /**
   * 创建Transform节点
   */
  public createTransformNode(scene: Scene, config: NodeCreationConfig): TransformNode {
    console.log(`[NodeFactory] 创建Transform节点: ${config.name}`);

    const transformNode = new TransformNode(config.id, scene);
    
    // 设置基础属性
    this.applyBaseNodeProperties(transformNode, config);

    // 注册到NodeRegistry
    const nodeProperties: TransformNodeProperties = {
      id: config.id,
      name: config.name,
      type: GameNodeType.TRANSFORM,
      visible: config.visible !== false,
      enabled: config.enabled !== false,
      position: config.position || Vector3.Zero(),
      rotation: config.rotation || Vector3.Zero(),
      scaling: config.scaling || Vector3.One(),
      parent: config.parent,
      children: []
    };

    this.nodeRegistry.register(nodeProperties);

    console.log(`[NodeFactory] Transform节点创建完成: ${config.id}`);
    return transformNode;
  }

  /**
   * 创建几何体
   */
  private createGeometry(scene: Scene, config: MeshCreationConfig): Mesh {
    const params = config.geometryParams || {};

    switch (config.geometryType) {
      case 'box':
        return MeshBuilder.CreateBox(config.id, {
          size: (params.size as number) || 1,
          width: params.width as number,
          height: params.height as number,
          depth: params.depth as number,
          ...params
        }, scene);

      case 'sphere':
        return MeshBuilder.CreateSphere(config.id, {
          diameter: (params.diameter as number) || 1,
          segments: (params.segments as number) || 16,
          ...params
        }, scene);

      case 'cylinder':
        return MeshBuilder.CreateCylinder(config.id, {
          height: (params.height as number) || 2,
          diameterTop: (params.diameterTop as number) || 1,
          diameterBottom: (params.diameterBottom as number) || 1,
          tessellation: (params.tessellation as number) || 24,
          ...params
        }, scene);

      case 'plane':
        return MeshBuilder.CreatePlane(config.id, {
          size: (params.size as number) || 1,
          width: params.width as number,
          height: params.height as number,
          ...params
        }, scene);

      case 'ground':
        return MeshBuilder.CreateGround(config.id, {
          width: (params.width as number) || 1,
          height: (params.height as number) || 1,
          subdivisions: (params.subdivisions as number) || 1,
          ...params
        }, scene);

      default:
        throw new Error(`不支持的几何体类型: ${config.geometryType}`);
    }
  }

  /**
   * 创建材质
   */
  private createMaterial(scene: Scene, config: MeshCreationConfig): StandardMaterial | PBRMaterial {
    const materialType = config.materialType || 'standard';
    const materialConfig = config.materialConfig || {};
    const materialName = `${config.id}_material`;

    if (materialType === 'pbr') {
      const material = new PBRMaterial(materialName, scene);
      
      if (materialConfig.baseColor) {
        material.albedoColor = materialConfig.baseColor;
      }
      if (materialConfig.metallic !== undefined) {
        material.metallic = materialConfig.metallic;
      }
      if (materialConfig.roughness !== undefined) {
        material.roughness = materialConfig.roughness;
      }

      return material;
    } else {
      const material = new StandardMaterial(materialName, scene);
      
      if (materialConfig.diffuseColor) {
        material.diffuseColor = materialConfig.diffuseColor;
      }
      if (materialConfig.emissiveColor) {
        material.emissiveColor = materialConfig.emissiveColor;
      }
      if (materialConfig.specularColor) {
        material.specularColor = materialConfig.specularColor;
      }

      return material;
    }
  }

  /**
   * 应用基础节点属性
   */
  private applyBaseNodeProperties(node: Node, config: NodeCreationConfig): void {
    if (config.position) {
      node.setAbsolutePosition(config.position);
    }
    if (config.rotation) {
      if ('rotation' in node) {
        (node as any).rotation = config.rotation;
      }
    }
    if (config.scaling) {
      if ('scaling' in node) {
        (node as any).scaling = config.scaling;
      }
    }
    if (config.visible !== undefined) {
      if ('isVisible' in node) {
        (node as any).isVisible = config.visible;
      }
    }
    if (config.enabled !== undefined) {
      node.setEnabled(config.enabled);
    }
  }

  /**
   * 批量创建节点
   */
  public createNodesFromTemplate(scene: Scene, template: {
    meshes?: MeshCreationConfig[];
    lights?: LightCreationConfig[];
    cameras?: CameraCreationConfig[];
    transforms?: NodeCreationConfig[];
  }): {
    meshes: Mesh[];
    lights: Light[];
    cameras: Camera[];
    transforms: TransformNode[];
  } {
    console.log('[NodeFactory] 批量创建节点开始');

    const result = {
      meshes: [] as Mesh[],
      lights: [] as Light[],
      cameras: [] as Camera[],
      transforms: [] as TransformNode[]
    };

    // 创建Transform节点（作为父节点）
    if (template.transforms) {
      template.transforms.forEach(config => {
        const transform = this.createTransformNode(scene, config);
        result.transforms.push(transform);
      });
    }

    // 创建Mesh节点
    if (template.meshes) {
      template.meshes.forEach(config => {
        const mesh = this.createMeshNode(scene, config);
        result.meshes.push(mesh);
      });
    }

    // 创建Light节点
    if (template.lights) {
      template.lights.forEach(config => {
        const light = this.createLightNode(scene, config);
        result.lights.push(light);
      });
    }

    // 创建Camera节点
    if (template.cameras) {
      template.cameras.forEach(config => {
        const camera = this.createCameraNode(scene, config);
        result.cameras.push(camera);
      });
    }

    console.log('[NodeFactory] 批量创建节点完成', {
      meshes: result.meshes.length,
      lights: result.lights.length,
      cameras: result.cameras.length,
      transforms: result.transforms.length
    });

    return result;
  }

  /**
   * 获取节点创建代码模板
   */
  public generateNodeCreationCode(config: MeshCreationConfig | LightCreationConfig | CameraCreationConfig): string {
    const configType = this.getConfigType(config);
    
    switch (configType) {
      case 'mesh':
        return this.generateMeshCreationCode(config as MeshCreationConfig);
      case 'light':
        return this.generateLightCreationCode(config as LightCreationConfig);
      case 'camera':
        return this.generateCameraCreationCode(config as CameraCreationConfig);
      default:
        return '';
    }
  }

  private getConfigType(config: any): 'mesh' | 'light' | 'camera' {
    if ('geometryType' in config) return 'mesh';
    if ('lightType' in config) return 'light';
    if ('cameraType' in config) return 'camera';
    throw new Error('无法确定配置类型');
  }

  private generateMeshCreationCode(config: MeshCreationConfig): string {
    return `
// 创建${config.name}
const ${config.id} = nodeFactory.createMeshNode(scene, {
  id: "${config.id}",
  name: "${config.name}",
  geometryType: "${config.geometryType}",
  position: new Vector3(${config.position?.x || 0}, ${config.position?.y || 0}, ${config.position?.z || 0}),
  materialType: "${config.materialType || 'standard'}"
});`;
  }

  private generateLightCreationCode(config: LightCreationConfig): string {
    return `
// 创建${config.name}
const ${config.id} = nodeFactory.createLightNode(scene, {
  id: "${config.id}",
  name: "${config.name}",
  lightType: "${config.lightType}",
  position: new Vector3(${config.position?.x || 0}, ${config.position?.y || 0}, ${config.position?.z || 0}),
  intensity: ${config.intensity || 1.0}
});`;
  }

  private generateCameraCreationCode(config: CameraCreationConfig): string {
    return `
// 创建${config.name}
const ${config.id} = nodeFactory.createCameraNode(scene, {
  id: "${config.id}",
  name: "${config.name}",
  cameraType: "${config.cameraType}",
  position: new Vector3(${config.position?.x || 0}, ${config.position?.y || 5}, ${config.position?.z || -10}),
  fov: ${config.fov || 0.8}
});`;
  }
}

// 导出单例实例
export const nodeFactory = NodeFactory.getInstance(); 