/**
 * ScriptExecutionEngine - 标准化的脚本执行引擎
 * 提供一致的Babylon.js API环境和错误处理机制
 */

import { 
  Engine, 
  Scene, 
  Vector3, 
  Color3,
  Mesh,
  Animation,
  ActionManager,
  ExecuteCodeAction
} from '@babylonjs/core';

export interface ScriptExecutionEnvironment {
  scene: Scene | null;
  babylonAPI: Record<string, unknown>;
  mockNodeRegistry: {
    getInstance: () => {
      getNode: (id: string) => unknown;
      getNodeById: (id: string) => unknown;
      getAllNodes: () => unknown[];
      addNode: () => void;
      removeNode: () => void;
    };
  };
  NodeCommunicationService: unknown;
  registerCleanup: (fn: () => void) => void;
}

export interface ScriptProcessingOptions {
  removeImports?: boolean;
  removeExports?: boolean;
  removeTypeAnnotations?: boolean;
  processBabylonNamespace?: boolean;
  validateSyntax?: boolean;
  validateBabylonAPI?: boolean;
}

export class ScriptExecutionEngine {
  private scene: Scene | null = null;

  constructor(scene: Scene | null = null) {
    this.scene = scene;
  }

  /**
   * 创建标准化的Babylon.js API对象
   */
  createBabylonAPI(): Record<string, unknown> {
    return {
      // 核心类
      Vector3: Vector3,
      Color3: Color3,
      Scene: Scene,
      Engine: Engine,
      Mesh: Mesh,
      
      // 动画相关
      Animation: Object.assign(Animation, {
        ANIMATIONLOOPMODE_CYCLE: Animation.ANIMATIONLOOPMODE_CYCLE || 1,
        ANIMATIONLOOPMODE_CONSTANT: Animation.ANIMATIONLOOPMODE_CONSTANT || 0,
        ANIMATIONTYPE_FLOAT: Animation.ANIMATIONTYPE_FLOAT || 0,
        ANIMATIONTYPE_VECTOR3: Animation.ANIMATIONTYPE_VECTOR3 || 1,
      }),
      
      // 动作管理器
      ActionManager: Object.assign(ActionManager, {
        OnKeyDownTrigger: ActionManager.OnKeyDownTrigger || 1,
        OnKeyUpTrigger: ActionManager.OnKeyUpTrigger || 2,
        OnPickTrigger: ActionManager.OnPickTrigger || 3,
        OnIntersectionEnterTrigger: ActionManager.OnIntersectionEnterTrigger || 12,
        OnIntersectionExitTrigger: ActionManager.OnIntersectionExitTrigger || 13,
      }),
      
      ExecuteCodeAction: ExecuteCodeAction,
      
      // 扩展全局Babylon.js API
      ...(window.BABYLON || {})
    };
  }

  /**
   * 创建Mock的节点注册表
   */
  createMockNodeRegistry() {
    const findNodeByIdInternal = (id: string) => {
      let mesh = this.scene?.getNodeById(id) || this.scene?.getMeshById(id);
      
      if (!mesh && this.scene) {
        const meshes = this.scene.meshes;
        if (id.includes('player') || id.includes('box')) {
          mesh = meshes.find(m => 
            m.name.toLowerCase().includes('box') || 
            m.name.toLowerCase().includes('cube') ||
            m.name.toLowerCase().includes('player')
          );
        }
        
        if (!mesh) {
          mesh = meshes.find(m => m.isPickable && m.name !== '__root__');
        }
      }
      
      return mesh;
    };

    const mockNodeRegistryInstance = {
      getNode: findNodeByIdInternal,
      getNodeById: findNodeByIdInternal,
      getAllNodes: () => [],
      addNode: () => {},
      removeNode: () => {}
    };

    return {
      getInstance: () => mockNodeRegistryInstance
    };
  }

  /**
   * 创建Mock的通信服务
   */
  createMockNodeCommunicationService() {
    function NodeCommunicationService(this: { 
      sendMessage: (message: unknown) => void; 
      broadcast: (message: unknown) => void 
    }) {
      this.sendMessage = function(message: unknown) {
        console.log('[脚本CommService] 发送消息:', message);
      };
      this.broadcast = function(message: unknown) {
        console.log('[脚本CommService] 广播消息:', message);
      };
    }
    
    return NodeCommunicationService;
  }

  /**
   * 创建标准化的脚本执行环境
   */
  createExecutionEnvironment(cleanupCollector?: ((fn: () => void) => void)): ScriptExecutionEnvironment {
    return {
      scene: this.scene,
      babylonAPI: this.createBabylonAPI(),
      mockNodeRegistry: this.createMockNodeRegistry(),
      NodeCommunicationService: this.createMockNodeCommunicationService(),
      registerCleanup: cleanupCollector || (() => {})
    };
  }

  /**
   * 处理TypeScript脚本，转换为可执行的JavaScript
   */
  processScript(scriptContent: string, options: ScriptProcessingOptions = {}): string {
    const {
      removeImports = true,
      removeExports = true,
      removeTypeAnnotations = true,
      processBabylonNamespace = true
    } = options;

    let processedContent = scriptContent;

    // 检测是否需要处理TypeScript特性
    const needsProcessing = /import\s+.*?from|export\s+[\w{]|:\s*[a-zA-Z0-9_.]+(\s*\||[\[\]<>])|window\.BABYLON/.test(scriptContent);
    
    if (needsProcessing) {
      console.log('检测到TypeScript特性，开始处理脚本...');
      
      if (removeImports) {
        // 移除import语句
        processedContent = processedContent.replace(/import\s+.*?from\s+['"][^'"]*['"];?\s*/g, '');
      }
      
      if (removeExports) {
        // 移除各种export语句
        processedContent = processedContent.replace(/export\s+(function|const|let|var)/g, '$1');
        processedContent = processedContent.replace(/export\s*{\s*[^}]*\s*}\s*;?\s*/g, '');
        processedContent = processedContent.replace(/export\s+default\s+[^;]+;?\s*/g, '');
      }
      
      if (removeTypeAnnotations) {
        // 移除TypeScript类型注解
        processedContent = processedContent.replace(/\(\s*(\w+)\s*:\s*[a-zA-Z0-9_.]+\s*\)/g, '($1)');
        processedContent = processedContent.replace(/(function\s+\w+\s*\([^)]*\))\s*:\s*[a-zA-Z0-9_.|<>\[\]]+\s*/g, '$1 ');
        processedContent = processedContent.replace(/(let|const|var)\s+(\w+)\s*:\s*[a-zA-Z0-9_.|<>\[\]]+\s*=/g, '$1 $2 =');
        processedContent = processedContent.replace(/(\w+)\s*:\s*[a-zA-Z0-9_.|<>\[\]]+/g, '$1');
      }
      
      if (processBabylonNamespace) {
        // 处理BABYLON命名空间引用
        processedContent = processedContent.replace(/BABYLON\./g, 'window.BABYLON.');
      }
      
      console.log('脚本处理完成');
    } else {
      console.log('脚本为纯JavaScript，跳过处理');
    }
    
    // 语法修复
    processedContent = processedContent.replace(/(\s+)(\w+)\s*\?\s*([^:,}]+)\s*:\s*([^,}]+)(\s*[,}])/g, '$1$2: $2 ? $3 : $4$5');
    processedContent = processedContent.replace(/(\s+)(\w+)\s*\?\s*([^:,}]+)(\s*[,}])/g, '$1$2: $2 ? $3 : undefined$4');
    processedContent = processedContent.replace(/(\s+)Date\.now\(\)(\s*[,}])/g, '$1timestamp: Date.now()$2');

    return processedContent;
  }

  /**
   * 验证脚本语法
   */
  validateSyntax(scriptContent: string): void {
    try {
      new Function(scriptContent);
      console.log('脚本语法验证通过');
    } catch (syntaxError) {
      console.error('脚本语法错误:', syntaxError);
      throw new Error(`脚本语法错误: ${syntaxError instanceof Error ? syntaxError.message : '未知错误'}`);
    }
  }

  /**
   * 验证Babylon.js API可用性
   */
  validateBabylonAPI(): void {
    const missingAPIs: string[] = [];
    
    if (!Vector3) missingAPIs.push('Vector3');
    if (!Color3) missingAPIs.push('Color3');
    if (!Animation) missingAPIs.push('Animation');
    if (!ActionManager) missingAPIs.push('ActionManager');
    if (!ExecuteCodeAction) missingAPIs.push('ExecuteCodeAction');
    
    if (missingAPIs.length > 0) {
      console.error('缺少Babylon.js API:', missingAPIs);
      throw new Error(`缺少必要的Babylon.js API: ${missingAPIs.join(', ')}`);
    }
    
    console.log('Babylon.js API验证通过');
  }

  /**
   * 执行脚本
   */
  executeScript(
    scriptContent: string, 
    executionEnvironment: ScriptExecutionEnvironment,
    options: ScriptProcessingOptions = {}
  ): void {
    const { validateSyntax = true, validateBabylonAPI = true } = options;

    // 处理脚本
    const processedContent = this.processScript(scriptContent, options);

    // 验证
    if (validateSyntax) {
      this.validateSyntax(processedContent);
    }
    
    if (validateBabylonAPI) {
      this.validateBabylonAPI();
    }

    // 创建脚本执行函数
    const scriptFunction = new Function(
      'scene', 
      'BABYLON',
      'NodeRegistry', 
      'NodeCommunicationService',
      'registerCleanup',
      `
      (function() {
        console.log('[脚本执行] 收到参数:', {
          scene: !!scene,
          BABYLON: !!BABYLON,
          NodeRegistry: !!NodeRegistry,
          NodeCommunicationService: !!NodeCommunicationService,
          registerCleanup: typeof registerCleanup
        });
        
        ${processedContent}
        
        if (typeof executeScript === 'function') {
          executeScript(scene);
          console.log('[脚本执行] executeScript函数已执行');
        } else {
          console.warn('[脚本执行] 未找到executeScript函数');
        }
      })();
      `
    );

    // 执行脚本
    console.log('[脚本执行] 开始执行脚本，参数检查:', {
      scene: !!executionEnvironment.scene,
      babylonAPI: Object.keys(executionEnvironment.babylonAPI),
      hasVector3: !!(executionEnvironment.babylonAPI as Record<string, unknown>).Vector3,
      mockNodeRegistry: !!executionEnvironment.mockNodeRegistry,
      NodeCommunicationService: !!executionEnvironment.NodeCommunicationService,
      registerCleanup: !!executionEnvironment.registerCleanup
    });

    try {
      scriptFunction(
        executionEnvironment.scene, 
        executionEnvironment.babylonAPI,
        executionEnvironment.mockNodeRegistry, 
        executionEnvironment.NodeCommunicationService,
        executionEnvironment.registerCleanup
      );
      console.log('[脚本执行] 脚本执行成功');
    } catch (executeError) {
      console.error('[脚本执行] 脚本执行时发生错误:', executeError);
      
      // 提供更详细的错误信息
      let errorMessage = '脚本执行失败';
      if (executeError instanceof Error) {
        if (executeError.message.includes('BABYLON.Vector3 is not a constructor')) {
          errorMessage = 'Babylon.js Vector3 API不可用，请检查Babylon.js库加载状态';
        } else if (executeError.message.includes('BABYLON.')) {
          errorMessage = `Babylon.js API错误: ${executeError.message}`;
        } else {
          errorMessage = `脚本运行错误: ${executeError.message}`;
        }
      }
      
      throw new Error(errorMessage);
    }
  }
}

export default ScriptExecutionEngine; 