import * as fs from 'fs';
import * as path from 'path';

export interface ProjectResource {
  type: 'texture' | 'model' | 'audio' | 'shader';
  path: string;
  name: string;
  size?: number;
  description?: string;
}

export interface ProjectAPIMap {
  presets: Record<string, {
    className: string;
    importPath: string;
    publicMethods: string[];
    types: string[];
  }>;
  components: Record<string, {
    importPath: string;
    exports: string[];
  }>;
  resources: ProjectResource[];
}

/**
 * 项目资源映射器
 * 自动扫描项目结构，构建完整的资源和API映射
 */
export class ProjectResourceMapper {
  private projectRoot: string;
  private resourceMap: ProjectAPIMap;

  constructor(projectRoot: string = process.cwd()) {
    this.projectRoot = projectRoot;
    this.resourceMap = {
      presets: {},
      components: {},
      resources: []
    };
  }

  /**
   * 扫描并构建完整的项目映射
   */
  async buildProjectMap(): Promise<ProjectAPIMap> {
    await this.scanPresets();
    await this.scanComponents();
    await this.scanResources();
    return this.resourceMap;
  }

  /**
   * 扫描Babylon.js预设系统
   */
  private async scanPresets(): Promise<void> {
    const presetsDir = path.join(this.projectRoot, 'src/babylon/presets');
    if (!fs.existsSync(presetsDir)) return;

    const presetFiles = fs.readdirSync(presetsDir)
      .filter(file => file.endsWith('.ts') && !file.includes('index'));

    for (const file of presetFiles) {
      const filePath = path.join(presetsDir, file);
      const content = fs.readFileSync(filePath, 'utf-8');
      
      const className = this.extractClassName(content);
      const publicMethods = this.extractPublicMethods(content);
      const types = this.extractTypes(content);
      
      if (className) {
        const presetName = file.replace('.ts', '');
        this.resourceMap.presets[presetName] = {
          className,
          importPath: `@/src/babylon/presets/${presetName}`,
          publicMethods,
          types
        };
      }
    }
  }

  /**
   * 扫描UI组件
   */
  private async scanComponents(): Promise<void> {
    const componentsDir = path.join(this.projectRoot, 'src/babylon/ui');
    if (!fs.existsSync(componentsDir)) return;

    const componentFiles = fs.readdirSync(componentsDir)
      .filter(file => file.endsWith('.tsx') || file.endsWith('.ts'));

    for (const file of componentFiles) {
      const filePath = path.join(componentsDir, file);
      const content = fs.readFileSync(filePath, 'utf-8');
      
      const exports = this.extractExports(content);
      
      if (exports.length > 0) {
        const componentName = file.replace(/\.(tsx|ts)$/, '');
        this.resourceMap.components[componentName] = {
          importPath: `@/src/babylon/ui/${componentName}`,
          exports
        };
      }
    }
  }

  /**
   * 扫描项目资源文件
   */
  private async scanResources(): Promise<void> {
    const publicDir = path.join(this.projectRoot, 'public');
    if (!fs.existsSync(publicDir)) return;

    await this.scanDirectory(publicDir, '');
  }

  /**
   * 递归扫描目录
   */
  private async scanDirectory(dirPath: string, relativePath: string): Promise<void> {
    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const itemRelativePath = relativePath ? `${relativePath}/${item}` : item;
      
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        await this.scanDirectory(fullPath, itemRelativePath);
      } else {
        const resource = this.createResourceFromFile(fullPath, itemRelativePath);
        if (resource) {
          this.resourceMap.resources.push(resource);
        }
      }
    }
  }

  /**
   * 从文件创建资源对象
   */
  private createResourceFromFile(filePath: string, relativePath: string): ProjectResource | null {
    const ext = path.extname(filePath).toLowerCase();
    const name = path.basename(filePath, ext);
    const size = fs.statSync(filePath).size;

    let type: ProjectResource['type'] | null = null;
    
    if (['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tga'].includes(ext)) {
      type = 'texture';
    } else if (['.glb', '.gltf', '.obj', '.fbx'].includes(ext)) {
      type = 'model';
    } else if (['.mp3', '.wav', '.ogg'].includes(ext)) {
      type = 'audio';
    } else if (['.glsl', '.vert', '.frag', '.effect'].includes(ext)) {
      type = 'shader';
    }

    if (!type) return null;

    return {
      type,
      path: `/${relativePath}`,
      name,
      size,
      description: this.generateResourceDescription(type, name, relativePath)
    };
  }

  /**
   * 生成资源描述
   */
  private generateResourceDescription(type: string, name: string, path: string): string {
    const pathParts = path.split('/');
    const folder = pathParts[pathParts.length - 2] || 'root';
    
    return `${type} - ${name} (位于 ${folder} 文件夹)`;
  }

  /**
   * 提取类名
   */
  private extractClassName(content: string): string | null {
    const classMatch = content.match(/export\s+class\s+(\w+)/);
    return classMatch ? classMatch[1] : null;
  }

  /**
   * 提取公共方法
   */
  private extractPublicMethods(content: string): string[] {
    const methodMatches = content.matchAll(/static\s+(\w+)\s*\(/g);
    return Array.from(methodMatches, match => match[1]);
  }

  /**
   * 提取类型定义
   */
  private extractTypes(content: string): string[] {
    const typeMatches = content.matchAll(/export\s+(?:enum|interface|type)\s+(\w+)/g);
    return Array.from(typeMatches, match => match[1]);
  }

  /**
   * 提取导出项
   */
  private extractExports(content: string): string[] {
    const exports: string[] = [];
    
    // 函数导出
    const functionMatches = content.matchAll(/export\s+(?:const|function)\s+(\w+)/g);
    exports.push(...Array.from(functionMatches, match => match[1]));
    
    // 类导出
    const classMatches = content.matchAll(/export\s+class\s+(\w+)/g);
    exports.push(...Array.from(classMatches, match => match[1]));
    
    // 接口导出
    const interfaceMatches = content.matchAll(/export\s+interface\s+(\w+)/g);
    exports.push(...Array.from(interfaceMatches, match => match[1]));
    
    return exports;
  }

  /**
   * 生成用于Agent的上下文字符串
   */
  generateContextString(): string {
    let context = '\n## 项目完整API和资源映射\n\n';
    
    // 预设系统
    context += '### Babylon.js预设系统:\n';
    for (const [, preset] of Object.entries(this.resourceMap.presets)) {
      context += `**${preset.className}**:\n`;
      context += `- 导入: import { ${preset.className}, ${preset.types.join(', ')} } from '${preset.importPath}'\n`;
      context += `- 公共方法: ${preset.publicMethods.join(', ')}\n\n`;
    }
    
    // UI组件
    context += '### UI组件:\n';
    for (const [name, component] of Object.entries(this.resourceMap.components)) {
      context += `**${name}**:\n`;
      context += `- 导入: import { ${component.exports.join(', ')} } from '${component.importPath}'\n\n`;
    }
    
    // 资源文件
    context += '### 可用资源文件:\n';
    const resourcesByType = this.groupResourcesByType();
    
    for (const [type, resources] of Object.entries(resourcesByType)) {
      context += `**${type}类型资源**:\n`;
      for (const resource of resources) {
        context += `- ${resource.name}: "${resource.path}"\n`;
      }
      context += '\n';
    }
    
    return context;
  }

  /**
   * 按类型分组资源
   */
  private groupResourcesByType(): Record<string, ProjectResource[]> {
    return this.resourceMap.resources.reduce((groups, resource) => {
      if (!groups[resource.type]) {
        groups[resource.type] = [];
      }
      groups[resource.type].push(resource);
      return groups;
    }, {} as Record<string, ProjectResource[]>);
  }

  /**
   * 获取特定类型的资源
   */
  getResourcesByType(type: ProjectResource['type']): ProjectResource[] {
    return this.resourceMap.resources.filter(resource => resource.type === type);
  }

  /**
   * 检查资源是否存在
   */
  hasResource(path: string): boolean {
    return this.resourceMap.resources.some(resource => resource.path === path);
  }
} 