import * as THREE from 'three';

import { NodeRegistry } from './NodeRegistry';
import { nodePerformanceMonitor } from './NodePerformanceMonitor';
import { 
  GameNodeType, 
  MeshNodeProperties, 
  LightNodeProperties, 
  CameraNodeProperties,
  TransformNodeProperties
} from '../types/NodeTypes';

/**
 * 简化的节点工厂类
 * 专注于核心功能，提供稳定的节点创建和注册
 */
export class NodeFactorySimple {
  private static instance: NodeFactorySimple;
  private nodeRegistry: NodeRegistry;

  private constructor() {
    this.nodeRegistry = NodeRegistry.getInstance();
  }

  public static getInstance(): NodeFactorySimple {
    if (!NodeFactorySimple.instance) {
      NodeFactorySimple.instance = new NodeFactorySimple();
    }
    return NodeFactorySimple.instance;
  }

  /**
   * 创建基础方块Mesh
   */
  public createBoxMesh(scene: Scene, config: {
    id: string;
    name: string;
    size?: number;
    position?: Vector3;
    color?: Color3;
  }): Mesh {
    console.log(`[NodeFactory] 创建方块: ${config.name}`);

    // 开始性能监控
    nodePerformanceMonitor.startTimer(config.id);

    // 创建方块几何体
    const box = MeshBuilder.CreateBox(config.id, { size: config.size || 1 }, scene);
    
    // 设置位置
    if (config.position) {
      box.position = config.position;
    }

    // 创建材质
    if (config.color) {
      const material = new StandardMaterial(`${config.id}_material`, scene);
      material.diffuseColor = config.color;
      box.material = material;
    }

    // 注册到NodeRegistry
    const nodeProperties: MeshNodeProperties = {
      id: config.id,
      name: config.name,
      type: GameNodeType.MESH,
      visible: true,
      enabled: true,
      position: box.position,
      rotation: box.rotation,
      scaling: box.scaling,
      parent: undefined,
      children: [],
      geometry: {
        type: 'box',
        parameters: { size: config.size || 1 }
      },
      material: {
        type: 'standard',
        diffuseColor: config.color
      }
    };

    this.nodeRegistry.register(nodeProperties);

    // 结束性能监控
    nodePerformanceMonitor.endTimer(config.id, 'create');
    nodePerformanceMonitor.recordNodeCount(this.nodeRegistry.getAllNodes().length);

    console.log(`[NodeFactory] 方块创建完成: ${config.id}`);
    return box;
  }

  /**
   * 创建地面Mesh
   */
  public createGroundMesh(scene: Scene, config: {
    id: string;
    name: string;
    width?: number;
    height?: number;
    position?: Vector3;
    color?: Color3;
  }): Mesh {
    console.log(`[NodeFactory] 创建地面: ${config.name}`);

    // 创建地面几何体
    const ground = MeshBuilder.CreateGround(config.id, { 
      width: config.width || 10, 
      height: config.height || 10 
    }, scene);
    
    // 设置位置
    if (config.position) {
      ground.position = config.position;
    }

    // 创建材质
    if (config.color) {
      const material = new StandardMaterial(`${config.id}_material`, scene);
      material.diffuseColor = config.color;
      ground.material = material;
    }

    // 注册到NodeRegistry
    const nodeProperties: MeshNodeProperties = {
      id: config.id,
      name: config.name,
      type: GameNodeType.MESH,
      visible: true,
      enabled: true,
      position: ground.position,
      rotation: ground.rotation,
      scaling: ground.scaling,
      parent: undefined,
      children: [],
      geometry: {
        type: 'ground',
        parameters: { width: config.width || 10, height: config.height || 10 }
      },
      material: {
        type: 'standard',
        diffuseColor: config.color
      }
    };

    this.nodeRegistry.register(nodeProperties);

    console.log(`[NodeFactory] 地面创建完成: ${config.id}`);
    return ground;
  }

  /**
   * 创建半球光源
   */
  public createHemisphericLight(scene: Scene, config: {
    id: string;
    name: string;
    intensity?: number;
    color?: Color3;
  }): HemisphericLight {
    console.log(`[NodeFactory] 创建半球光源: ${config.name}`);

    const light = new HemisphericLight(config.id, Vector3.Up(), scene);
    
    // 设置光源属性
    if (config.intensity !== undefined) {
      light.intensity = config.intensity;
    }
    if (config.color) {
      light.diffuse = config.color;
    }

    // 注册到NodeRegistry
    const nodeProperties: LightNodeProperties = {
      id: config.id,
      name: config.name,
      type: GameNodeType.LIGHT,
      visible: true,
      enabled: true,
      position: Vector3.Zero(),
      rotation: Vector3.Zero(),
      scaling: Vector3.One(),
      parent: undefined,
      children: [],
      lightType: 'hemispheric',
      intensity: config.intensity || 1.0,
      color: config.color || Color3.White(),
      direction: undefined,
      range: undefined,
      angle: undefined,
      exponent: undefined
    };

    this.nodeRegistry.register(nodeProperties);

    console.log(`[NodeFactory] 半球光源创建完成: ${config.id}`);
    return light;
  }

  /**
   * 创建弧形旋转相机
   */
  public createArcRotateCamera(scene: Scene, config: {
    id: string;
    name: string;
    alpha?: number;
    beta?: number;
    radius?: number;
    target?: Vector3;
  }): ArcRotateCamera {
    console.log(`[NodeFactory] 创建弧形相机: ${config.name}`);

    const camera = new ArcRotateCamera(
      config.id,
      config.alpha || -Math.PI / 2,
      config.beta || Math.PI / 2.5,
      config.radius || 10,
      config.target || Vector3.Zero(),
      scene
    );

    // 注册到NodeRegistry
    const nodeProperties: CameraNodeProperties = {
      id: config.id,
      name: config.name,
      type: GameNodeType.CAMERA,
      visible: true,
      enabled: true,
      position: camera.position.clone(), // 克隆当前位置
      rotation: Vector3.Zero(),
      scaling: Vector3.One(),
      parent: undefined,
      children: [],
      cameraType: 'arc_rotate',
      fov: camera.fov,
      target: config.target || Vector3.Zero(),
      minZ: camera.minZ,
      maxZ: camera.maxZ
    };

    this.nodeRegistry.register(nodeProperties);

    console.log(`[NodeFactory] 弧形相机创建完成: ${config.id}`);
    return camera;
  }

  /**
   * 创建Transform节点
   */
  public createTransformNode(scene: Scene, config: {
    id: string;
    name: string;
    position?: Vector3;
    rotation?: Vector3;
    scaling?: Vector3;
  }): TransformNode {
    console.log(`[NodeFactory] 创建Transform节点: ${config.name}`);

    const transformNode = new TransformNode(config.id, scene);
    
    // 设置基础属性
    if (config.position) {
      transformNode.position = config.position;
    }
    if (config.rotation) {
      transformNode.rotation = config.rotation;
    }
    if (config.scaling) {
      transformNode.scaling = config.scaling;
    }

    // 注册到NodeRegistry
    const nodeProperties: TransformNodeProperties = {
      id: config.id,
      name: config.name,
      type: GameNodeType.TRANSFORM,
      visible: true,
      enabled: true,
      position: transformNode.position,
      rotation: transformNode.rotation,
      scaling: transformNode.scaling,
      parent: undefined,
      children: []
    };

    this.nodeRegistry.register(nodeProperties);

    console.log(`[NodeFactory] Transform节点创建完成: ${config.id}`);
    return transformNode;
  }

  /**
   * 创建简单的3D场景
   */
  public createBasic3DScene(scene: Scene): {
    ground: Mesh;
    playerBox: Mesh;
    light: HemisphericLight;
    camera: ArcRotateCamera;
  } {
    console.log('[NodeFactory] 创建基础3D场景...');

    // 创建地面
    const ground = this.createGroundMesh(scene, {
      id: 'ground',
      name: '地面',
      width: 20,
      height: 20,
      position: Vector3.Zero(),
      color: new Color3(0.5, 0.5, 0.5)
    });

    // 创建玩家方块
    const playerBox = this.createBoxMesh(scene, {
      id: 'player_box',
      name: '玩家方块',
      size: 2,
      position: new Vector3(0, 1, 0),
      color: new Color3(0.2, 0.6, 1.0)
    });

    // 创建光源
    const light = this.createHemisphericLight(scene, {
      id: 'main_light',
      name: '主光源',
      intensity: 1.0,
      color: Color3.White()
    });

    // 创建相机
    const camera = this.createArcRotateCamera(scene, {
      id: 'main_camera',
      name: '主摄像机',
      alpha: -Math.PI / 2,
      beta: Math.PI / 2.5,
      radius: 15,
      target: Vector3.Zero()
    });

    console.log('[NodeFactory] 基础3D场景创建完成');

    return {
      ground,
      playerBox,
      light,
      camera
    };
  }

  /**
   * 清理所有节点
   */
  public clearAllNodes(): void {
    this.nodeRegistry.clear();
    console.log('[NodeFactory] 所有节点已清理');
  }

  /**
   * 获取节点统计信息
   */
  public getNodeStats(): {
    total: number;
    byType: Record<string, number>;
  } {
    const allNodes = this.nodeRegistry.getAllNodes();
    const stats = {
      total: allNodes.length,
      byType: {} as Record<string, number>
    };

    allNodes.forEach(node => {
      const type = node.type;
      stats.byType[type] = (stats.byType[type] || 0) + 1;
    });

    return stats;
  }
}

// 导出单例实例
export const nodeFactorySimple = NodeFactorySimple.getInstance(); 