import { Vector3, Color3 } from '@babylonjs/core';
import { 
  MeshCreationConfig, 
  LightCreationConfig, 
  CameraCreationConfig, 
  NodeCreationConfig 
} from '../utils/NodeFactory';

/**
 * 游戏模板接口
 */
export interface GameTemplate {
  id: string;
  name: string;
  description: string;
  nodes: {
    meshes: MeshCreationConfig[];
    lights: LightCreationConfig[];
    cameras: CameraCreationConfig[];
    transforms: NodeCreationConfig[];
  };
  gameLogic: {
    initialization: string;
    gameLoop: string;
    eventHandlers: string;
    cleanup: string;
  };
  codeTemplate: string;
}

/**
 * 游戏模板生成器
 */
export class GameTemplateGenerator {
  
  /**
   * 生成完整的游戏组件代码
   */
  public static generateGameComponent(template: GameTemplate, gameId: string): string {
    const importsCode = GameTemplateGenerator.generateImports();
    const nodeCreationCode = GameTemplateGenerator.generateNodeCreationCode(template.nodes);
    const gameLogicCode = GameTemplateGenerator.generateGameLogicCode(template.gameLogic);
    
    return `${importsCode}

/**
 * ${template.name} - 游戏组件
 * ${template.description}
 * 游戏ID: ${gameId}
 */
export default function ${GameTemplateGenerator.toPascalCase(gameId)}() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const sceneRef = useRef<Scene | null>(null);
  const engineRef = useRef<Engine | null>(null);
  
  // 游戏状态
  const gameState = useRef({
    isPlaying: false,
    score: 0,
    level: 1,
    nodeRegistry: NodeRegistry.getInstance()
  });

  // 初始化游戏场景
  const initializeScene = useCallback(async () => {
    if (!canvasRef.current) return;

    try {
      console.log('[${template.name}] 初始化游戏场景...');
      
      // 检测WebGPU支持并创建引擎
      const webGPUSupported = await BABYLON.WebGPUEngine?.IsSupportedAsync || false;
      const engine = webGPUSupported 
        ? new BABYLON.WebGPUEngine(canvasRef.current, { 
            adaptToDeviceRatio: true,
            powerPreference: 'high-performance'
          })
        : new BABYLON.Engine(canvasRef.current, true, { 
            adaptToDeviceRatio: true,
            powerPreference: 'high-performance'
          });

      if (webGPUSupported && 'initAsync' in engine) {
        await (engine as any).initAsync();
      }

      engineRef.current = engine;
      
      // 创建场景
      const scene = new BABYLON.Scene(engine);
      sceneRef.current = scene;

      // 性能优化设置
      scene.skipPointerMovePicking = true;
      scene.autoClear = false;
      scene.autoClearDepthAndStencil = false;

      // 获取NodeFactory和NodeRegistry实例
      const nodeFactory = NodeFactory.getInstance();
      const nodeRegistry = NodeRegistry.getInstance();
      
      // 清理之前的节点数据
      nodeRegistry.clear();

${nodeCreationCode}

      // 设置默认相机控制
      if (scene.activeCamera) {
        scene.activeCamera.attachControl(canvasRef.current, true);
      }

      // 启用物理引擎（如果需要）
      try {
        const hk = new BABYLON.HavokPlugin();
        scene.enablePhysics(new BABYLON.Vector3(0, -9.81, 0), hk);
        console.log('[${template.name}] 物理引擎启用成功');
      } catch (error) {
        console.warn('[${template.name}] 物理引擎启用失败:', error);
      }

${gameLogicCode.initialization}

      // 游戏主循环
      scene.onBeforeRenderObservable.add(() => {
        if (gameState.current.isPlaying) {
${gameLogicCode.gameLoop}
        }
      });

      // 开始渲染循环
      engine.runRenderLoop(() => {
        if (scene && !scene.isDisposed) {
          scene.render();
        }
      });

      // 启动游戏
      gameState.current.isPlaying = true;
      console.log('[${template.name}] 游戏初始化完成并开始运行');

    } catch (error) {
      console.error('[${template.name}] 场景初始化失败:', error);
    }
  }, []);

${gameLogicCode.eventHandlers}

  // 组件挂载时初始化
  useEffect(() => {
    initializeScene();

    // 清理函数
    return () => {
${gameLogicCode.cleanup}
      if (sceneRef.current) {
        const nodeRegistry = NodeRegistry.getInstance();
        nodeRegistry.clear();
        sceneRef.current.dispose();
      }
      if (engineRef.current) {
        engineRef.current.dispose();
      }
    };
  }, [initializeScene]);

  return (
    <div className="w-full h-full bg-black relative">
      <canvas 
        ref={canvasRef}
        className="w-full h-full outline-none"
        tabIndex={0}
      />
      
      {/* 游戏UI覆盖层 */}
      <div className="absolute top-4 left-4 text-white z-10">
        <div className="bg-black bg-opacity-50 rounded p-2">
          <p>得分: {gameState.current.score}</p>
          <p>等级: {gameState.current.level}</p>
        </div>
      </div>
    </div>
  );
}`;
  }

  /**
   * 生成导入语句
   */
  private static generateImports(): string {
    return `import React, { useRef, useEffect, useCallback } from 'react';
import { 
  Engine, 
  WebGPUEngine, 
  Scene, 
  Vector3, 
  Color3, 
  MeshBuilder, 
  StandardMaterial,
  PBRMaterial,
  ArcRotateCamera,
  UniversalCamera,
  HemisphericLight,
  DirectionalLight,
  PointLight,
  HavokPlugin,
  PhysicsAggregate,
  PhysicsShapeType
} from '@babylonjs/core';
import * as BABYLON from '@babylonjs/core';

import { NodeFactory } from '../../../src/utils/NodeFactory';
import { NodeRegistry } from '../../../src/utils/NodeRegistry';
import { GameNodeType } from '../../../src/types/NodeTypes';`;
  }

  /**
   * 生成节点创建代码
   */
  private static generateNodeCreationCode(nodes: GameTemplate['nodes']): string {
    let code = '      // 创建游戏节点\n';
    
    // 创建Transform节点
    nodes.transforms.forEach(config => {
      code += `      nodeFactory.createTransformNode(scene, ${JSON.stringify(config, null, 8).replace(/"/g, '"')});\n`;
    });

    // 创建Mesh节点
    nodes.meshes.forEach(config => {
      code += `      nodeFactory.createMeshNode(scene, ${JSON.stringify(config, null, 8).replace(/"/g, '"')});\n`;
    });

    // 创建Light节点
    nodes.lights.forEach(config => {
      code += `      nodeFactory.createLightNode(scene, ${JSON.stringify(config, null, 8).replace(/"/g, '"')});\n`;
    });

    // 创建Camera节点
    nodes.cameras.forEach(config => {
      code += `      nodeFactory.createCameraNode(scene, ${JSON.stringify(config, null, 8).replace(/"/g, '"')});\n`;
    });

    return code;
  }

  /**
   * 生成游戏逻辑代码
   */
  private static generateGameLogicCode(gameLogic: GameTemplate['gameLogic']): {
    initialization: string;
    gameLoop: string;
    eventHandlers: string;
    cleanup: string;
  } {
    return {
      initialization: `      // 游戏初始化逻辑\n${gameLogic.initialization}`,
      gameLoop: `        // 游戏主循环逻辑\n${gameLogic.gameLoop}`,
      eventHandlers: gameLogic.eventHandlers,
      cleanup: `      // 游戏清理逻辑\n${gameLogic.cleanup}`
    };
  }

  /**
   * 转换为PascalCase
   */
  private static toPascalCase(str: string): string {
    return str.replace(/(?:^|[_-])([a-z])/g, (_, char) => char.toUpperCase())
              .replace(/[_-]/g, '');
  }
}

/**
 * 预定义游戏模板
 */
export const GAME_TEMPLATES: Record<string, GameTemplate> = {
  // 基础3D场景模板
  basic3d: {
    id: 'basic3d',
    name: '基础3D场景',
    description: '包含基本几何体、光照和相机的简单3D场景',
    nodes: {
      transforms: [],
      meshes: [
        {
          id: 'ground',
          name: '地面',
          geometryType: 'ground',
          geometryParams: { width: 20, height: 20 },
          position: new Vector3(0, 0, 0),
          materialType: 'standard',
          materialConfig: {
            diffuseColor: new Color3(0.5, 0.5, 0.5)
          }
        },
        {
          id: 'player_box',
          name: '玩家方块',
          geometryType: 'box',
          geometryParams: { size: 2 },
          position: new Vector3(0, 1, 0),
          materialType: 'pbr',
          materialConfig: {
            baseColor: new Color3(0.2, 0.6, 1.0),
            metallic: 0.1,
            roughness: 0.5
          }
        }
      ],
      lights: [
        {
          id: 'main_light',
          name: '主光源',
          lightType: 'hemispheric',
          position: new Vector3(0, 10, 0),
          intensity: 1.0,
          color: new Color3(1, 1, 1)
        }
      ],
      cameras: [
        {
          id: 'main_camera',
          name: '主摄像机',
          cameraType: 'arc_rotate',
          position: new Vector3(0, 5, -10),
          target: new Vector3(0, 0, 0),
          fov: 0.8,
          minZ: 0.1,
          maxZ: 1000
        }
      ]
    },
    gameLogic: {
      initialization: `      // 设置初始游戏状态
      gameState.current.score = 0;
      gameState.current.level = 1;`,
      gameLoop: `        // 简单的方块旋转动画
        const playerBox = scene.getMeshByName('player_box');
        if (playerBox) {
          playerBox.rotation.y += 0.02;
        }`,
      eventHandlers: `  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!sceneRef.current) return;
      
      const playerBox = sceneRef.current.getMeshByName('player_box');
      if (!playerBox) return;
      
      const moveSpeed = 0.1;
      switch (event.key.toLowerCase()) {
        case 'w':
        case 'arrowup':
          playerBox.position.z += moveSpeed;
          break;
        case 's':
        case 'arrowdown':
          playerBox.position.z -= moveSpeed;
          break;
        case 'a':
        case 'arrowleft':
          playerBox.position.x -= moveSpeed;
          break;
        case 'd':
        case 'arrowright':
          playerBox.position.x += moveSpeed;
          break;
        case ' ':
          // 跳跃
          if (playerBox.position.y <= 1.1) {
            playerBox.position.y = 3;
            setTimeout(() => {
              if (playerBox && playerBox.position.y > 1) {
                playerBox.position.y = 1;
              }
            }, 500);
          }
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);`,
      cleanup: `      console.log('[基础3D场景] 游戏清理完成');`
    },
    codeTemplate: ''
  },

  // 射击游戏模板
  shooter: {
    id: 'shooter',
    name: '射击游戏',
    description: '经典的太空射击游戏，包含玩家飞船、敌机和子弹系统',
    nodes: {
      transforms: [
        {
          id: 'game_world',
          name: '游戏世界',
          position: new Vector3(0, 0, 0)
        }
      ],
      meshes: [
        {
          id: 'player_ship',
          name: '玩家飞船',
          geometryType: 'box',
          geometryParams: { width: 1, height: 0.5, depth: 2 },
          position: new Vector3(0, 0, -5),
          materialType: 'pbr',
          materialConfig: {
            baseColor: new Color3(0.2, 0.8, 0.2),
            metallic: 0.8,
            roughness: 0.2
          },
          parent: 'game_world'
        },
        {
          id: 'background_plane',
          name: '背景',
          geometryType: 'plane',
          geometryParams: { width: 50, height: 50 },
          position: new Vector3(0, 0, 20),
          materialType: 'standard',
          materialConfig: {
            diffuseColor: new Color3(0.1, 0.1, 0.3),
            emissiveColor: new Color3(0.05, 0.05, 0.1)
          }
        }
      ],
      lights: [
        {
          id: 'main_light',
          name: '主光源',
          lightType: 'directional',
          direction: new Vector3(0, -1, 0.5),
          intensity: 1.2,
          color: new Color3(1, 1, 1)
        },
        {
          id: 'ambient_light',
          name: '环境光',
          lightType: 'hemispheric',
          intensity: 0.3,
          color: new Color3(0.5, 0.5, 1)
        }
      ],
      cameras: [
        {
          id: 'game_camera',
          name: '游戏摄像机',
          cameraType: 'universal',
          position: new Vector3(0, 8, -15),
          target: new Vector3(0, 0, 0),
          fov: 0.8,
          minZ: 0.1,
          maxZ: 100
        }
      ]
    },
    gameLogic: {
      initialization: `      // 初始化射击游戏状态
      gameState.current.score = 0;
      gameState.current.level = 1;
      gameState.current.bullets = [];
      gameState.current.enemies = [];
      gameState.current.lastEnemySpawn = Date.now();`,
      gameLoop: `        // 射击游戏主循环
        const currentTime = Date.now();
        
        // 生成敌机
        if (currentTime - gameState.current.lastEnemySpawn > 2000) {
          spawnEnemy();
          gameState.current.lastEnemySpawn = currentTime;
        }
        
        // 更新子弹
        updateBullets();
        
        // 更新敌机
        updateEnemies();
        
        // 检查碰撞
        checkCollisions();`,
      eventHandlers: `  // 射击游戏事件处理
  const spawnEnemy = useCallback(() => {
    if (!sceneRef.current) return;
    
    const enemyId = \`enemy_\${Date.now()}\`;
    const enemy = MeshBuilder.CreateBox(enemyId, { size: 1 }, sceneRef.current);
    enemy.position = new Vector3(
      (Math.random() - 0.5) * 10,
      2,
      10
    );
    
    const material = new StandardMaterial(\`\${enemyId}_mat\`, sceneRef.current);
    material.diffuseColor = new Color3(1, 0.2, 0.2);
    enemy.material = material;
    
    gameState.current.enemies.push({
      mesh: enemy,
      speed: 0.1 + Math.random() * 0.05
    });
  }, []);

  const fireBullet = useCallback(() => {
    if (!sceneRef.current) return;
    
    const playerShip = sceneRef.current.getMeshByName('player_ship');
    if (!playerShip) return;
    
    const bulletId = \`bullet_\${Date.now()}\`;
    const bullet = MeshBuilder.CreateSphere(bulletId, { diameter: 0.2 }, sceneRef.current);
    bullet.position = playerShip.position.clone();
    bullet.position.z += 1;
    
    const material = new StandardMaterial(\`\${bulletId}_mat\`, sceneRef.current);
    material.emissiveColor = new Color3(1, 1, 0);
    bullet.material = material;
    
    gameState.current.bullets.push({
      mesh: bullet,
      speed: 0.5
    });
  }, []);

  const updateBullets = useCallback(() => {
    if (!gameState.current.bullets) return;
    
    gameState.current.bullets = gameState.current.bullets.filter(bullet => {
      bullet.mesh.position.z += bullet.speed;
      
      if (bullet.mesh.position.z > 20) {
        bullet.mesh.dispose();
        return false;
      }
      return true;
    });
  }, []);

  const updateEnemies = useCallback(() => {
    if (!gameState.current.enemies) return;
    
    gameState.current.enemies = gameState.current.enemies.filter(enemy => {
      enemy.mesh.position.z -= enemy.speed;
      
      if (enemy.mesh.position.z < -10) {
        enemy.mesh.dispose();
        return false;
      }
      return true;
    });
  }, []);

  const checkCollisions = useCallback(() => {
    // 实现碰撞检测逻辑
  }, []);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!sceneRef.current) return;
      
      const playerShip = sceneRef.current.getMeshByName('player_ship');
      if (!playerShip) return;
      
      const moveSpeed = 0.2;
      switch (event.key.toLowerCase()) {
        case 'a':
        case 'arrowleft':
          if (playerShip.position.x > -8) {
            playerShip.position.x -= moveSpeed;
          }
          break;
        case 'd':
        case 'arrowright':
          if (playerShip.position.x < 8) {
            playerShip.position.x += moveSpeed;
          }
          break;
        case ' ':
          fireBullet();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [fireBullet]);`,
      cleanup: `      // 清理游戏对象
      if (gameState.current.bullets) {
        gameState.current.bullets.forEach(bullet => bullet.mesh?.dispose());
      }
      if (gameState.current.enemies) {
        gameState.current.enemies.forEach(enemy => enemy.mesh?.dispose());
      }
      console.log('[射击游戏] 游戏清理完成');`
    },
    codeTemplate: ''
  }
};

/**
 * 根据游戏类型获取最佳模板
 */
export function getBestTemplateForGameType(gameType: string): GameTemplate {
  const gameTypeLower = gameType.toLowerCase();
  
  if (gameTypeLower.includes('射击') || gameTypeLower.includes('飞机') || gameTypeLower.includes('shoot')) {
    return GAME_TEMPLATES.shooter;
  }
  
  // 默认返回基础3D场景
  return GAME_TEMPLATES.basic3d;
}

/**
 * 创建自定义游戏模板
 */
export function createCustomTemplate(
  gameType: string,
  description: string,
  nodes: GameTemplate['nodes'],
  gameLogic: Partial<GameTemplate['gameLogic']> = {}
): GameTemplate {
  return {
    id: `custom_${Date.now()}`,
    name: gameType,
    description,
    nodes,
    gameLogic: {
      initialization: gameLogic.initialization || '// 自定义初始化逻辑',
      gameLoop: gameLogic.gameLoop || '// 自定义游戏循环逻辑',
      eventHandlers: gameLogic.eventHandlers || '// 自定义事件处理',
      cleanup: gameLogic.cleanup || '// 自定义清理逻辑'
    },
    codeTemplate: ''
  };
} 