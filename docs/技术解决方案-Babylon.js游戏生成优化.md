# PlayableGen平台：Babylon.js游戏生成技术优化方案

## 问题分析

### 1. 文件结构差异问题

**现状**：
- **方式A**：Agent生成独立的 `index.html`、`game.js`、`style.css` 文件
- **方式B**：TSX组件文件如 `AirplaneGameScene.tsx`，集成在React应用中

**差异对比**：

| 特性 | 独立文件结构 | TSX组件结构 |
|------|-------------|-------------|
| **独立性** | ✅ 完全独立运行 | ❌ 依赖React环境 |
| **部署简单性** | ✅ 直接上传HTML | ❌ 需要构建过程 |
| **开发体验** | ❌ 无TypeScript支持 | ✅ 类型安全+IDE支持 |
| **代码复用** | ❌ 难以复用 | ✅ 组件化复用 |
| **Playable广告** | ✅ 符合单文件要求 | ❌ 无法生成单文件 |
| **调试便利性** | ❌ 调试困难 | ✅ React DevTools |

### 2. Babylon.js API准确性问题

**现状问题**：
- 大模型可能使用过时的API（如 `attachControls` 而非 `attachControl`）
- API覆盖不全面，缺少高级功能
- 没有版本兼容性验证机制

## 解决方案

### 1. 混合架构设计

```mermaid
graph TD
    A[用户需求] --> B[GameDesignAgent]
    B --> C[TSX模板组件开发]
    C --> D[平台内预览调试]
    D --> E[CodeGenerationAgent]
    E --> F[查询最新Babylon.js API]
    F --> G[生成独立HTML文件]
    G --> H[Playable广告输出]
    
    C --> I[组件库维护]
    I --> J[API标准化]
    J --> E
```

**核心策略**：
1. **开发阶段**：使用TSX组件进行开发和调试
2. **生成阶段**：转换为独立HTML文件输出
3. **API统一**：确保两种格式使用相同的Babylon.js API

### 2. API准确性保障机制

#### 2.1 动态API查询系统

```typescript
// 在CodeGenerationAgent中集成API查询
private async queryBabylonJSAPI(gameDesign: GameDesign): Promise<string> {
  const apiTopics = this.getRequiredAPITopics(gameDesign);
  return this.getBabylonJSBestPractices(apiTopics);
}
```

#### 2.2 基于游戏类型的API主题识别

```typescript
private getRequiredAPITopics(gameDesign: GameDesign): string[] {
  const topics = ['basic', 'camera', 'materials', 'lighting'];
  
  // 射击游戏需要物理和粒子系统
  if (gameDesign.gameType?.includes('射击')) {
    topics.push('physics', 'collision', 'particles');
  }
  
  // 3D游戏需要网格和动画
  if (gameDesign.gameType?.includes('3D')) {
    topics.push('meshes', 'animations', 'shadows');
  }
  
  return topics;
}
```

#### 2.3 Babylon.js 7.42.0 API最佳实践库

**摄像机API**：
```javascript
// ✅ 正确用法
camera.attachControl(canvas, true);

// ❌ 过时用法
camera.attachControls(canvas, true);
```

**材质API**：
```javascript
// ✅ 标准材质
const material = new BABYLON.StandardMaterial(name, scene);

// ✅ PBR材质
const pbrMaterial = new BABYLON.PBRMaterial(name, scene);
```

**网格创建API**：
```javascript
// ✅ 使用MeshBuilder
const sphere = BABYLON.MeshBuilder.CreateSphere(name, options, scene);
const box = BABYLON.MeshBuilder.CreateBox(name, options, scene);
```

### 3. 代码生成质量保障

#### 3.1 代码完整性验证

```typescript
private validateCodeCompleteness(sections: any): void {
  const issues: string[] = [];
  
  // 检查HTML完整性
  if (sections.html && !sections.html.includes('</html>')) {
    issues.push("HTML代码不完整，缺少结束标签");
  }
  
  // 检查JavaScript语法完整性
  if (sections.javascript) {
    const jsCode = sections.javascript;
    if (jsCode.trim().endsWith(',') || jsCode.trim().endsWith('if (')) {
      issues.push("JavaScript代码被截断");
    }
  }
}
```

#### 3.2 多格式代码提取

```typescript
private extractCodeSections(content: string): any {
  // 方法1: === 格式
  let htmlMatch = content.match(/=== HTML ===([\s\S]*?)(?:\n=== [A-Z]+ ===|$)/);
  
  // 方法2: Markdown代码块格式
  if (!htmlMatch) {
    htmlMatch = content.match(/```html\n?([\s\S]*?)```/);
  }
  
  // 方法3: 直接提取HTML文档
  if (!htmlMatch && content.includes('<!DOCTYPE html>')) {
    const htmlStart = content.indexOf('<!DOCTYPE html>');
    const htmlEnd = content.lastIndexOf('</html>') + 7;
    if (htmlStart !== -1 && htmlEnd > htmlStart) {
      sections.html = content.substring(htmlStart, htmlEnd);
    }
  }
}
```

## 实施计划

### 第一阶段：API准确性优化（已完成）
- [x] 集成动态API查询到CodeGenerationAgent
- [x] 建立基于游戏类型的API主题识别
- [x] 创建Babylon.js 7.42.0最佳实践库
- [x] 添加代码完整性验证机制

### 第二阶段：混合架构实现
- [ ] 创建TSX游戏模板组件库
- [ ] 建立TSX到HTML的转换系统
- [ ] 实现组件参数化配置
- [ ] 集成到PlayableGen工作流

### 第三阶段：质量保障和测试
- [ ] 建立自动化测试套件
- [ ] 实现API兼容性检查
- [ ] 创建代码质量评估指标
- [ ] 优化生成速度和准确性

## 预期效果

1. **API准确性提升**：确保生成的代码使用最新、正确的Babylon.js API
2. **开发体验改善**：通过TSX组件提供更好的开发和调试体验
3. **输出格式灵活**：既支持平台内预览，又能生成独立的Playable广告
4. **代码质量保障**：通过验证机制确保生成代码的完整性和可运行性

## 技术要点

- 使用Babylon.js 7.42.0最新API
- 支持移动端触屏和桌面端键盘控制
- 集成Inspector调试功能
- 优化渲染性能和内存使用
- 确保跨浏览器兼容性
