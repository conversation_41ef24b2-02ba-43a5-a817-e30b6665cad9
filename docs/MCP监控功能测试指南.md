# MCP监控功能测试指南

本指南将帮助你测试刚刚实现的MCP（Model Context Protocol）监控功能，包括连接池管理、性能监控、缓存机制等。

## 🚀 快速开始

### 1. 启动开发服务器

```bash
# 确保在项目根目录
cd /Users/<USER>/code/AI/PlayableAgent

# 安装依赖（如果还没有安装）
npm install

# 启动开发服务器
npm run dev
```

### 2. 访问MCP监控页面

开发服务器启动后，在浏览器中访问：
- **主页**: http://localhost:3000
- **MCP监控页面**: http://localhost:3000/mcp-monitoring

你也可以通过导航栏中的 "MCP监控" 按钮直接访问。

## 📊 监控页面功能测试

### 实时性能监控面板

页面顶部的监控面板会显示：
- **总查询数**: 累计执行的文档查询次数
- **成功率**: 查询成功的百分比
- **平均响应时间**: 查询的平均耗时
- **缓存命中率**: 从缓存获取结果的比例

**测试步骤**:
1. 页面加载后，观察监控面板是否正常显示
2. 数据会每30秒自动刷新一次
3. 点击"刷新"按钮可手动更新数据

### 文档查询测试

**功能**: 测试MCP文档查询的基本功能

**测试步骤**:
1. 在"文档查询测试"区域的输入框中输入关键词，例如：
   - `Engine Scene Camera`
   - `PhysicsAggregate HavokPlugin`
   - `Material Texture Lighting`
2. 点击"测试查询"按钮
3. 观察查询结果和响应时间

**预期结果**:
- 查询应该在几秒内完成
- 返回相关的Babylon.js文档内容
- 响应时间显示在结果上方

### 性能测试套件

**功能**: 运行完整的MCP性能测试

**测试步骤**:
1. 点击"运行完整测试"按钮
2. 观察测试进度和结果输出
3. 测试包括：
   - 连接池初始化测试
   - 文档查询性能测试
   - 缓存机制测试
   - 故障转移测试
   - 并发查询测试
   - 性能监控测试

**预期结果**:
- 所有测试应该通过（显示✅）
- 缓存测试应该显示明显的速度提升
- 并发测试成功率应该 > 80%

## 🔧 命令行测试

### 运行MCP功能测试脚本

```bash
# 在项目根目录运行
npx ts-node scripts/test-mcp-functionality.ts
```

这个脚本会执行：
- 配置验证
- 基础功能测试
- 缓存功能测试
- 并发查询测试
- 完整性能测试

### 单独运行性能测试

```bash
# 运行性能测试套件
npx ts-node -e "
import { runMCPPerformanceTests } from './src/tests/MCPPerformanceTest';
runMCPPerformanceTests().catch(console.error);
"
```

## 📈 性能指标解读

### 正常性能指标范围

- **响应时间**: 
  - 首次查询: 1-5秒（需要网络请求）
  - 缓存查询: < 50ms
- **成功率**: > 95%
- **缓存命中率**: 随使用增加而提升，最终应 > 60%

### 连接状态指标

- **已连接**: 🟢 表示MCP服务器连接正常
- **未连接**: 🔴 表示连接失败，会自动重试
- **查询次数**: 该连接处理的查询数量
- **重连次数**: 连接失败后的重试次数

## 🐛 常见问题排查

### 1. 页面无法访问

**问题**: 访问 http://localhost:3000/mcp-monitoring 显示404

**解决方案**:
```bash
# 确保开发服务器正在运行
npm run dev

# 检查是否有编译错误
# 查看终端输出是否有错误信息
```

### 2. 监控数据不显示

**问题**: 监控面板显示空白或加载中

**可能原因**:
- MCP服务器连接失败
- 网络问题
- 配置错误

**解决方案**:
```bash
# 检查MCP配置
npx ts-node -e "
import { printMCPConfig } from './src/config/mcpConfig';
printMCPConfig();
"

# 运行基础测试
npx ts-node scripts/test-mcp-functionality.ts
```

### 3. 查询总是失败

**问题**: 文档查询测试总是返回错误

**可能原因**:
- Context7 MCP服务不可用
- 网络连接问题
- API配置错误

**解决方案**:
1. 检查网络连接
2. 验证MCP服务器URL配置
3. 查看浏览器开发者工具的网络面板
4. 检查控制台错误信息

### 4. 缓存不工作

**问题**: 重复查询没有速度提升

**解决方案**:
```bash
# 检查缓存配置
# 在浏览器开发者工具中查看是否有缓存相关错误
# 尝试清空缓存后重新测试
```

## 🎯 测试检查清单

### 基础功能 ✅
- [ ] 页面正常加载
- [ ] 导航链接工作
- [ ] 监控面板显示数据
- [ ] 手动刷新功能正常

### 查询功能 ✅
- [ ] 基本查询返回结果
- [ ] 响应时间合理（< 10秒）
- [ ] 错误处理正常
- [ ] 结果格式正确

### 缓存功能 ✅
- [ ] 重复查询速度提升
- [ ] 缓存命中率统计正确
- [ ] 缓存过期机制工作

### 性能监控 ✅
- [ ] 实时数据更新
- [ ] 连接状态准确
- [ ] 性能指标计算正确
- [ ] 错误记录功能

### 测试套件 ✅
- [ ] 完整测试可以运行
- [ ] 测试结果准确
- [ ] 错误信息清晰
- [ ] 性能报告详细

## 📞 获取帮助

如果遇到问题：

1. **查看控制台**: 浏览器开发者工具 → Console
2. **检查网络**: 开发者工具 → Network 面板
3. **运行诊断**: 使用命令行测试脚本
4. **查看日志**: 检查终端输出的错误信息

## 🎉 测试完成

当所有测试通过后，你就成功验证了：
- ✅ MCP连接池管理正常工作
- ✅ 性能监控系统运行良好
- ✅ 缓存机制有效提升性能
- ✅ Web界面功能完整
- ✅ 自动化测试覆盖全面

恭喜！你的MCP监控系统已经准备就绪！🚀
