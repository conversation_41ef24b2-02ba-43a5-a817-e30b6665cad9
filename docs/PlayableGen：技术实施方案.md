# PlayableGen：技术实施方案

## 1. 概述

本文档是PlayableGen平台的技术实施方案，旨在为开发团队提供从项目初始化到V1版本发布的详细技术指引。方案基于先前确定的产品架构，核心技术栈选型为：Babylon.js作为3D渲染引擎，LangGraph作为多智能体编排框架。

## 2. 核心技术栈与环境配置

### 2.1. 环境要求

* Node.js: v22.x 或更高版本
* 包管理器: npm 或 yarn
* 版本控制: Git
* IDE: Visual Studio Code (推荐，因其强大的TypeScript支持和调试功能)

### 2.2. 项目初始化

1. 后端 (Agent Service):

* 使用 npm init 初始化一个新的Node.js项目。
* 安装核心依赖:Bashnpm install typescript @types/node ts-node\
  npm install @langchain/langgraph @langchain/openai# 根据选择的LLM和工具安装其他依赖
* 配置 tsconfig.json 以支持现代Node.js特性和模块解析。

2. 前端 (Playable Canvas UI):

* 使用Vite初始化一个React + TypeScript项目，以获得最佳的开发体验和构建性能。Bashnpm create vite\@latest playablegen-ui -- --template react-ts
* 安装核心依赖:Bashcd playablegen-ui\
  npm install @babylonjs/core @babylonjs/loaders @babylonjs/inspector

3. 代码仓库:

* 建议使用Monorepo结构（如使用npm workspaces或Turborepo）来统一管理前端和后端代码，简化依赖管理和跨项目脚本执行。

## 3. 第一阶段：核心引擎与CoderAgent MVP (1-3个月)

目标: 验证最核心的技术风险：AI Agent能否可靠地生成并执行可工作的Babylon.js游戏逻辑代码。

### 3.1. “可玩画布”基础搭建

* 在Vite项目中，创建一个React组件 PlayableCanvas.tsx。
* 此组件将包含一个 \<canvas> 元素，并使用 useEffect Hook来初始化Babylon.js的 Engine 和 Scene。
* 实现一个基础场景，包含一个地面、一个立方体、一个基础相机 (ArcRotateCamera) 和一个光源 (HemisphericLight)。这将作为所有后续AI生成内容的渲染目标。
* 集成Babylon.js Inspector 1。通过简单的代码即可激活这个强大的调试工具，为后续的人工微调和调试提供基础。

### 3.2. CoderAgent v1.0 实现 (基于LangGraph)

此阶段的CoderAgent是整个系统的核心，我们将验证其代码生成能力。

1. 定义LangGraph图:

* 创建一个只包含一个节点的简单图 2。该节点即为CoderAgent。
* 使用LangGraph的StatefulGraph来定义状态，状态中应包含uam (通用资产清单) 和 gameplay\_prompt 2。

2. 输入数据结构 (简版UAM):

* 手动创建一个JSON文件作为简化的UAM，它只包含预设的、静态的资产信息。JSON{\
   "assets": {\
     "player": { "type": "mesh", "name": "player\_box" },\
     "enemy": { "type": "mesh", "name": "enemy\_sphere" }\
   },\
   "scene": {\
     "entrypoint": "main.ts" }\
  }

3. LLM选型与提示工程:

* 模型选择: 优先选用Claude 3.7 Sonnet或OpenAI GPT-4.1 4。这些模型在长上下文处理和多语言编码（尤其是TypeScript）方面表现出色 4。我们将基于ClassEval等更接近真实世界场景的基准来持续评估模型性能，因为它衡量的是具有上下文依赖的复合代码单元的生成能力，这与我们的需求高度相关 6。
* 系统提示 (System Prompt): 为CoderAgent设计一个高度结构化的系统提示，明确指示其：

- 角色：“你是一个专业的Babylon.js游戏开发者，使用TypeScript。”
- 任务：“根据用户提供的玩法描述和UAM，编写游戏逻辑代码。”
- 约束：“只能使用UAM中定义的资产名称来引用场景中的对象，例如 scene.getMeshByName('player\_box')。”
- 输出格式：“必须只返回一个包含完整、可执行的TypeScript代码块，不要包含任何解释性文字。”

4. 代码注入与执行机制:

* CoderAgent生成的TypeScript代码字符串需要被动态执行。
* 初步方案: 后端将生成的代码发送到前端。前端接收到代码后，先使用TypeScript的转换器（如ts-morph或简单的tsc API调用）将其转换为JavaScript，然后通过 eval() 或创建一个新的 \<script> 标签来执行。
* 安全考量: eval()存在安全风险。在生产环境中，必须将此执行过程隔离在安全的沙箱环境中，例如在一个独立的Web Worker或iframe中运行，以防止恶意代码影响主应用。

### 3.3. 端到端流程验证

1. 触发: 手动调用后端LangGraph服务，传入简版UAM和玩法提示（例如：“当按下'W'键时，让名为'player\_box'的立方体向前移动”）。
2. 执行: CoderAgent调用LLM API，生成TypeScript代码。
3. 响应: 后端将生成的代码字符串返回给前端。
4. 渲染: 前端动态编译并执行代码，更新“可玩画布”中的场景。
5. 验证: 观察画布中的立方体是否按预期响应键盘输入。

此阶段的成功，证明了AI驱动代码生成这一核心假设的可行性。

## 4. 第二阶段：多智能体框架与资产流水线 (4-7个月)

目标: 实现从自然语言提示到自动化生成包含AI创建资产的静态3D场景。

### 4.1. 通用资产清单 (UAM) v1.0 Schema定义

设计一个全面的、机器可读的JSON Schema来定义UAM。这将是所有Agent之间通信的契约。

* 顶级结构: version, projectId, assets, sceneConfig, gameplay。
* assets: 一个对象，键为逻辑名称（如playerCharacter），值为资产对象。

- 资产对象: type (model, texture, sound, animation), status (pending, generating, completed, failed), source (text\_prompt, image\_url, upload), generator (meshy, tripo), url (S3存储路径), metadata (多边形数、动画列表等)。

* sceneConfig: 场景布局、灯光、相机设置。
* gameplay: 游戏玩法的自然语言描述和关键参数。

### 4.2. Agent实现与LangGraph编排

将第一阶段的单节点图扩展为一个包含多个协作Agent的有向无环图（DAG） 2。

* ProjectManagerAgent:

- 职责: 作为图的入口点 (set\_entry\_point) 2。接收用户原始输入，调用LLM将其分解为结构化的UAM和任务列表。
- 实现: 使用函数调用（Function Calling）能力，让LLM直接输出符合UAM Schema的JSON对象。

* AssetDesignerAgent:

- 职责: 生成3D模型和纹理。
- 实现:

1. 从UAM中读取状态为pending的资产任务。
2. 根据generator字段选择调用Meshy 8 或Tripo AI 11 的API。
3. API调用是异步的。Agent需发起任务创建请求，获得task\_id，然后轮询任务状态API，直到任务SUCCEEDED或FAILED 8。
4. 成功后，下载生成的模型文件（如.glb），上传到我们自己的S3桶，并将S3 URL、status更新回UAM中。

* SceneDirectorAgent:

- 职责: 搭建场景。
- 实现: 此Agent的触发条件是所有依赖的资产任务都已completed。它读取UAM，生成一段Babylon.js的场景设置代码（或一个场景描述JSON），用于初始化相机、灯光，并将所有模型加载到正确的位置。

* 条件边 (Conditional Edges):

- 使用LangGraph的add\_conditional\_edges功能来控制流程 2。例如，在ProjectManagerAgent之后，根据UAM中需要生成的资产类型，决定是调用AssetDesignerAgent还是直接进入场景搭建。

### 4.3. 云基础设施

* 对象存储: 配置一个Amazon S3桶（或兼容服务）用于存储所有生成的资产。
* 数据库:

- 使用PostgreSQL或类似的关系型数据库来存储UAM、任务状态和项目元数据。
- 设置一个向量数据库（如Pinecone, pgvector）用于知识库，存储成功的“提示-广告”对，为未来的RAG（检索增强生成）提供支持。

## 5. 第三阶段：全平台整合与V1版本 (8-12个月)

目标: 连接所有组件，交付一个功能完整的内部V1平台。

### 5.1. 完善Agent工作小组

集成剩余的Agent到LangGraph工作流中。

* AnimatorAgent: 调用Meshy Animation API 9 为模型添加基础动画。
* SoundDesignerAgent: 调用ElevenLabs的文本到音效API 15 生成游戏音效。
* QA Agent: 一个探索性的Agent，可使用多模态模型分析游戏截图或录屏，检查是否存在明显的视觉BUG或逻辑错误。
* OptimizerAgent: 在最终交付前，调用glTF-Transform等工具库对模型和纹理进行压缩，并使用Terser等工具对生成的JavaScript代码进行混淆和压缩。

### 5.2. 前端UI与API设计

构建一个用户友好的Web界面，让非技术人员也能轻松使用。

* API设计: 设计一套RESTful API，供前端与后端LangGraph服务通信。

- POST /v1/playables: 创建一个新的互动广告生成任务。请求体为用户的自然语言提示和上传的参考文件。返回一个task\_id。
- GET /v1/playables/{task\_id}: 获取任务的当前状态和UAM。
- GET /v1/playables/{task\_id}/stream: 使用WebSocket或Server-Sent Events，实时推送Agent的执行日志和状态更新。
- POST /v1/playables/{task\_id}/iterate: 用户提交迭代修改指令（如“让角色跑得快一点”）。

* 前端实现:

- 主界面: 一个简洁的输入框用于提交初始需求。
- 状态可视化: 实时展示LangGraph的任务图执行进度，提高透明度。
- 迭代交互: 一个聊天窗口，用户在此输入修改指令，调用/iterate API。
- 画布交互: 用户在Babylon.js Inspector中做的修改，应被捕获并转化为对AI的迭代指令。

### 5.3. 持久化与人机协作

* LangGraph的持久化: 利用LangGraph内置的检查点（checkpointing）功能，将每个步骤后的图状态持久化到数据库中 3。这对于处理耗时长的任务（如3D模型生成）和从故障中恢复至关重要。
* 人在回路 (Human-in-the-Loop): 在工作流的关键节点（如资产生成后、代码生成前）设置暂停点。系统可以通过API通知前端，等待用户在UI上点击“批准”后，再通过调用resume()方法继续执行工作流。Mastra AI和LangGraph都支持此类功能，这是实现高质量交付的关键。

### 5.4. 部署

* 容器化: 使用Docker将后端Agent服务和前端UI分别打包成容器。
* 编排: 使用Docker Compose进行本地开发环境的编排，使用Kubernetes进行生产环境的部署和扩展。
* CI/CD: 建立GitHub Actions工作流，实现代码提交后自动测试、构建和部署到预生产/生产环境。

## 6. 附录

### 附录A: 关键依赖项 (示例)

|      |                      |        |                    |
| ---- | -------------------- | ------ | ------------------ |
| 类别   | 库                    | 推荐版本   | 备注                 |
| 3D引擎 | @babylonjs/core      | 8.x.x  | 核心渲染引擎 18          |
| 编排   | @langchain/langgraph | latest | 多智能体工作流编排 2        |
| LLM  | @langchain/openai    | latest | 与OpenAI/Claude模型交互 |
| 前端框架 | react                | 18.x.x | UI构建               |
| 构建工具 | vite                 | 5.x.x  | 前端开发服务器与打包         |
| 语言   | typescript           | 5.x.x  | 保证类型安全             |

***

本技术实施方案提供了一个分阶段、可落地的开发路径。团队应在每个阶段结束时进行复盘，根据实际遇到的问题和挑战，灵活调整后续阶段的计划。


