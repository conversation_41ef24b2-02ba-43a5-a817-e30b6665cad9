# Babylon.js 游戏循环最佳实践

## 问题背景

在使用 Babylon.js 开发 React 游戏组件时，经常会遇到以下错误：

1. **requestAnimationFrame 错误**：`Failed to execute 'requestAnimationFrame' on 'Window': parameter 1 is not of type 'Function'`
2. **React 无限循环错误**：`Maximum update depth exceeded`

## 根本原因分析

### 1. requestAnimationFrame 错误
- **错误原因**：Babylon.js 的 `engine.runRenderLoop()` 内部使用 `requestAnimationFrame`，但传递的参数不是函数
- **触发条件**：当传递给 `runRenderLoop` 的回调函数不是有效函数时
- **常见场景**：在 React 组件中错误地使用了 `requestAnimationFrame` 与 Babylon.js 的渲染循环

### 2. React 无限循环错误
- **错误原因**：在游戏循环中频繁调用 `setState`，导致组件重新渲染
- **触发条件**：`useCallback` 的依赖项包含会频繁变化的状态（如 `gameState`）
- **常见场景**：每帧都更新 React 状态，导致组件重新渲染和 `useCallback` 重新创建

## 正确的解决方案

### 1. 使用 Babylon.js 的 onBeforeRenderObservable

**❌ 错误做法**：
```typescript
// 错误：同时使用 engine.runRenderLoop 和 requestAnimationFrame
const gameLoop = useCallback(() => {
  // 游戏逻辑
  setGameState(prev => ({ ...prev, score: prev.score + 1 }));
  animationFrameRef.current = requestAnimationFrame(gameLoop);
}, [gameState]); // 依赖gameState导致无限循环

engine.runRenderLoop(() => {
  scene.render();
});
```

**✅ 正确做法**：
```typescript
// 正确：使用 scene.onBeforeRenderObservable
const gameLoop = useCallback(() => {
  // 游戏逻辑，直接修改 ref 而不是 state
  gameStateRef.current.score += 1;
}, []); // 空依赖数组

// 在场景初始化时注册
scene.onBeforeRenderObservable.add(gameLoop);

engine.runRenderLoop(() => {
  scene.render();
});
```

### 2. 使用 useRef 存储游戏状态

**❌ 错误做法**：
```typescript
const [gameState, setGameState] = useState({
  score: 0,
  isRunning: false
});

const gameLoop = useCallback(() => {
  setGameState(prev => ({ ...prev, score: prev.score + 1 }));
}, [gameState]); // 每次状态变化都会重新创建函数
```

**✅ 正确做法**：
```typescript
// 使用 useRef 存储游戏状态
const gameStateRef = useRef({
  score: 0,
  isRunning: false
});

// 使用单独的 state 用于 UI 显示
const [displayState, setDisplayState] = useState({
  score: 0,
  isRunning: false
});

// 定期更新显示状态
useEffect(() => {
  const interval = setInterval(() => {
    setDisplayState({ ...gameStateRef.current });
  }, 100); // 每100ms更新一次UI
  return () => clearInterval(interval);
}, []);

const gameLoop = useCallback(() => {
  // 直接修改 ref，不触发重新渲染
  gameStateRef.current.score += 1;
}, []); // 空依赖数组
```

### 3. 正确的 Babylon.js 相机设置

**❌ 错误做法**：
```typescript
camera.attachToCanvas(canvas, true); // 方法不存在
camera.attachControls(canvas, true); // 方法名错误
```

**✅ 正确做法**：
```typescript
camera.attachControl(canvas, true); // 正确的方法名
```

## 完整的最佳实践模板

```typescript
import React, { useRef, useEffect, useState, useCallback } from 'react';
import * as BABYLON from '@babylonjs/core';

const BabylonGameComponent: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const engineRef = useRef<BABYLON.Engine | null>(null);
  const sceneRef = useRef<BABYLON.Scene | null>(null);
  
  // 游戏状态使用 useRef 存储
  const gameStateRef = useRef({
    score: 0,
    isRunning: false,
    speed: 0.1
  });

  // UI 显示状态
  const [displayState, setDisplayState] = useState({
    score: 0,
    isRunning: false,
    speed: 0.1
  });

  // 定期更新显示状态
  useEffect(() => {
    const interval = setInterval(() => {
      setDisplayState({ ...gameStateRef.current });
    }, 100);
    return () => clearInterval(interval);
  }, []);

  // 游戏循环逻辑
  const gameLoop = useCallback(() => {
    const gameState = gameStateRef.current;
    if (!gameState.isRunning) return;

    // 更新游戏状态（直接修改 ref）
    gameState.score += Math.floor(gameState.speed * 10);
    gameState.speed = Math.min(gameState.speed + 0.001, 0.8);
    
    // 其他游戏逻辑...
  }, []); // 空依赖数组

  // 初始化场景
  const initializeScene = useCallback(async () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // 创建引擎
    const engine = new BABYLON.Engine(canvas, true);
    engineRef.current = engine;

    // 创建场景
    const scene = new BABYLON.Scene(engine);
    sceneRef.current = scene;

    // 创建相机
    const camera = new BABYLON.ArcRotateCamera("camera", -Math.PI / 2, Math.PI / 2.5, 15, BABYLON.Vector3.Zero(), scene);
    camera.setTarget(BABYLON.Vector3.Zero());
    camera.attachControl(canvas, true);

    // 创建光源
    const light = new BABYLON.HemisphericLight("light", new BABYLON.Vector3(0, 1, 0), scene);
    light.intensity = 0.8;

    // 注册游戏循环到 Babylon.js 的渲染循环
    scene.onBeforeRenderObservable.add(gameLoop);

    // 启动渲染循环
    engine.runRenderLoop(() => {
      scene.render();
    });

    // 处理窗口大小变化
    window.addEventListener('resize', () => {
      engine.resize();
    });

    // 启动游戏
    gameStateRef.current.isRunning = true;
  }, [gameLoop]);

  // 组件挂载时初始化
  useEffect(() => {
    initializeScene();

    return () => {
      if (sceneRef.current) {
        sceneRef.current.dispose();
      }
      if (engineRef.current) {
        engineRef.current.dispose();
      }
    };
  }, [initializeScene]);

  return (
    <div style={{ width: '100%', height: '100vh', position: 'relative' }}>
      <canvas 
        ref={canvasRef} 
        style={{ width: '100%', height: '100%', display: 'block' }}
      />
      
      {/* UI 显示 */}
      <div style={{ position: 'absolute', top: '20px', left: '20px', color: 'white' }}>
        <div>分数: {displayState.score}</div>
        <div>速度: {displayState.speed.toFixed(2)}</div>
      </div>
    </div>
  );
};

export default BabylonGameComponent;
```

## 核心原则总结

1. **分离关注点**：游戏逻辑使用 `useRef`，UI 显示使用 `useState`
2. **避免频繁更新**：游戏循环中不要调用 `setState`
3. **使用正确的 API**：使用 `scene.onBeforeRenderObservable` 而不是 `requestAnimationFrame`
4. **最小化依赖**：`useCallback` 的依赖数组应该尽可能小
5. **正确的生命周期管理**：在组件卸载时正确清理资源

## 常见错误和解决方案

| 错误类型 | 错误表现 | 解决方案 |
|---------|---------|---------|
| requestAnimationFrame 错误 | `parameter 1 is not of type 'Function'` | 使用 `scene.onBeforeRenderObservable` 代替 `requestAnimationFrame` |
| React 无限循环 | `Maximum update depth exceeded` | 使用 `useRef` 存储游戏状态，避免在游戏循环中调用 `setState` |
| 性能问题 | 帧率下降，UI 卡顿 | 减少 UI 更新频率，使用定时器定期更新显示状态 |
| 内存泄漏 | 组件卸载后仍在运行 | 在 `useEffect` 的清理函数中正确处理资源释放 |

这个最佳实践模板可以有效避免 Babylon.js 与 React 集成时的常见问题，确保游戏循环的稳定性和性能。 