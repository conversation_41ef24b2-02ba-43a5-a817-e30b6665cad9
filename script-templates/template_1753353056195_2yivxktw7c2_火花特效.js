/**
 * 脚本模板文件
 * 
 * 模板ID: template_1753353056195_2yivxktw7c2
 * 模板名称: 火花特效
 * 创建时间: 2025/7/24 18:30:56
 * 功能类型: animation
 * 目标节点类型: mesh
 * 描述: 场景节点脚本
 * 
 * 使用说明：
 * 1. 复制此模板代码到新脚本中
 * 2. 根据需要修改参数和逻辑
 * 3. 测试脚本功能
 */

/**
 * 自动生成的脚本文件
 * 
 * 脚本ID: script_1753341800987
 * 脚本名称: 火花特效
 * 生成时间: 2025/7/24 15:23:21
 * 最后更新: 2025/7/24 15:28:05
 * 功能类型: unknown
 * 目标节点: mesh
 * 依赖项: @babylonjs/core
 * 描述: 在场景中播放火花的粒子特效
 */

function executeScript(scene) {
  try {
    // 创建粒子系统
    const particleSystem = new BABYLON.ParticleSystem("sparkParticles", 2000, scene);
    
    // 设置粒子纹理 - 使用内置的火花纹理
    particleSystem.particleTexture = new BABYLON.Texture("https://playground.babylonjs.com/textures/flare.png", scene);
    
    // 创建发射器位置
    const emitter = BABYLON.MeshBuilder.CreateBox("emitter", {size: 0.1}, scene);
    emitter.position = new BABYLON.Vector3(0, 2, 0);
    emitter.isVisible = false;
    particleSystem.emitter = emitter;
    
    // 设置粒子发射区域
    particleSystem.minEmitBox = new BABYLON.Vector3(-0.5, 0, -0.5);
    particleSystem.maxEmitBox = new BABYLON.Vector3(0.5, 0, 0.5);
    
    // 设置粒子颜色
    particleSystem.color1 = new BABYLON.Color4(1, 0.8, 0, 1.0); // 金黄色
    particleSystem.color2 = new BABYLON.Color4(1, 0.4, 0, 1.0); // 橙色
    particleSystem.colorDead = new BABYLON.Color4(0.5, 0.1, 0, 0.0); // 消失时的颜色
    
    // 设置粒子大小
    particleSystem.minSize = 0.1;
    particleSystem.maxSize = 0.3;
    
    // 设置粒子生命周期
    particleSystem.minLifeTime = 0.3;
    particleSystem.maxLifeTime = 1.5;
    
    // 设置发射速率
    particleSystem.emitRate = 1500;
    
    // 设置粒子速度和方向
    particleSystem.direction1 = new BABYLON.Vector3(-2, 8, -2);
    particleSystem.direction2 = new BABYLON.Vector3(2, 8, 2);
    
    // 设置粒子初始速度
    particleSystem.minEmitPower = 1;
    particleSystem.maxEmitPower = 3;
    particleSystem.updateSpeed = 0.005;
    
    // 设置重力
    particleSystem.gravity = new BABYLON.Vector3(0, -9.81, 0);
    
    // 设置混合模式为加法混合，产生发光效果
    particleSystem.blendMode = BABYLON.ParticleSystem.BLENDMODE_ONEONE;
    
    // 启动粒子系统
    particleSystem.start();
    
    // 添加键盘控制 - 按空格键重新播放特效
    const canvas = scene.getEngine().getRenderingCanvas();
    if (canvas) {
      canvas.focus();
    }
    
    const handleKeyDown = (event) => {
      if (event.code === 'Space') {
        event.preventDefault();
        // 重新启动粒子特效
        particleSystem.stop();
        setTimeout(() => {
          particleSystem.start();
        }, 100);
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    if (canvas) {
      canvas.addEventListener('keydown', handleKeyDown);
    }
    
    // 自动循环播放 - 每3秒重新播放一次
    const intervalId = setInterval(() => {
      particleSystem.stop();
      setTimeout(() => {
        particleSystem.start();
      }, 200);
    }, 3000);
    
    // 注册清理函数
    if (typeof registerCleanup === 'function') {
      registerCleanup(() => {
        document.removeEventListener('keydown', handleKeyDown);
        if (canvas) {
          canvas.removeEventListener('keydown', handleKeyDown);
        }
        clearInterval(intervalId);
        particleSystem.dispose();
        emitter.dispose();
        console.log('火花粒子特效清理完成');
      });
    }
    
    console.log('火花粒子特效已启动，按空格键重新播放');
    
  } catch (error) {
    console.error('火花粒子特效脚本执行失败:', error);
  }
}