{"ver": "2.3.13", "importer": "fbx", "imported": true, "uuid": "3645271b-68e6-4b2c-a368-b35f15102291", "files": ["__original-animation-0.cconb"], "subMetas": {"34611": {"importer": "gltf-mesh", "uuid": "3645271b-68e6-4b2c-a368-b35f15102291@34611", "displayName": "", "id": "34611", "name": "3d66-Editable_Poly-23233528-002.mesh", "userData": {"gltfIndex": 1, "triangleCount": 36}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "73451": {"importer": "gltf-mesh", "uuid": "3645271b-68e6-4b2c-a368-b35f15102291@73451", "displayName": "", "id": "73451", "name": "3d66-Editable_Poly-23233528-003.mesh", "userData": {"gltfIndex": 2, "triangleCount": 68}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "74610": {"importer": "gltf-mesh", "uuid": "3645271b-68e6-4b2c-a368-b35f15102291@74610", "displayName": "", "id": "74610", "name": "3d66-Editable_Poly-23233528-010.mesh", "userData": {"gltfIndex": 9, "triangleCount": 206}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "4e2b5": {"importer": "gltf-mesh", "uuid": "3645271b-68e6-4b2c-a368-b35f15102291@4e2b5", "displayName": "", "id": "4e2b5", "name": "3d66-Editable_Poly-23233528-001.mesh", "userData": {"gltfIndex": 0, "triangleCount": 36}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "4a091": {"importer": "gltf-mesh", "uuid": "3645271b-68e6-4b2c-a368-b35f15102291@4a091", "displayName": "", "id": "4a091", "name": "3d66-Editable_Poly-23233528-004.mesh", "userData": {"gltfIndex": 3, "triangleCount": 54}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "3c4f1": {"importer": "gltf-mesh", "uuid": "3645271b-68e6-4b2c-a368-b35f15102291@3c4f1", "displayName": "", "id": "3c4f1", "name": "3d66-Editable_Poly-23233528-005.mesh", "userData": {"gltfIndex": 4, "triangleCount": 54}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "c1037": {"importer": "gltf-mesh", "uuid": "3645271b-68e6-4b2c-a368-b35f15102291@c1037", "displayName": "", "id": "c1037", "name": "3d66-Editable_Poly-23233528-006.mesh", "userData": {"gltfIndex": 5, "triangleCount": 56}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "da047": {"importer": "gltf-mesh", "uuid": "3645271b-68e6-4b2c-a368-b35f15102291@da047", "displayName": "", "id": "da047", "name": "3d66-Editable_Poly-23233528-007.mesh", "userData": {"gltfIndex": 6, "triangleCount": 316}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "45f94": {"importer": "gltf-mesh", "uuid": "3645271b-68e6-4b2c-a368-b35f15102291@45f94", "displayName": "", "id": "45f94", "name": "3d66-Editable_Poly-23233528-008.mesh", "userData": {"gltfIndex": 7, "triangleCount": 598}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "ac853": {"importer": "gltf-mesh", "uuid": "3645271b-68e6-4b2c-a368-b35f15102291@ac853", "displayName": "", "id": "ac853", "name": "3d66-Editable_Poly-23233528-009.mesh", "userData": {"gltfIndex": 8, "triangleCount": 1438}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "8bf5d": {"importer": "gltf-mesh", "uuid": "3645271b-68e6-4b2c-a368-b35f15102291@8bf5d", "displayName": "", "id": "8bf5d", "name": "3d66-Editable_Poly-23233528-011.mesh", "userData": {"gltfIndex": 10, "triangleCount": 212}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "73b7f": {"importer": "gltf-animation", "uuid": "3645271b-68e6-4b2c-a368-b35f15102291@73b7f", "displayName": "", "id": "73b7f", "name": "Take 001.animation", "userData": {"gltfIndex": 0, "wrapMode": 2, "sample": 30, "span": {"from": 0, "to": 3.3333332538604736}, "events": []}, "ver": "1.0.17", "imported": true, "files": [".cconb"], "subMetas": {}}, "f22b1": {"importer": "gltf-material", "uuid": "3645271b-68e6-4b2c-a368-b35f15102291@f22b1", "displayName": "", "id": "f22b1", "name": "Material #31.material", "userData": {"gltfIndex": 0}, "ver": "1.0.14", "imported": true, "files": [".json"], "subMetas": {}}, "71fd8": {"importer": "gltf-scene", "uuid": "3645271b-68e6-4b2c-a368-b35f15102291@71fd8", "displayName": "", "id": "71fd8", "name": "shitou.prefab", "userData": {"gltfIndex": 0}, "ver": "1.0.14", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"imageMetas": [], "fbx": {"smartMaterialEnabled": true}, "animationImportSettings": [{"name": "Take 001", "duration": 3.3333332538604736, "fps": 30, "splits": [{"name": "Take 001", "from": 0, "to": 3.3333332538604736, "wrapMode": 2, "previousId": "73b7f"}]}], "lods": {"enable": false, "hasBuiltinLOD": false, "options": [{"screenRatio": 0.25, "faceCount": 1}, {"screenRatio": 0.125, "faceCount": 0.25}, {"screenRatio": 0.01, "faceCount": 0.1}]}, "assetFinder": {"meshes": ["3645271b-68e6-4b2c-a368-b35f15102291@4e2b5", "3645271b-68e6-4b2c-a368-b35f15102291@34611", "3645271b-68e6-4b2c-a368-b35f15102291@73451", "3645271b-68e6-4b2c-a368-b35f15102291@4a091", "3645271b-68e6-4b2c-a368-b35f15102291@3c4f1", "3645271b-68e6-4b2c-a368-b35f15102291@c1037", "3645271b-68e6-4b2c-a368-b35f15102291@da047", "3645271b-68e6-4b2c-a368-b35f15102291@45f94", "3645271b-68e6-4b2c-a368-b35f15102291@ac853", "3645271b-68e6-4b2c-a368-b35f15102291@74610", "3645271b-68e6-4b2c-a368-b35f15102291@8bf5d"], "skeletons": [], "textures": [], "materials": ["3645271b-68e6-4b2c-a368-b35f15102291@f22b1"], "scenes": ["3645271b-68e6-4b2c-a368-b35f15102291@71fd8"]}, "materials": {"3645271b-68e6-4b2c-a368-b35f15102291@f22b1": {"__type__": "cc.Material", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "_effectAsset": {"__uuid__": "c8f66d17-351a-48da-a12c-0212d28575c4", "__expectedType__": "cc.EffectAsset"}, "_techIdx": 0, "_defines": [{"USE_ALBEDO_MAP": true}, {}, {}, {}, {}, {}], "_states": [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], "_props": [{"mainTexture": {"__uuid__": "2ca4cba0-c91d-4d37-b38c-88315e3f614b@6c48a", "__expectedType__": "cc.Texture2D"}}, {}, {}, {}, {}, {}]}}, "tangents": 1, "meshCompress": {"enable": true, "quantize": true, "compress": true}, "meshSimplify": {"enable": true, "lockBoundary": true, "autoErrorRate": true, "targetRatio": 0.26}}}