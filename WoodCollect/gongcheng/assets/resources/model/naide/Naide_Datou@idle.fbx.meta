{"ver": "2.3.13", "importer": "fbx", "imported": true, "uuid": "e0882514-1ac8-4c1e-9471-4c2a12f23f26", "files": ["__original-animation-0.cconb"], "subMetas": {"1f586": {"importer": "gltf-animation", "uuid": "e0882514-1ac8-4c1e-9471-4c2a12f23f26@1f586", "displayName": "", "id": "1f586", "name": "idle.animation", "userData": {"gltfIndex": 0, "wrapMode": 2, "sample": 30, "span": {"from": 0, "to": 2}, "events": []}, "ver": "1.0.17", "imported": true, "files": [".cconb"], "subMetas": {}}, "8c9f5": {"importer": "gltf-scene", "uuid": "e0882514-1ac8-4c1e-9471-4c2a12f23f26@8c9f5", "displayName": "", "id": "8c9f5", "name": "<PERSON><PERSON>_<PERSON>@idle.prefab", "userData": {"gltfIndex": 0}, "ver": "1.0.14", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"imageMetas": [], "fbx": {"smartMaterialEnabled": true}, "animationImportSettings": [{"name": "Take 001", "duration": 2, "fps": 30, "splits": [{"name": "idle", "from": 0, "to": 2, "wrapMode": 2, "previousId": "1f586"}]}], "lods": {"enable": false, "hasBuiltinLOD": false, "options": [{"screenRatio": 0.25, "faceCount": 1}, {"screenRatio": 0.125, "faceCount": 0.25}, {"screenRatio": 0.01, "faceCount": 0.1}]}, "assetFinder": {"meshes": [], "skeletons": [], "textures": [], "materials": [], "scenes": ["e0882514-1ac8-4c1e-9471-4c2a12f23f26@8c9f5"]}}}