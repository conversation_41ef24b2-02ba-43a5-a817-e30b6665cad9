{"ver": "2.3.13", "importer": "fbx", "imported": true, "uuid": "7f3e0202-3623-44e4-8a53-a500f62c67ab", "files": ["__original-animation-0.cconb"], "subMetas": {"a3bcc": {"importer": "gltf-animation", "uuid": "7f3e0202-3623-44e4-8a53-a500f62c67ab@a3bcc", "displayName": "", "id": "a3bcc", "name": "PickUp.animation", "userData": {"gltfIndex": 0, "wrapMode": 2, "sample": 30, "span": {"from": 0, "to": 0.8333333134651184}, "events": [], "speed": 0.7}, "ver": "1.0.17", "imported": true, "files": [".cconb"], "subMetas": {}}, "9e45f": {"importer": "gltf-scene", "uuid": "7f3e0202-3623-44e4-8a53-a500f62c67ab@9e45f", "displayName": "", "id": "9e45f", "name": "<PERSON><PERSON>_<PERSON>@PickUp.prefab", "userData": {"gltfIndex": 0}, "ver": "1.0.14", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"imageMetas": [], "fbx": {"smartMaterialEnabled": true}, "animationImportSettings": [{"name": "Take 001", "duration": 0.8333333134651184, "fps": 30, "splits": [{"name": "PickUp", "from": 0, "to": 0.8333333134651184, "wrapMode": 2, "previousId": "a3bcc", "speed": 0.7}]}], "lods": {"enable": false, "hasBuiltinLOD": false, "options": [{"screenRatio": 0.25, "faceCount": 1}, {"screenRatio": 0.125, "faceCount": 0.25}, {"screenRatio": 0.01, "faceCount": 0.1}]}, "assetFinder": {"meshes": [], "skeletons": [], "textures": [], "materials": [], "scenes": ["7f3e0202-3623-44e4-8a53-a500f62c67ab@9e45f"]}}}