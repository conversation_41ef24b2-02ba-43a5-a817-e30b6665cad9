{"ver": "2.3.13", "importer": "fbx", "imported": true, "uuid": "2e8f7c9b-1173-41bc-93c4-7cd9fcddd630", "files": ["__original-animation-0.cconb"], "subMetas": {"77ccf": {"importer": "gltf-animation", "uuid": "2e8f7c9b-1173-41bc-93c4-7cd9fcddd630@77ccf", "displayName": "", "id": "77ccf", "name": "HoldingIdle.animation", "userData": {"gltfIndex": 0, "wrapMode": 2, "sample": 30, "span": {"from": 0, "to": 1}, "events": []}, "ver": "1.0.17", "imported": true, "files": [".cconb"], "subMetas": {}}, "acbc1": {"importer": "gltf-scene", "uuid": "2e8f7c9b-1173-41bc-93c4-7cd9fcddd630@acbc1", "displayName": "", "id": "acbc1", "name": "<PERSON><PERSON>_<PERSON><PERSON>@HoldingIdle.prefab", "userData": {"gltfIndex": 0}, "ver": "1.0.14", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"imageMetas": [], "fbx": {"smartMaterialEnabled": true}, "animationImportSettings": [{"name": "Take 001", "duration": 1, "fps": 30, "splits": [{"name": "HoldingIdle", "from": 0, "to": 1, "wrapMode": 2, "previousId": "77ccf"}]}], "lods": {"enable": false, "hasBuiltinLOD": false, "options": [{"screenRatio": 0.25, "faceCount": 1}, {"screenRatio": 0.125, "faceCount": 0.25}, {"screenRatio": 0.01, "faceCount": 0.1}]}, "assetFinder": {"meshes": [], "skeletons": [], "textures": [], "materials": [], "scenes": ["2e8f7c9b-1173-41bc-93c4-7cd9fcddd630@acbc1"]}}}