{"ver": "2.3.13", "importer": "fbx", "imported": true, "uuid": "eea936b4-e057-4492-b741-67a05434031f", "files": ["__original-animation-0.cconb"], "subMetas": {"946ae": {"importer": "gltf-animation", "uuid": "eea936b4-e057-4492-b741-67a05434031f@946ae", "displayName": "", "id": "946ae", "name": "HoldingWalk.animation", "userData": {"gltfIndex": 0, "wrapMode": 2, "sample": 30, "span": {"from": 0, "to": 0.9333333373069763}, "events": [], "speed": 2}, "ver": "1.0.17", "imported": true, "files": [".cconb"], "subMetas": {}}, "1b69d": {"importer": "gltf-scene", "uuid": "eea936b4-e057-4492-b741-67a05434031f@1b69d", "displayName": "", "id": "1b69d", "name": "<PERSON><PERSON>_<PERSON><PERSON>@HoldingWalk.prefab", "userData": {"gltfIndex": 0}, "ver": "1.0.14", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"imageMetas": [], "fbx": {"smartMaterialEnabled": true}, "animationImportSettings": [{"name": "Take 001", "duration": 0.9333333373069763, "fps": 30, "splits": [{"name": "HoldingWalk", "from": 0, "to": 0.9333333373069763, "wrapMode": 2, "previousId": "946ae", "speed": 2}]}], "lods": {"enable": false, "hasBuiltinLOD": false, "options": [{"screenRatio": 0.25, "faceCount": 1}, {"screenRatio": 0.125, "faceCount": 0.25}, {"screenRatio": 0.01, "faceCount": 0.1}]}, "assetFinder": {"meshes": [], "skeletons": [], "textures": [], "materials": [], "scenes": ["eea936b4-e057-4492-b741-67a05434031f@1b69d"]}}}