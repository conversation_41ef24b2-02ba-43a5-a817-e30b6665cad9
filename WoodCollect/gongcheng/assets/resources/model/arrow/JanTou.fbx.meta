{"ver": "2.3.13", "importer": "fbx", "imported": true, "uuid": "19e44a3d-4a34-439d-b6a6-1bda5d838aab", "files": [], "subMetas": {"e22fe": {"importer": "gltf-mesh", "uuid": "19e44a3d-4a34-439d-b6a6-1bda5d838aab@e22fe", "displayName": "", "id": "e22fe", "name": "pPlane1.mesh", "userData": {"gltfIndex": 0, "triangleCount": 512}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "8d883": {"importer": "gltf-material", "uuid": "19e44a3d-4a34-439d-b6a6-1bda5d838aab@8d883", "displayName": "", "id": "8d883", "name": "lambert1.material", "userData": {"gltfIndex": 0}, "ver": "1.0.14", "imported": true, "files": [".json"], "subMetas": {}}, "09925": {"importer": "gltf-scene", "uuid": "19e44a3d-4a34-439d-b6a6-1bda5d838aab@09925", "displayName": "", "id": "09925", "name": "JanTou.prefab", "userData": {"gltfIndex": 0}, "ver": "1.0.14", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"imageMetas": [], "fbx": {"smartMaterialEnabled": true}, "lods": {"enable": false, "hasBuiltinLOD": false, "options": [{"screenRatio": 0.25, "faceCount": 1}, {"screenRatio": 0.125, "faceCount": 0.25}, {"screenRatio": 0.01, "faceCount": 0.1}]}, "assetFinder": {"meshes": ["19e44a3d-4a34-439d-b6a6-1bda5d838aab@e22fe"], "skeletons": [], "textures": [], "materials": ["19e44a3d-4a34-439d-b6a6-1bda5d838aab@8d883"], "scenes": ["19e44a3d-4a34-439d-b6a6-1bda5d838aab@09925"]}, "materials": {"19e44a3d-4a34-439d-b6a6-1bda5d838aab@8d883": {"__type__": "cc.Material", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "_effectAsset": {"__uuid__": "9b20a514-6cc3-49de-b216-b6b863046249", "__expectedType__": "cc.EffectAsset"}, "_techIdx": 0, "_defines": [{"USE_OUTLINE_PASS": true}, {"USE_BASE_COLOR_MAP": true, "USE_EMISSIVE_MAP": true, "USE_ALPHA_TEST": true, "ALPHA_TEST_CHANNEL": "r", "BASE_COLOR_MAP_AS_SHADE_MAP_1": true, "BASE_COLOR_MAP_AS_SHADE_MAP_2": true}, {}, {}, {}, {}, {}], "_states": [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], "_props": [{"lineWidth": 0.2}, {"mainColor": {"__type__": "cc.Color", "r": 105, "g": 201, "b": 255, "a": 255}, "colorScale": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "alphaThreshold": -2.1, "shadeColor1": {"__type__": "cc.Color", "r": 25, "g": 255, "b": 255, "a": 255}, "shadeColor2": {"__type__": "cc.Color", "r": 255, "g": 235, "b": 0, "a": 255}, "specular": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "baseStep": 0.75}, {}, {}, {}, {}, {}]}}, "tangents": 1, "meshCompress": {"enable": true, "quantize": true, "compress": true}, "meshSimplify": {"enable": true, "lockBoundary": true, "targetRatio": 0.5, "autoErrorRate": true}}}