{"ver": "2.3.13", "importer": "fbx", "imported": true, "uuid": "eb03526d-b64e-4d6b-a73b-1df4fb68999f", "files": [], "subMetas": {"faac9": {"importer": "gltf-mesh", "uuid": "eb03526d-b64e-4d6b-a73b-1df4fb68999f@faac9", "displayName": "", "id": "faac9", "name": "网格.001.mesh", "userData": {"gltfIndex": 0, "triangleCount": 742}, "ver": "1.1.1", "imported": true, "files": [".bin", ".json"], "subMetas": {}}, "48b02": {"importer": "gltf-embeded-image", "uuid": "eb03526d-b64e-4d6b-a73b-1df4fb68999f@48b02", "displayName": "", "id": "48b02", "name": "base_color_texture.image", "userData": {"gltfIndex": 0, "fixAlphaTransparencyArtifacts": true, "hasAlpha": true, "type": "texture"}, "ver": "1.0.3", "imported": true, "files": [".json", ".png"], "subMetas": {}}, "7d6ab": {"importer": "texture", "uuid": "eb03526d-b64e-4d6b-a73b-1df4fb68999f@7d6ab", "displayName": "", "id": "7d6ab", "name": "base_color_texture.texture", "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "isUuid": true, "imageUuidOrDatabaseUri": "eb03526d-b64e-4d6b-a73b-1df4fb68999f@48b02"}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "22e2a": {"importer": "gltf-material", "uuid": "eb03526d-b64e-4d6b-a73b-1df4fb68999f@22e2a", "displayName": "", "id": "22e2a", "name": "材质.material", "userData": {"gltfIndex": 0}, "ver": "1.0.14", "imported": true, "files": [".json"], "subMetas": {}}, "82caa": {"importer": "gltf-scene", "uuid": "eb03526d-b64e-4d6b-a73b-1df4fb68999f@82caa", "displayName": "", "id": "82caa", "name": "HuoChong.prefab", "userData": {"gltfIndex": 0}, "ver": "1.0.14", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"imageMetas": [{"name": "base_color_texture", "uri": "eb03526d-b64e-4d6b-a73b-1df4fb68999f@48b02"}], "fbx": {"smartMaterialEnabled": true}, "lods": {"enable": false, "hasBuiltinLOD": false, "options": [{"screenRatio": 0.25, "faceCount": 1}, {"screenRatio": 0.125, "faceCount": 0.25}, {"screenRatio": 0.01, "faceCount": 0.1}]}, "assetFinder": {"meshes": ["eb03526d-b64e-4d6b-a73b-1df4fb68999f@faac9"], "skeletons": [], "textures": ["eb03526d-b64e-4d6b-a73b-1df4fb68999f@7d6ab"], "materials": ["eb03526d-b64e-4d6b-a73b-1df4fb68999f@22e2a"], "scenes": ["eb03526d-b64e-4d6b-a73b-1df4fb68999f@82caa"]}, "materials": {"eb03526d-b64e-4d6b-a73b-1df4fb68999f@ef934": {"__type__": "cc.Material", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "_effectAsset": {"__uuid__": "c8f66d17-351a-48da-a12c-0212d28575c4", "__expectedType__": "cc.EffectAsset"}, "_techIdx": 0, "_defines": [{"USE_INSTANCING": true, "USE_ALBEDO_MAP": true}, {}, {}, {}, {}, {}], "_states": [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], "_props": [{"mainColor": {"__type__": "cc.Color", "r": 188, "g": 188, "b": 188, "a": 255}, "mainTexture": {"__uuid__": "2e3dfaf4-dfc9-4d8a-8de8-e5b7fadbc629@6c48a", "__expectedType__": "cc.Texture2D"}}, {}, {}, {}, {}, {}]}, "eb03526d-b64e-4d6b-a73b-1df4fb68999f@22e2a": {"__type__": "cc.Material", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "_effectAsset": {"__uuid__": "f648964e-8d32-41fc-9ac9-7a1e714dd17b", "__expectedType__": "cc.EffectAsset"}, "_techIdx": 0, "_defines": [{"DCC_APP_NAME": 2, "USE_ALBEDO_MAP": true, "HAS_EXPORTED_METALLIC": true}, {}, {}, {}], "_states": [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], "_props": [{"mainTexture": {"__uuid__": "5a4d7bfe-11c8-41b9-b0ee-a0df6a75da16@6c48a", "__expectedType__": "cc.Texture2D"}, "mainColor": {"__type__": "cc.Color", "r": 231, "g": 231, "b": 231, "a": 255}, "emissive": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "emissiveScale": 0, "shininessExponent": 25, "specularFactor": 0.25, "transparencyFactor": 0}, {}, {}, {}]}}}}