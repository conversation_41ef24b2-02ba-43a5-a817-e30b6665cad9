import { _decorator, AudioClip, color, Component, EPhysicsDrawFlags, Input, input, instantiate, Label, lerp, log, Node, NodePool, PhysicsSystem, Prefab, resources, Tween, UITransform, v3, Vec3 } from 'cc';
import { AudioManager, EventBusSystem, fpplayable, OnceEvent, OnEvent, SCREEN_DIRECTION, SystemEventType, Utils, XTween } from 'playable-fpframework';
import { OrbitFollowTarget } from './OrbitFollowTarget';
import { logicConfig } from './LogicConfig';
import { Global } from './Globals';
import { GameEventType } from './Constants';
import { CollectBox, FinishType } from './CollectBox';
import { Sell } from './Sell';
const { ccclass, property } = _decorator;

@ccclass('GameMgr')
export class GameMgr extends Component {

    @property(OrbitFollowTarget)
    private orbitFollowTarget: OrbitFollowTarget;

    @property(Prefab)
    private flotagePrefab: Prefab;
    @property(Node)
    private flotageParent: Node;
    private flotagePool: NodePool = new NodePool();

    @property(Prefab)
    private coinPrefab: Prefab;
    private coinPool: NodePool = new NodePool();

    @property(Node)
    public animNode: Node;

    @property(Node)
    private collectGround: Node;
    @property(Node)
    private newGround: Node;
    @property(Node)
    private newGroundCollectBox: Node;

    @property(Node)
    private collectFence: Node;

    @property(Node)
    private newGroundFence: Node;

    @property(Node)
    private collectWorker: Node;

    @property(Node)
    private collectScene: Node;

    @property(Node)
    private collectGun: Node;

    @property(Node)
    private sellWood: Node;

    @property(Node)
    private sellGun: Node;

    @property(Node)
    private gunClientMgr: Node;

    @property(Node)
    private worker: Node;

    @property(Node)
    private camera_target: Node;

    @property(Label)
    private lab_coin: Label;

    @property(Node)
    private dragStopping: Node;

    @property([Node])
    private collectBoxGuide: Node[] = [];

    @property(Node)
    private arrow: Node;

    @property([Sell])
    private guideSell: Sell[] = [];

    private screenDirection: string = "";

    protected onLoad(): void {
        const start = new Date().getTime();
        console.log("preloadDir start", start);
        resources.preloadDir('common/audio', AudioClip, (err, res) => {
            if (err) {
                console.log('preloadDir error', err);
                return;
            }
            console.log('preloadDir success', res);
            console.log("preloadDir end", new Date().getTime() - start);
        });
        Global.gameMgr = this;
        const global = {
            ios_url: "https://apps.apple.com/fr/app/sea-of-conquest-pirate-war/id6463715971",
            google_url: "https://play.google.com/store/apps/details?id=com.seaofconquest.global"
        }

        const kr = {
            ios_url: "https://apps.apple.com/kr/app/%EC%A0%95%EB%B3%B5%EC%9D%98-%EB%B0%94%EB%8B%A4-%ED%95%B4%EC%A0%81-%EC%8B%9C%EB%AE%AC%EB%A0%88%EC%9D%B4%EC%85%98-rpg/id6476547835",
            google_url: "https://play.google.com/store/apps/details?id=com.seaofconquest.gpkr"
        }

        const ru = {
            ios_url: "https://apps.apple.com/ru/app/sea-of-conquest-pirate-war/id6476490116",
            google_url: "https://play.google.com/store/apps/details?id=com.seaofconquest.gpru&gl=US"
        }
        fpplayable.initProject({
            ios_url: ru.ios_url,
            google_url: ru.google_url
        })
        Global.isEnd = false;
        this.schedule(this.checkGuide, 2.5, Infinity, 0);
    }

    start() {
        this.schedule(this.floating.bind(this), .5, Infinity, 0);

        this.node.once(Node.EventType.TOUCH_START, () => {
            AudioManager.musicPlay("bgm", true);
        }, this);
        this.lab_coin.string = "100";
        this.guideAnim();
        this.scheduleOnce(() => {
            this.checkGuide();
        }, 0.1);
    }

    guideAnim() {
        const arrow = this.arrow.children[0];
        new XTween(arrow, Infinity, true).to(0.5, { position: v3(0, -0.2, 0) }, { easing: "quadraticInOut" }).play();
        let r = this.dragStopping.getComponent(UITransform).width * this.dragStopping.scale.x / 4;
        new XTween({}, Infinity).delay(0.3).to(0.6, {}, {
            easing: "cubicIn",
            onUpdate: (_, ratio) => {
                let x = r * Math.sin(((1 - ratio) * 360 - 135) / 180 * Math.PI);
                let y = r * Math.cos(((1 - ratio) * 360 - 135) / 180 * Math.PI);
                this.dragStopping.getChildByName("pole").setPosition(v3(x, y, 0).add(v3(r - 20, r - 20, 0)));
            }
        }).play();
    }

    // 获取金币
    getCoin() {
        let coin = this.coinPool.get();
        if (!coin) {
            coin = instantiate(this.coinPrefab);
        }
        return coin;
    }

    // 回收金币
    freeCoin(coin: Node) {
        this.coinPool.put(coin);
        coin.setWorldScale(1, 1, 1);
        coin.setWorldRotationFromEuler(0, 0, 0);
        coin.removeFromParent();
    }


    // 获取漂浮物
    getFlotage() {
        let flotage = this.flotagePool.get();
        if (!flotage) {
            flotage = instantiate(this.flotagePrefab);
        }
        return flotage;
    }

    // 漂浮物
    floating() {
        for (let i = 0; i < 4; i++) {
            const flotage = this.getFlotage();
            flotage.setParent(this.flotageParent);
            const z = i * 1.7 - 3.4 + Utils.random(-10, 10) / 20;
            const x = Utils.random(-10, 10) / 20;
            flotage.setPosition(-23 + x, 0, z);
            flotage.active = true;
            const distance = 46;
            const rotate = Utils.random(0, 180);
            flotage.eulerAngles = v3(0, rotate, 0);
            new Tween(flotage).to(distance / logicConfig.flotageSpeed, { position: v3(15 + x, 0, z) }).call(() => {
                this.flotagePool.put(flotage);
            }).start();
        }
    }

    @OnEvent(GameEventType.FINISHED_DELIVERY)
    async finishedDelivery({ type }) {
        AudioManager.soundPlay("collect_finished");
        switch (type) {
            case FinishType.WOOD:
                this.collectFence.active = false;
                this.collectGround.active = true;
                this.collectWorker.active = true;
                this.collectGun.active = true;
                this.sellWood.active = true;
                AudioManager.soundPlay("show");
                this.sellWood.getChildByName("render").active = true;
                new XTween(this.collectGround)
                    .fromTo(0.5, { position: v3(0, -1, 0) }, { position: v3(0, 0, 0) }, { easing: "elasticOut" })
                    .play();
                await Utils.delay(0.3, this);
                this.collectGround.getChildByName("npc").children.forEach(e => {
                    e.getChildByName("show").active = true;
                })
                break;
            case FinishType.WORKER:
                if (!this.collectGun.active) {
                    this.collectScene.active = true;
                }
                AudioManager.soundPlay("show");
                this.worker.active = true;
                break;
            case FinishType.GUN:
                if (!this.collectWorker.active) {
                    this.collectScene.active = true;
                }
                AudioManager.soundPlay("show");
                this.sellGun.active = true;
                new XTween(this.sellGun).fromTo(0.5, { scale: v3(0, 0, 0) }, { scale: v3(1, 1, 1) }, { easing: "elasticOut" }).play();
                this.gunClientMgr.active = true;
                break;
            case FinishType.SCENE:
                AudioManager.soundPlay("show");
                this.node.getChildByName("download").active = true;
                this.dragStopping.active = false;
                this.unschedule(this.showGuide);
                this.camera_target.setWorldPosition(Global.role.node.worldPosition);
                this.scheduleOnce(() => {
                    this.orbitFollowTarget.target = this.camera_target;
                    this.newGroundFence.active = false;
                    this.newGround.active = true;
                    const boat = this.newGround.getChildByName("boat");
                    new XTween(boat).to(1.5, { position: v3(0, 0, 0) }, { easing: "quarticOut" }).play();
                    new XTween(this.camera_target).fromTo(1, { worldPosition: Global.role.node.worldPosition }, { worldPosition: this.newGroundCollectBox.worldPosition }, { easing: "cubicOut" })
                        .delay(0.6)
                        // .call(() => {
                        //     this.node.getChildByName("download").on(Node.EventType.TOUCH_START, () => {
                        //         fpplayable.download();
                        //     }, this);
                        //     fpplayable.download();
                        // })
                        .to(0.5, { worldPosition: Global.role.node.worldPosition })
                        .play()
                        .onFinally(() => {
                            this.orbitFollowTarget.target = Global.role.node;
                            this.node.getChildByName("download").active = false;
                        });

                    new XTween(this.newGround)
                        .fromTo(0.5, { position: v3(0, 0, 0) }, { position: v3(0, 0, 0) }, { easing: "elasticOut" })
                        .play().onFinally(() => {
                            this.newGroundCollectBox.active = true;
                        });
                }, 0.15);
                break;
        }
    }

    isEnd: boolean = false;

    @OnceEvent(SystemEventType.GMAE_END)
    gameEnd() {
        if (this.isEnd) return;
        this.isEnd = true;
        this.node.getChildByName("download").active = true;
        this.node.getChildByName("download").on(Node.EventType.TOUCH_START, () => {
            fpplayable.download();
        }, this);
        fpplayable.gameEnd(true);
        fpplayable.download();
    }

    @OnEvent(SystemEventType.SCREEN_RESIZE)
    screenResize({ type }) {
        this.screenDirection = type === SCREEN_DIRECTION.LANDSCAPE ? "Landscape" : "Portrait";
        this.orbitFollowTarget.polarAngle = logicConfig[`polarAngle${this.screenDirection}`];
        this.orbitFollowTarget.azimuthAngle = logicConfig[`azimuthAngle${this.screenDirection}`];
        this.orbitFollowTarget.radialDistance = logicConfig[`radius${this.screenDirection}`];
        this.orbitFollowTarget.viewOffset = logicConfig[`viewOffset${this.screenDirection}`];
    }

    @OnEvent(SystemEventType.JOY_TOUCH_START)
    onTouchStart() {
        this.dragStopping.active = false;
        this.unschedule(this.showGuide);
        this.orbitFollowTarget.radialDistance;
        XTween.removeTagTweens(this.orbitFollowTarget);
        new XTween(this.orbitFollowTarget).to(3, { radialDistance: logicConfig[`radius${this.screenDirection}`] - 2 }, { easing: "circularOut" }).play();
    }

    @OnEvent(SystemEventType.JOY_TOUCH_END)
    onTouchEnd() {
        this.scheduleOnce(this.showGuide, 2);
        XTween.removeTagTweens(this.orbitFollowTarget);
        new XTween(this.orbitFollowTarget).to(.6, { radialDistance: logicConfig[`radius${this.screenDirection}`] }, { easing: "backOut" }).play();
    }

    protected lateUpdate(dt: number): void {
        this.orbitFollowTarget.polarAngle = logicConfig[`polarAngle${this.screenDirection}`];
        this.orbitFollowTarget.azimuthAngle = logicConfig[`azimuthAngle${this.screenDirection}`];
        this.orbitFollowTarget.radialDistance = logicConfig[`radius${this.screenDirection}`];
        this.orbitFollowTarget.viewOffset = logicConfig[`viewOffset${this.screenDirection}`];
        if (Vec3.distance(Global.role.node.worldPosition, this.arrow.worldPosition) < 3) {
            this.arrow.active = false;
        }
    }

    //#region 引导

    showGuide() {
        if (Global.isEnd) return;
        this.dragStopping.active = true;
    }

    checkGuide() {
        if (Global.isEnd) return;
        const role = Global.role;
        const coin = role.coinCollectAssets.list.length * 10;
        const collectBox = this.collectBoxGuide.find(e => e.active && coin >= e.getComponent(CollectBox).need);
        let wps: Vec3;

        if (collectBox) {
            wps = v3(collectBox.worldPosition.x, this.arrow.worldPosition.y, collectBox.worldPosition.z);
        } else {
            const sell = this.guideSell.filter(e => e.node.active && e.coinCount > 0);
            sell.sort((a, b) => a.coinCount - b.coinCount);
            if (sell?.[0]) {
                wps = v3(sell[0].frame.node.worldPosition.x, this.arrow.worldPosition.y, sell[0].frame.node.worldPosition.z);
            }
            else {
                if (role.woodCollectAssets.list.length > 10) {
                    const render = this.sellWood.getChildByName("render");
                    wps = v3(render.worldPosition.x, this.arrow.worldPosition.y, render.worldPosition.z);
                } else {
                    wps = v3(Global.woodFactory.node.worldPosition.x, this.arrow.worldPosition.y, Global.woodFactory.node.worldPosition.z);
                }
            }
        }
        this.arrow.setWorldPosition(wps);
        if (Vec3.distance(role.node.worldPosition, wps) > 3) {
            this.arrow.active = true;
        }
    }
    //#endregion

    //#region 更新金币UI
    @OnEvent(GameEventType.UPDATE_COIN)
    updateLabelCoin({ coin }) {
        const curCoin = Number(this.lab_coin.string);
        if (curCoin > coin) {
            this.lab_coin.color = color("#FF0000");
        } else {
            this.lab_coin.color = color("#00FF00");
        }
        XTween.removeTagTweens(this.lab_coin.node);
        new XTween(this.lab_coin.node)
            .to(0.3, { scale: v3(1.5, 1.5, 1.5) }, {
                easing: "elasticOut",
                onUpdate: (_, t) => {
                    this.lab_coin.string = lerp(curCoin, coin, t).toFixed(0);
                }
            })
            .call(() => {
                this.lab_coin.color = color("#FFFFFF");
            })
            .to(0.1, { scale: v3(1, 1, 1) })
            .play();
    }
    //#endregion

    download() {
        fpplayable.download();
    }
}

