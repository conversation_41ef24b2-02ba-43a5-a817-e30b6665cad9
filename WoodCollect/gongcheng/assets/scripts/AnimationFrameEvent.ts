import { _decorator, Component, Node } from "cc";
import { AnimationEventType, EventBusSystem, fpplayable } from "playable-fpframework";
const { ccclass, property } = _decorator;

@ccclass("AnimationFrameEvent")
export class AnimationFrameEvent extends Component {
  onAnimationEffectEvent(...args) {
    EventBusSystem.emit(AnimationEventType.ON_ANIMTION_EFFECT_EVENT, { args });
  }

  onAnimationDamageEvent(...args) {
    EventBusSystem.emit(AnimationEventType.ON_ANIMTION_DAMAGE_EVENT, { args });

  }
}
