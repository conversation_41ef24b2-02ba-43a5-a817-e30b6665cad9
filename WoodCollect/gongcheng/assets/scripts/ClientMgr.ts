import { _decorator, color, Component, Enum, instantiate, Node, NodePool, Pool, Prefab, Sprite, v3 } from 'cc';
import { Client } from './Client';
import { Global } from './Globals';
import { logicConfig } from './LogicConfig';
import { EventBusSystem, Utils, XTween, XTweenUtils } from 'playable-fpframework';
import { AssetsType, GameEventType } from './Constants';
const { ccclass, property } = _decorator;

@ccclass('ClientMgr')
export class ClientMgr extends Component {
    /** 客人预制体 */
    @property(Prefab)
    private clientPrefab: Prefab = null;

    /** 客人路径父节点 */
    @property(Node)
    private clientPath: Node;

    @property
    private clientCount: number = 6;
    @property
    private clientInterval: number = 0.5;

    @property({ type: Enum(AssetsType) })
    assetType: AssetsType = AssetsType.Wood;

    /** 客人列表 */
    private clientList: Client[] = [];

    /** 当前收银客人 */
    private _curClient: Client = null;
    public get curClient(): Client {
        return this._curClient;
    }
    public set curClient(client: Client) {
        this._curClient = client;
        if (this._curClient) {
            this.curClient.showProgress();
            EventBusSystem.emit(GameEventType.START_CASHIER, { client: this.curClient, type: this.assetType });
        }
    }

    clientPool: NodePool = new NodePool();

    protected start(): void {
        this.initClient();
    }

    private async initClient() {
        for (let i = 0; i < this.clientCount; i++) {
            this.createClient();
            await Utils.delay(this.clientInterval, this);
        }
    }

    createClient() {
        let client = this.clientPool.get();
        if (!client) {
            client = instantiate(this.clientPrefab);
        }
        this.node.addChild(client);
        const pos = this.clientPath.children[0].worldPosition;
        client.setWorldPosition(pos);
        const comp = client.getComponent(Client);
        comp.playAnim("walk");
        comp.init(this.clientPath.children, this, this.assetType);
        this.clientList.push(comp);
    }

    resume(type: AssetsType) {
        this.clientList.forEach(c => {
            if (c.assetType === type && c.isWait) {
                c.resume();
            }
        });
        this.createClient();
    }

    // 回收客人
    recycle(client: Client) {
        this.clientPool.put(client.node);
    }
}

