import { _decorator, Collider, Component, ITriggerEvent, Node, RigidBody, SkeletalAnimation, Vec3 } from 'cc';
import { logicConfig } from './LogicConfig';
import { JoyStick } from './JoyStick';
import { CollectBox } from './CollectBox';
import { Global } from './Globals';
import { AudioManager, EventBusSystem, SystemEventType, Utils, XTween, XTweenUtils } from 'playable-fpframework';
import { GameEventType, RoleState } from './Constants';
import { WoodFactory } from './WoodFactory';
import { CollectAssets } from './CollectAssets';
import { Sell } from './Sell';
const { ccclass, property } = _decorator;

@ccclass('Role')
export class Role extends Component {

    @property(Node)
    private camera: Node;

    @property(Node)
    private backCoin: Node;

    @property(Node)
    private backWood: Node;

    @property(SkeletalAnimation)
    private render: SkeletalAnimation;

    private state: RoleState = RoleState.Idle;
    private moveDirection: Vec3 = new Vec3()
    private roleRigidBody: RigidBody = null;

    woodCollectAssets: CollectAssets;
    coinCollectAssets: CollectAssets;

    private intervalID: number;

    onLoad() {
        Global.role = this;
        this.roleRigidBody = this.node.getComponent(RigidBody);
        this.woodCollectAssets = this.backWood.getComponent(CollectAssets);
        this.coinCollectAssets = this.backCoin.getComponent(CollectAssets);
        this.registerEvent();
    }

    protected start(): void {
        for (let i = 0; i < 20; i++) {
            const coin = Global.gameMgr.getCoin();
            this.backCoin.addChild(coin);
            this.coinCollectAssets.list.push(coin);
            coin.setWorldPosition(this.coinCollectAssets.nextAssetPosition);
        }
    }

    protected lateUpdate(dt: number): void {
        this.backWood.setPosition(0, 0.5, this.coinCollectAssets.list.length > 0 ? -0.9 : -0.4);
    }

    registerEvent() {
        this.node.getComponent(Collider).on("onTriggerEnter", this.onTriggerEnter, this);
        this.node.getComponent(Collider).on("onTriggerExit", this.onTriggerExit, this);
    }

    update(deltaTime: number) {
        if (JoyStick.instance.direction.length() > 0.2) {
            if (this.state === RoleState.Idle) {
                this.state = RoleState.Walk;
                this.render.crossFade("run", 0.2);
            }
            let dir = JoyStick.instance.direction.clone();
            const angleY = Math.atan2(dir.y, dir.x) * (180 / Math.PI) + 90 + this.camera.eulerAngles.y; // 计算Y轴旋转角度
            this.node.setRotationFromEuler(0, angleY, 0); // 仅设置Y轴旋转
            const forward = this.node.forward.multiplyScalar(-1);
            Vec3.multiplyScalar(this.moveDirection, forward, logicConfig.speed);
            this.roleRigidBody.setLinearVelocity(this.moveDirection);
        } else {
            if (this.state === RoleState.Walk) {
                this.state = RoleState.Idle;
                this.render.crossFade("idle", 0.2);
            }
            this.roleRigidBody.setLinearVelocity(Vec3.ZERO);
        }
    }

    delivery(args) {
        const collect_box: CollectBox = args[0];
        if (collect_box.finished) return;
        if (this.coinCollectAssets.list.length > 0) {
            collect_box.realCoinCount += 5;
            const coin = this.coinCollectAssets.asset;
            EventBusSystem.emit(GameEventType.UPDATE_COIN, { coin: this.coinCollectAssets.list.length * 5 });
            Utils.changeParent(coin, Global.gameMgr.animNode);
            const tween = XTweenUtils.bezierTo(
                coin,
                0.3,
                coin.worldPosition.clone(),
                collect_box.node.worldPosition.clone(),
                this.coinCollectAssets.list.length < 10 ? 4 : 2,
                .8,
                .4,
                { easing: "backIn" }
            );
            tween.play()
                .onFinally(() => {
                    AudioManager.soundPlay("delivery");
                    collect_box.curCount += 5;
                    Global.gameMgr.freeCoin(coin);
                });
        }
    }

    onTriggerEnter(event: ITriggerEvent) {
        const collect_box = event.otherCollider.getComponent(CollectBox);
        const woodFactory = event.otherCollider.getComponent(WoodFactory);
        const sell = event.otherCollider.getComponent(Sell);
        if (collect_box) {
            if (!collect_box.finished) {
                this.delivery([collect_box]);
                this.intervalID = setInterval(this.delivery.bind(this), 40, [collect_box]);
            }
            if (collect_box.isJump) {
                EventBusSystem.emit(SystemEventType.GMAE_END, { isWin: true });
            }
        } else if (woodFactory) {
            woodFactory.collectWood(this.backWood.getComponent(CollectAssets));
            // this.delivery([woodFactory]);
        } else if (sell) {
            sell.collectWood(this.backWood.getComponent(CollectAssets));
            sell.deliveryCoin(this.backCoin.getComponent(CollectAssets));
        } else if (event.otherCollider.node.name === "download") {
            EventBusSystem.emit(SystemEventType.GMAE_END, { isWin: true });
        }
    }

    onTriggerExit(event: ITriggerEvent) {
        const collect_box = event.otherCollider.getComponent(CollectBox);
        const woodFactory = event.otherCollider.getComponent(WoodFactory);
        const sell = event.otherCollider.getComponent(Sell);
        if (collect_box) {
            clearInterval(this.intervalID);
        } else if (woodFactory) {
            woodFactory.cancelCollectWood(this.backWood.getComponent(CollectAssets));
        } else if (sell) {
            sell.cancelCollectWood(this.backWood.getComponent(CollectAssets));
            sell.cancelDeliveryCoin();
        }
    }

    protected onDestroy(): void {
        this.intervalID && clearInterval(this.intervalID);
    }

}

