import { _decorator, Component, Node, SkeletalAnimation, Tween, Vec3 } from 'cc';
import { Sell } from './Sell';
import { WorkerState } from './Constants';
import { XTween } from 'playable-fpframework';
import { logicConfig } from './LogicConfig';
import { Global } from './Globals';
import { CollectAssets } from './CollectAssets';
const { ccclass, property } = _decorator;

@ccclass('Worker')
export class Worker extends Component {
    @property(Node)
    private sellNode: Node;

    @property(Node)
    private collectNode: Node;

    @property(Sell)
    private sell: Sell;

    @property(CollectAssets)
    private collectAssets: CollectAssets;

    private state: WorkerState = WorkerState.SELL;

    private time: number = 0;

    private render: SkeletalAnimation;

    protected onLoad(): void {
        this.render = this.node.getChildByName("render").getComponent(SkeletalAnimation);
        this.time = Vec3.distance(this.sellNode.worldPosition, this.collectNode.worldPosition) / logicConfig.workerSpeed;
    }

    protected lateUpdate(dt: number): void {
        if (this.state === WorkerState.SELL) {
            if (this.collectAssets.list.length <= 0) {
                if (this.sell.assetsCount < 10) {
                    this.state = WorkerState.COLLECT;
                    this.collect();
                } else {
                    this.render.crossFade("idle");
                }
            }
        } else {
            if (this.collectAssets.list.length >= 20) {
                Global.woodFactory.cancelCollectWood(this.collectAssets);
                this.state = WorkerState.SELL;
                this.delivery();
            }
        }
    }

    collect() {
        this.render.crossFade("Walk", 0.2);
        this.render.node.setRotationFromEuler(0, 180, 0);
        new Tween(this.node).to(this.time, { worldPosition: this.collectNode.worldPosition }).call(() => {
            this.render.crossFade("HoldingIdle", 0.2);
            Global.woodFactory.collectWood(this.collectAssets);
        }).start();
    }

    delivery() {
        this.render.node.setRotationFromEuler(0, 0, 0);
        this.render.crossFade("HoldingWalk", 0.2);
        new Tween(this.node).to(this.time, { worldPosition: this.sellNode.worldPosition }).call(() => {
            this.render.crossFade("HoldingIdle", 0.2);
            this.sell.collectWood(this.collectAssets);
        }).start();
    }

}

