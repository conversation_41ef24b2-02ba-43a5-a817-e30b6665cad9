import { _decorator, Component, EventTouch, Node, UITransform, Vec3 } from "cc";
import { EventBusSystem, SystemEventType } from "playable-fpframework";
const { ccclass, property, menu } = _decorator;

@ccclass("JoyStick")
@menu("2D/JoyStick")
export class JoyStick extends Component {
    private static _instance: JoyStick = null;
    public static get instance(): JoyStick {
        return this._instance;
    }

    private dish: Node = null;
    private pole: Node = null;

    public direction: Vec3 = Vec3.ZERO;

    protected onLoad(): void {
        JoyStick._instance = this;
        this.dish = this.node.getChildByName("dish");
        this.pole = this.dish.getChildByName("pole");
    }

    start() {
        this.registerEvent();
        this.dish.active = false;
    }

    protected onDestroy(): void {
        this.unregisterEvent();
    }

    /** 注册事件 */
    private registerEvent() {
        this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    }

    /** 注销事件 */
    private unregisterEvent() {
        this.node.off(Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.off(Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.off(Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.off(Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    }

    private onTouchStart(event: EventTouch) {
        let touch = event.getUILocation();
        this.dish.setWorldPosition(new Vec3(touch.x, touch.y, 0));
        this.dish.active = true;
        EventBusSystem.emit(SystemEventType.JOY_TOUCH_START);
    }

    private onTouchMove(event: EventTouch) {
        let touch = event.getUILocation();
        const uiTransform = this.dish.getComponent(UITransform);

        let position = uiTransform.convertToNodeSpaceAR(new Vec3(touch.x, touch.y, 0));
        let ratio = position.length() / (uiTransform.width / 2);
        this.pole.setPosition(ratio > 1 ? position.divide(new Vec3(ratio, ratio, 1)) : position);
        this.direction = this.pole.position.clone().normalize();
    }

    private onTouchEnd() {
        EventBusSystem.emit(SystemEventType.JOY_TOUCH_END);
        this.direction = Vec3.ZERO;
        this.dish.active = false;
        this.pole.setPosition(Vec3.ZERO);
    }
}
