import { _decorator, color, Color, Component, Node, NodePool, Prefab, Sprite, v3, Vec3 } from 'cc';
import { CollectAssets } from './CollectAssets';
import { AudioManager, EventBusSystem, OnEvent, Utils, XTween, XTweenUtils } from 'playable-fpframework';
import { Global } from './Globals';
import { AssetsType, GameEventType } from './Constants';
import { ClientMgr } from './ClientMgr';
const { ccclass, property } = _decorator;

@ccclass('Sell')
export class Sell extends Component {
    @property(Node)
    private assetsParent: Node;

    @property(Sprite)
    public frame: Sprite;

    @property(Node)
    private coinParent: Node;

    @property(ClientMgr)
    private clientMgr: ClientMgr;

    @property({ displayName: "是否检查人物身上的木材数量" })
    isCheckWood: boolean = false;

    @property(Node)
    private gunNode: Node;

    @property()
    private isXOffset: boolean = false;

    private coinList: Node[] = [];

    private assetsList: Node[] = [];

    private deliveryId: number;

    private timer: number = 0;

    get assetsCount() {
        return this.assetsList.length;
    }

    get coinCount() {
        return this.coinList.length;
    }

    collectAssetsMap: Map<CollectAssets, number> = new Map();

    collectWood(collectAssets: CollectAssets) {
        if (!this.isCheckWood) return;
        let id = setInterval(() => {
            const asset = collectAssets.asset;
            if (!asset) {
                clearInterval(id);
                this.collectAssetsMap.delete(collectAssets);
                return;
            }
            Utils.changeParent(asset, Global.gameMgr.animNode);
            this.assetsList.push(asset);
            const pos = v3(0, this.assetsList.length * 0.23, 0).add(this.assetsParent.worldPosition);
            const bezier = XTweenUtils.bezierTo(asset, 0.25, asset.worldPosition, pos, 1.5, .8, .4, { easing: "backIn" });
            const rotate = new XTween(asset).to(0.3, { eulerAngles: v3(0, 90, 0) });
            new XTween(asset).add(bezier, rotate)
                .call(() => {
                    Utils.changeParent(asset, this.assetsParent);
                    asset.setRotationFromEuler(v3(0, 90, 0));
                    AudioManager.soundPlay("collect");
                })
                .to(0.07, { scale: v3(1.5, 1.5, 1.5) })
                .to(0.07, { scale: v3(1, 1, 1) })
                .play().setTag(asset.uuid);
        }, 50);
        this.collectAssetsMap.set(collectAssets, id);
    }

    deliveryCoin(collectAssets: CollectAssets) {
        this.frame.color = color("#00FF00");
        this.deliveryId = setInterval(() => {
            if (this.isCheckWood && Global.role.woodCollectAssets.list.length > 0) return;
            const asset = this.coinList.pop();
            if (!asset) return;
            XTween.removeTagTweens(asset);
            Utils.changeParent(asset, Global.gameMgr.animNode);
            collectAssets.list.push(asset);
            EventBusSystem.emit(GameEventType.UPDATE_COIN, { coin: collectAssets.list.length * 5 });
            const pos = collectAssets.nextAssetPosition;
            XTweenUtils.bezierTo(asset, 0.3, asset.worldPosition, pos, 2, .8, .4, { easing: "backIn" })
                .call(() => {
                    AudioManager.soundPlay("collect");
                    collectAssets.node.addChild(asset);
                })
                .to(0.07, { scale: v3(1.5, 1.5, 1.5) })
                .to(0.07, { scale: v3(1, 1, 1) })
                .play()
        }, 60);
    }

    cancelCollectWood(collectAssets: CollectAssets) {
        const id = this.collectAssetsMap.get(collectAssets);
        if (id) {
            clearInterval(id);
            this.collectAssetsMap.delete(collectAssets);
        }
    }

    cancelDeliveryCoin() {
        clearInterval(this.deliveryId);
        this.frame.color = color("#FFFFFF");
    }

    sellTime: number = 0;

    protected lateUpdate(dt: number): void {
        this.sellTime += dt;
        if (this.sellTime >= 0.07) {
            this.sellTime = 0;
            this.startCashier({ client: this.clientMgr.curClient, type: this.clientMgr.assetType });
        }
    }

    @OnEvent(GameEventType.START_CASHIER)
    async startCashier({ client, type }) {
        if (type === AssetsType.Wood) {
            if (!this.assetsList.length || !client || client.needAsset <= 0) return;
            client.realCount++;
            const wood = this.assetsList.pop();
            if (!wood) return;
            XTween.removeTagTweens(wood.uuid);
            Utils.changeParent(wood, Global.gameMgr.animNode);
            XTweenUtils.bezierTo(wood, 0.25, wood.worldPosition, client.ui.worldPosition, 1.5, .8, .4, { easing: "backIn" })
                .call(() => {
                    // console.log("sell finished", wood.uuid);
                    EventBusSystem.emit(GameEventType.FREE_WOOD, { wood });
                    client.curAssetCount++;
                    if (client.needAsset <= 0 && !client.isPay) {
                        client.isPay = true;
                        this.pay(client.ui.worldPosition);
                    }
                })
                .play();
        } else {
            if (!client || client.needAsset <= 0) return;
            client.realCount++;
            const gun = this.gunNode;
            this.gunNode.active = true;
            XTween.removeTagTweens(gun.uuid);
            gun.setWorldPosition(this.assetsParent.worldPosition);
            Utils.changeParent(gun, Global.gameMgr.animNode);
            XTweenUtils.bezierTo(gun, 0.25, gun.worldPosition, client.ui.worldPosition, 1.5, .8, .4, { easing: "backIn" })
                .call(() => {
                    this.gunNode.active = false;
                    // console.log("sell finished", gun.uuid);
                    client.curAssetCount++;
                    if (client.needAsset <= 0 && !client.isPay) {
                        client.isPay = true;
                        this.pay(client.ui.worldPosition);
                    }
                })
                .play()
        }
    }

    async pay(wps: Vec3) {
        AudioManager.soundPlay("finished");
        for (let i = 0; i < 2; i++) {
            const coin = Global.gameMgr.getCoin();
            coin.setParent(this.coinParent);
            coin.setWorldPosition(wps);
            this.coinList.push(coin);
            const pos = v3(0, Math.floor(this.coinList.length / 2) * 0.08, 0)
            if (this.isXOffset) {
                pos.x = this.coinList.length % 2 * 0.5 - 0.25;
            } else {
                pos.z = this.coinList.length % 2 * 0.5 - 0.25;
            }
            pos.add(this.coinParent.worldPosition);
            XTweenUtils.bezierTo(coin, 0.3, coin.worldPosition, pos, 2, .8, .4, { easing: "backIn" })
                .call(() => {
                    AudioManager.soundPlay("collect");
                })
                .to(0.07, { scale: v3(1.5, 1.5, 1.5) })
                .to(0.07, { scale: v3(1, 1, 1) }).play();
            await Utils.delay(0.05, this);
        }
    }

    protected onDestroy(): void {
        clearInterval(this.timer);
    }

}

