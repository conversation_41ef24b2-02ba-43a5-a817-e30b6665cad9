import { _decorator, Component, easing, instantiate, Node, NodePool, Prefab, v3, Vec3 } from 'cc';
import { AudioManager, OnEvent, Utils, XTweenUtils } from 'playable-fpframework';
import { CollectAssets } from './CollectAssets';
import { Global } from './Globals';
import { GameEventType } from './Constants';
const { ccclass, property } = _decorator;

@ccclass('WoodFactory')
export class WoodFactory extends Component {

    @property(Prefab)
    private woodPrefab: Prefab;
    private woodPool: NodePool = new NodePool();

    @property([Node])
    private woodStartPoint: Node[] = [];

    private woodList: Node[] = [];

    // 木头放置的配置
    private rowsPerLayer = 2;  // 每层2排
    private colsPerRow = 10;   // 每排10列
    private woodSpacingX = 0.35;   // 木头之间的间距
    private woodSpacingZ = -1.1;   // 木头之间的间距
    private layerHeight = 0.23;   // 层高

    public roles: CollectAssets[] = [];

    private get role(): CollectAssets {
        return this.roles?.[0];
    }

    protected onLoad(): void {
        Global.woodFactory = this;
    }

    protected start(): void {
        this.schedule(this.createWood.bind(this), 1, Infinity, 1);
    }

    // 获取木头
    private getWood() {
        let wood = this.woodPool.get();
        if (!wood) {
            wood = instantiate(this.woodPrefab);
        }
        return wood;
    }

    // 回收木头
    @OnEvent(GameEventType.FREE_WOOD)
    private freeWood({ wood }) {
        this.woodPool.put(wood);
    }

    async createWood() {
        for (let i = 0; i < 3; i++) {
            for (let j = 0; j < this.woodStartPoint.length; j++) {
                const wps = this.woodStartPoint[j].worldPosition;
                const wood = this.getWood();
                let dest;
                let parent = this.node;
                if (this.role) {
                    this.role.list.push(wood);
                    dest = this.role.nextAssetPosition;
                    parent = this.role.node;
                    wood.setParent(Global.gameMgr.animNode);
                    wood.setWorldPosition(wps);
                } else {
                    this.woodList.push(wood);
                    wood.setParent(this.node);
                    wood.setWorldPosition(wps);
                    dest = this.calculateWoodPosition(this.woodList.length - 1).add(this.node.worldPosition);
                }
                wood.setRotationFromEuler(0, 0, 0);
                XTweenUtils.bezierTo(wood, 0.25, wood.worldPosition, dest, 4, .8, .4, { easing: "backIn" })
                    .call(() => {
                        if (parent !== this.node) {
                            wood.setParent(parent);
                            wood.setRotationFromEuler(0, 0, 0);
                        }
                        AudioManager.soundPlay("collect");
                    })
                    .to(0.07, { scale: v3(1.5, 1.5, 1.5) })
                    .to(0.07, { scale: v3(1, 1, 1) })
                    .play();
                await Utils.delay(0.01, this);
            }
            await Utils.delay(0.05, this);
        }
    }

    collectWood(role: CollectAssets) {
        if (this.roles.indexOf(role) !== -1) return;
        this.roles.push(role);
        role["collectIntervalId"] = setInterval(() => {
            const wood = this.woodList.pop();
            if (!wood) return;
            role.list.push(wood);
            const dest = role.nextAssetPosition;
            XTweenUtils.bezierTo(wood, 0.25, wood.worldPosition, dest, 4, .8, .4, { easing: "backIn" })
                .call(() => {
                    wood.setParent(role.node);
                    wood.setRotationFromEuler(0, 0, 0);
                    AudioManager.soundPlay("collect");
                })
                .to(0.1, { scale: v3(1.5, 1.5, 1.5) })
                .to(0.1, { scale: v3(1, 1, 1) })
                .play();
        }, 100);
    }

    cancelCollectWood(role: CollectAssets) {
        if (this.roles.indexOf(role) === -1) return;
        this.roles.splice(this.roles.indexOf(role), 1);
        clearInterval(role["collectIntervalId"]);
    }


    // 计算木头的放置位置
    private calculateWoodPosition(index: number): Vec3 {
        const layer = Math.floor(index / (this.rowsPerLayer * this.colsPerRow));
        const remainingInLayer = index % (this.rowsPerLayer * this.colsPerRow);
        const row = Math.floor(remainingInLayer / this.colsPerRow);
        const col = remainingInLayer % this.colsPerRow;

        // 计算位置
        const x = col * this.woodSpacingX - (this.colsPerRow / 2 * this.woodSpacingX);
        const y = layer * this.layerHeight;
        const z = row * this.woodSpacingZ;

        return new Vec3(x, y, z);
    }

    public getWoodByList() {
        if (!this.woodList.length) return null;
        const wood = this.woodList.pop();
        const dest = this.calculateWoodPosition(this.woodList.length).add(this.node.worldPosition);
        // wood.setWorldPosition(dest);
        return wood;
    }
}

