import { _decorator, Component, Enum, Label, Node, Sprite, v3 } from 'cc';
import { EventBusSystem, fpplayable, SystemEventType, XTween } from 'playable-fpframework';
import { GameEventType } from './Constants';
const { ccclass, property } = _decorator;

export enum FinishType {
    WOOD,
    GUN,
    SCENE,
    WORKER,
}

@ccclass('CollectBox')
export class CollectBox extends Component {

    @property(Label)
    private label: Label;

    @property({ displayName: "需要的金币数量" })
    private coin: number = 100;

    @property({ type: Enum(FinishType), displayName: "收集完成的事件类型" })
    private eventType: FinishType;

    @property(Sprite)
    private bar: Sprite;

    @property
    public isJump: boolean = false;

    // 当前展示的金币数量
    private _curCount: number = 0;
    public get curCount(): number {
        return this._curCount;
    }
    public set curCount(val: number) {
        this._curCount = val;
        XTween.removeTagTweens(this.node);
        this.label.string = (this.coin - this._curCount).toString();
        if (this._curCount >= this.coin) {
            new XTween(this.node)
                .to(0.2, { scale: v3(0, 0, 0) })
                .call(() => {
                    this.node.active = false;
                    EventBusSystem.emit(GameEventType.FINISHED_DELIVERY, { type: this.eventType });
                })
                .play()
        } else {
            new XTween(this.node)
                .to(0.05, { scale: v3(0.9, 0.9, 0.9) })
                .to(0.05, { scale: v3(1, 1, 1) })
                .play();
        }
        XTween.removeTagTweens(this.bar);
        new XTween(this.bar).to(0.05, { fillRange: this._curCount / this.coin }).play();
    }

    // 实际的金币数量
    private _realCoinCount = 0;
    public get realCoinCount(): number {
        return this._realCoinCount;
    }

    public set realCoinCount(val: number) {
        this._realCoinCount = val;
        if (this._realCoinCount >= this.coin) {
            this.finished = true;
        }
    }

    public get need(): number {
        return this.coin - this.realCoinCount;
    }

    public finished: boolean = false;

    onLoad() {
        this.bar = this.node.getChildByPath("render/bar").getComponent(Sprite);
        this.curCount = 0;
    }

    onEnable() {
        new XTween(this.node).fromTo(0.3, { scale: v3(0, 0, 0) }, { scale: v3(1, 1, 1) }, { easing: "elasticOut" }).play();
    }

}
