import { _decorator, Collider, Component, find, ITriggerEvent, Label, Node, SkeletalAnimation, Sprite, Tween, v3, Vec3 } from 'cc';
import { XTween } from 'playable-fpframework';
import { logicConfig } from './LogicConfig';
import { AssetsType } from './Constants';
import { ClientMgr } from './ClientMgr';
import { Global } from './Globals';
const { ccclass, property } = _decorator;

@ccclass('Client')
export class Client extends Component {
    @property(Node)
    public ui: Node;

    @property(Node)
    private smile: Node;

    @property(Sprite)
    private bar: Sprite;
    @property(Label)
    private labCount: Label = null;
    // 需要资源的数量
    private assetCount: number = 4;

    public assetType: AssetsType = AssetsType.Wood;

    private tween: Tween<Node> = null;

    // 当前资源的数量
    private _curAssetCount: number = 0;
    public get curAssetCount(): number {
        return this._curAssetCount;
    }
    public set curAssetCount(value: number) {
        this._curAssetCount = value;
        const count = this.assetCount - value;
        this.labCount.string = `x ${count}`;
        XTween.removeTagTweens(this.bar);
        XTween.removeTagTweens(this.bar.node.parent);
        // console.log("curAssetCount", count);
        new XTween(this.bar).to(0.06, { fillRange: value / this.assetCount }).play();
        new XTween(this.bar.node.parent, 2, true).to(0.03, { scale: v3(1.2, 1.2, 1.2) }).play().onFinally(() => {
            if (count <= 0) {
                // console.log("exit");
                this.exit();
            }
        });
    }

    public realCount: number = 0;

    private render: SkeletalAnimation = null;

    mainCamera: Node = null;
    // 路径点
    pathPoints: Vec3[] = [];

    clientMgr: ClientMgr = null;

    isPause: boolean = false;

    nextPoint: Vec3 = null;

    isSelling: boolean = false;

    isWait: boolean = false;

    get needAsset() {
        return this.assetCount - this.realCount;
    }

    isPay: boolean = false;

    protected onLoad(): void {
        this.render = this.node.getChildByName("render").getComponent(SkeletalAnimation);
        this.mainCamera = find("Main Camera");
        this.render.getComponent(Collider).on("onTriggerEnter", this.onTriggerEnter, this);
        this.render.getComponent(Collider).on("onTriggerExit", this.onTriggerExit, this);
    }

    onTriggerEnter(event: ITriggerEvent) {
        const comp = event.otherCollider.node.parent.getComponent(Client);
        if (comp.assetType === this.assetType && this.needAsset > 0 && !this.isSelling && comp.isPause) {
            this.pause();
        }
    }

    onTriggerExit(event: ITriggerEvent) {
        const comp = event.otherCollider.node.parent.getComponent(Client);
        if (comp.assetType === this.assetType && this.needAsset > 0 && !this.isSelling && !comp.isPause) {
            this.resume();
        }
    }

    init(wps: Node[], clientMgr: ClientMgr, type: AssetsType) {
        this.pathPoints = wps.map(e => e.worldPosition);
        this.render.getComponent(Collider).enabled = true;
        this.isPay = false;
        this.assetCount = type === AssetsType.Wood ? 4 : 1;
        this.bar.node.parent.getChildByName("icon_wood").active = type === AssetsType.Wood;
        this.bar.node.parent.getChildByName("icon_gun").active = type === AssetsType.Gun;
        this.realCount = 0;
        this.curAssetCount = 0;
        this.smile.active = false;
        this.bar.node.parent.active = false;
        this.bar.fillRange = 0;
        this.render.node.setRotationFromEuler(0, 0, 0);
        this.assetType = type;
        this.tween = new Tween(this.node);
        this.playAnim("Walk");
        this.clientMgr = clientMgr;
        let time = 0;
        for (let i = 0; i < this.pathPoints.length - 1; i++) {
            const next_point = this.pathPoints[i + 1];
            const pre_point = this.pathPoints[i];
            const next = wps[i + 1];
            time = Vec3.distance(pre_point, next_point) / logicConfig.clientSpeed;
            this.tween.to(time, { worldPosition: next_point }, {
                onStart: () => {
                    this.nextPoint = next_point;
                    if (this.isPause) return;
                    const target = next_point.clone();
                    target.subtract(pre_point).normalize();
                    let angle = Math.atan2(-target.z, target.x) / Math.PI * 180 + 90;
                    this.render.node.setRotationFromEuler(0, angle, 0);
                },
                onComplete: () => {
                    if (next.name === "sell") {
                        // console.log("sell");
                        this.isSelling = true;
                        this.pause();
                        this.clientMgr.curClient = this;
                    } else if (next.name === "wait") {
                        if (this.clientMgr.curClient) {
                            // console.log("wait");
                            this.isWait = true;
                            this.pause();
                        } else {
                            this.render.getComponent(Collider).enabled = false;
                        }
                    }
                }
            })
        }
        this.tween.call(() => {
            this.clientMgr.recycle(this);

        })
        // this.tween.onFinally(() => {
        // })
        this.tween.start();
    }

    pause() {
        if (this.isPause) return;
        this.isPause = true;
        this.tween.pause();
        this.playAnim("idle");
    }

    resume() {
        if (!this.isPause || this.isSelling || (this.isWait && this.clientMgr.curClient)) return;
        this.isWait = false;
        this.isPause = false;
        this.tween.resume();
        this.playAnim("Walk");
        const target = this.nextPoint.clone();
        target.subtract(this.node.worldPosition).normalize();
        let angle = Math.atan2(-target.z, target.x) / Math.PI * 180 + 90;
        this.render.node.setRotationFromEuler(0, angle, 0);
    }

    exit() {
        // Global.uiMgr.collectCoin(this.ui.worldPosition, this.fishCount);
        // const wps = this.mainCamera.getComponent(Camera).worldToScreen(this.node.worldPosition);
        // console.log("exit1");
        this.isSelling = false;
        this.clientMgr.curClient = null;
        this.bar.node.parent.active = false;
        this.smile.active = true;
        this.resume();
        this.clientMgr.resume(this.assetType);
    }

    showProgress() {
        this.smile.active = false;
        this.bar.node.parent.active = true;
    }

    playAnim(anim: string) {
        this.render.crossFade(anim, 0.1);
    }

    protected lateUpdate(dt: number): void {
        // if (GameMgr.instance.isEnd) return;
        if (this.ui?.active) {
            this.ui.setRotationFromEuler(this.mainCamera.eulerAngles);
        }
    }

}
