import { _decorator, Component, Enum, Node, v3 } from 'cc';
import { EventBusSystem } from 'playable-fpframework';
import { GameEventType } from './Constants';
const { ccclass, property } = _decorator;

@ccclass('CollectAssets')
export class CollectAssets extends Component {
    @property
    interval: number = 0.1;

    list: Node[] = [];

    protected start(): void {
        this.node.on(Node.EventType.CHILD_ADDED, this.onChildAdded, this);
    }

    get nextAssetPosition() {
        return v3(0, this.list.length * this.interval, 0).add(this.node.worldPosition);
    }

    onChildAdded() {
        this.node.children.forEach((child, index) => {
            child.setPosition(v3(0, index * this.interval, 0));
        })
    }

    get asset() {
        return this.list.pop();
    }
}

