import { _decorator, Component, Node, Tween, v3 } from 'cc';
import { XTween } from 'playable-fpframework';
const { ccclass, property } = _decorator;

@ccclass('test')
export class test extends Component {
    start() {
        let number = 0;

        new Tween(this.node).to(5, {}, {
            onUpdate: () => {
                number++;
                console.log(number);
            }
        }).start();
    }

    update(deltaTime: number) {

    }
}

