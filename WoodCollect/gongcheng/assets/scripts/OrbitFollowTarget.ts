import { _decorator, Component, Node, Vec3, Quat, BitMask } from 'cc';
import { EDITOR_NOT_IN_PREVIEW } from 'cc/env';

const { ccclass, property, playOnFocus, executeInEditMode, disallowMultiple, menu } = _decorator;

// 枚举定义
export enum OrbitFollowMode {
    None = 0,
    Pitch = 2,
    Yaw = 4,
    Up = 8,
}

const QUARTER_PI = 0.25 * Math.PI;
const RADIAN_TO_DEGREE = 1 / Math.PI * 180;
const DEGREE_TO_RADIAN = Math.PI / 180;

const clamp = (value: number, min: number, max: number): number => {
    return value < min ? min : value > max ? max : value;
};

const fromToRotation = function (out: Quat, from: Vec3, to: Vec3) {
    const cross = Vec3.cross(new Vec3(), from, to);
    const angle = Vec3.angle(from, to);
    return Quat.fromAxisAngle(out, cross.normalize(), angle);
};

@ccclass('OrbitFollowTarget')
@executeInEditMode
@playOnFocus
@disallowMultiple
@menu('Camera/OrbitFollowTarget')
export class OrbitFollowTarget extends Component {
    // 属性定义
    @property({ serializable: true })
    private _preview: boolean = false;
    @property({
        displayName: '预览',
        tooltip: '开启/关闭编辑器预览',
    })
    get preview(): boolean {
        return this._preview;
    }
    set preview(value: boolean) {
        this._preview = value;
        this.computeLastPosition();
    }

    @property({
        type: Node,
        displayName: '目标',
        tooltip: '观察与跟随目标',
        visible: true,
    })
    private _target: Node | null = null;

    get target(): Vec3 | Node {
        return this._target !== null ? this._target.worldPosition : this.targetPosition;
    }

    set target(value: Vec3 | Node) {
        if (value instanceof Vec3) {
            this.targetPosition.set(value);
            this._target = null;
            this.targetRotation = null;
        } else {
            this._target = value;
        }
    }

    @property({
        type: BitMask(OrbitFollowMode),
        displayName: '跟随模式',
        tooltip: '跟随模式',
    })
    private followMode: OrbitFollowMode = OrbitFollowMode.None;


    @property({ serializable: true })
    private _polarAngle: number = QUARTER_PI;
    @property({
        slide: true,
        min: 0,
        max: 180,
        displayName: '极角',
        tooltip: '极角，与Y轴之间的夹角',
        visible: function (this: OrbitFollowTarget) {
            return !(this.followMode & OrbitFollowMode.Pitch);
        },
    })
    get polarAngle(): number {
        return this._polarAngle * RADIAN_TO_DEGREE;
    }

    set polarAngle(value: number) {
        this._polarAngle = value * DEGREE_TO_RADIAN;
    }

    @property({ serializable: true })
    private _azimuthAngle: number = 0;
    @property({
        slide: true,
        min: 0,
        max: 360,
        displayName: '方位角',
        tooltip: '方位角度，基于XZ平面的Y轴旋转',
        visible: function (this: OrbitFollowTarget) {
            return !(this.followMode & OrbitFollowMode.Yaw);
        },
    })
    get azimuthAngle(): number {
        return this._azimuthAngle * RADIAN_TO_DEGREE;
    }

    set azimuthAngle(value: number) {
        this._azimuthAngle = value * DEGREE_TO_RADIAN;
    }

    @property({ serializable: true })
    private _radialDistance: number = 15;
    @property({
        min: 0,
        displayName: '径向距离',
        tooltip: '也是球面半径，可以理解为与目标的距离',
    })
    get radialDistance(): number {
        return this._radialDistance;
    }
    set radialDistance(value: number) {
        this._radialDistance = value;
    }

    @property({ serializable: true })
    private _viewOffset: Vec3 = new Vec3();
    @property({
        visible: true,
        displayName: '视角偏移',
        tooltip: '相机视角的偏移',
    })
    get viewOffset(): Vec3 {
        return this._viewOffset;
    }

    set viewOffset(value: Vec3) {
        this._viewOffset.set(value.x, value.y, value.z);
    }

    @property({ serializable: true })
    private _dampingFactor: number = 0.1;
    @property({
        displayName: '阻尼系数',
        tooltip: '阻尼系数，相机平滑转动的插件数据，数值越小，转动越慢，数值越大，转动越快。',
        slide: true,
        min: 0.001,
        max: 1,
        step: 0.001,
    })
    get dampingFactor(): number {
        return this._dampingFactor;
    }
    set dampingFactor(value: number) {
        this._dampingFactor = clamp(value, 0.001, 1);
    }


    get viewOffsetX(): number {
        return this._viewOffset.x;
    }

    set viewOffsetX(value: number) {
        this._viewOffset.x = value;
    }

    get viewOffsetY(): number {
        return this._viewOffset.y;
    }

    set viewOffsetY(value: number) {
        this._viewOffset.y = value;
    }

    get viewOffsetZ(): number {
        return this._viewOffset.z;
    }

    set viewOffsetZ(value: number) {
        this._viewOffset.z = value;
    }


    get lookatPosition(): Vec3 {
        return this.lastPosition;
    }

    // 私有属性
    private targetPosition: Vec3 = new Vec3();
    private lastPosition: Vec3 = new Vec3();
    private targetRotation: Quat | null = null;

    // 生命周期方法
    onEnable() {
        this.computeLastPosition();
    }

    lateUpdate(dt: number) {
        if (EDITOR_NOT_IN_PREVIEW && !this.preview) return;
        this.smoothToTarget(this.dampingFactor);
    }

    // 公有方法
    forceSettle() {
        this.smoothToTarget(1);
    }

    // 私有方法
    private computeLastPosition() {
        const cameraPosition = this.computeCameraPosition(this.computeSphericalRotation());
        this.lastPosition.set(cameraPosition);
    }

    private computeLookatPosition(rotation?: Quat): Vec3 {
        const tempQuat = Quat.fromAxisAngle(new Quat(), Vec3.UP, this.getAzimuthAngle());
        if (this.followMode & OrbitFollowMode.Up && this.target instanceof Node) {
            const upRotation = fromToRotation(new Quat(), Vec3.UP, this.target.up);
            Quat.multiply(tempQuat, tempQuat, upRotation);
            if (rotation) {
                Quat.multiply(rotation, rotation, upRotation);
            }
        }
        const offset = Vec3.transformQuat(new Vec3(), this.viewOffset, tempQuat);
        return offset.add(this.getTargetPosition());
    }

    private computeCameraPosition(sphericalRotation: Quat): Vec3 {
        const lookatPosition = this.computeLookatPosition(sphericalRotation);
        const direction = Vec3.transformQuat(new Vec3(), new Vec3(0, 0, this.radialDistance), sphericalRotation);
        return lookatPosition.add(direction);
    }

    private computeSphericalRotation(): Quat {
        const quat = new Quat();
        const polarAngle = this.getPolarAngle();
        const azimuthAngle = this.getAzimuthAngle();
        const halfPolar = polarAngle * 0.5;
        const halfAzimuth = azimuthAngle * 0.5;

        const sinPolar = Math.sin(halfPolar);
        const cosPolar = Math.cos(halfPolar);
        const sinAzimuth = Math.sin(halfAzimuth);
        const cosAzimuth = Math.cos(halfAzimuth);

        quat.x = sinPolar * cosAzimuth;
        quat.y = cosPolar * sinAzimuth;
        quat.z = -sinPolar * sinAzimuth;
        quat.w = cosPolar * cosAzimuth;

        return quat;
    }

    private getPolarAngle(): number {
        return this.target instanceof Node && (this.followMode & OrbitFollowMode.Pitch)
            ? -this.target.eulerAngles.x * DEGREE_TO_RADIAN
            : this._polarAngle - Math.PI / 2;
    }

    private getAzimuthAngle(): number {
        return this.target instanceof Node && (this.followMode & OrbitFollowMode.Yaw)
            ? (180 + this.target.eulerAngles.y) * DEGREE_TO_RADIAN
            : this._azimuthAngle;
    }

    private getTargetPosition(): Vec3 {
        return this.target instanceof Node ? this.target.worldPosition : this.target;
    }

    private getTargetRotation(): Quat {
        return this.target instanceof Node ? this.target.worldRotation : Quat.IDENTITY;
    }

    private smoothToTarget(dampingFactor: number) {
        const sphericalRotation = this.computeSphericalRotation();
        const cameraPosition = this.computeCameraPosition(sphericalRotation);
        this.node.position = this.lastPosition.lerp(cameraPosition, dampingFactor);
        this.node.rotation = Quat.lerp(sphericalRotation, this.node.rotation, sphericalRotation, dampingFactor);
    }
}
