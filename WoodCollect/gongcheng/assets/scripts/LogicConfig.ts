import { v3 } from "cc";
import { fpplayable } from "playable-fpframework";


export const logicConfig = {
    speed: {
        name: "速度",
        default_value: 5,
        group: "角色",
        max: 10,
        min: 0,
        step: 0.01,
    },
    workerSpeed: {
        name: "工人速度",
        default_value: 3.5,
        group: "角色",
        max: 10,
        min: 0,
        step: 0.01,
    },
    flotageSpeed: {
        name: "木板漂浮速度",
        default_value: 4,
        group: "木板漂浮",
        max: 10,
        min: 0,
        step: 0.01,
    },

    clientSpeed: {
        name: "速度",
        default_value: 5,
        group: "顾客信息",
        max: 50,
        min: 1,
        step: 1,
    },
    clientInterval: {
        name: "间距",
        default_value: 1.2,
        group: "顾客信息",
        max: 50,
        min: 0,
        step: 0.01,
    },

    polarAngleLandscape: {
        name: "纵向角度",
        default_value: 43,
        group: "相机横屏信息",
        max: 180,
        min: 0,
        step: 1,
    },
    azimuthAngleLandscape: {
        name: "横向角度",
        default_value: 45,
        group: "相机横屏信息",
        max: 180,
        min: -180,
        step: 1,
    },
    radiusLandscape: {
        name: "视距",
        default_value: 11.8,
        group: "相机横屏信息",
        max: 100,
        min: 0,
        step: 0.1,
    },
    viewOffsetLandscape: {
        name: "偏移",
        default_value: v3(0, 0, 0),
        group: "相机横屏信息",
    },

    polarAnglePortrait: {
        name: "纵向角度",
        default_value: 45,
        group: "相机竖屏信息",
        max: 180,
        min: 0,
        step: 1,
    },
    azimuthAnglePortrait: {
        name: "横向角度",
        default_value: 46,
        group: "相机竖屏信息",
        max: 180,
        min: -180,
        step: 1,
    },
    radiusPortrait: {
        name: "视距",
        default_value: 26,
        group: "相机竖屏信息",
        max: 100,
        min: 0,
        step: 0.1,
    },
    viewOffsetPortrait: {
        name: "偏移",
        default_value: v3(0, 0, 0),
        group: "相机竖屏信息",
    },
} as any;

fpplayable.addParameter(logicConfig, "MO_CollectWOODSLG");