  //混合模块
  #pragma define-meta BLEND_METHOD options([normal,addition,multiply])
    
  #define normal(o,add) o = vec4(add[0] * add[3] + o[0] * (1.0-add[3]),add[1] * add[3] + o[1] * (1.0-add[3]) ,add[2] * add[3] + o[2] * (1.0-add[3]), o[3]);
  #define addition(o,add) o = vec4(add[0] + o[0] ,add[1] + o[1] , add[2] + o[2] , o[3]);  
  #define multiply(o,add) o = vec4(add[0] * o[0] ,add[1] * o[1] , add[2] * o[2] , o[3]);
