  precision highp float;
  #include <cc-global>
  #if USE_LOCAL
    #include <cc-local>
  #endif
  
  #if SAMPLE_FROM_RT
    #include <common/common-define>
    //老版本引用
    //#include <common>
  #endif
  
  in vec3 a_position;
  in vec2 a_texCoord;
  in vec4 a_color;

  out vec4 i_color;
  out vec2 uv0;
  out vec2 uv1;
  out vec3 v_position;

  vec4 vert () {
    vec4 pos = vec4(a_position, 1);

    #if USE_LOCAL
      pos = cc_matWorld * pos;
    #endif

    #if USE_PIXEL_ALIGNMENT
      pos = cc_matView * pos;
      pos.xyz = floor(pos.xyz);
      pos = cc_matProj * pos;
    #else
      pos = cc_matViewProj * pos;
    #endif

    uv0 = a_texCoord;
    #if SAMPLE_FROM_RT
      uv1 = uv0;
      CC_HANDLE_RT_SAMPLE_FLIP(uv1);
    #endif
    i_color = a_color;
    return pos;
  }