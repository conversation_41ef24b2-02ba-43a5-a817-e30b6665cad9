'use client';

import React, { useEffect, useRef } from 'react';
import { Engine, Scene } from '@babylonjs/core';
import { onSceneReady, onRender, getGameState, testWoodStack, resetGame } from '../../components/babylon/scenes/WoodCollectScene3D';

export default function TestWoodCollectionPage() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const engineRef = useRef<Engine | null>(null);
  const sceneRef = useRef<Scene | null>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    // 创建Babylon.js引擎
    const engine = new Engine(canvasRef.current, true);
    engineRef.current = engine;

    // 创建场景
    const scene = new Scene(engine);
    sceneRef.current = scene;

    // 初始化场景
    onSceneReady(scene).then(() => {
      console.log('[TestWoodCollection] 场景初始化完成');
    });

    // 启动渲染循环
    engine.runRenderLoop(() => {
      onRender();
      scene.render();
    });

    // 处理窗口大小变化
    const handleResize = () => {
      engine.resize();
    };
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      scene.dispose();
      engine.dispose();
    };
  }, []);

  const handleAddWood = () => {
    testWoodStack(5);
    console.log('[TestWoodCollection] 添加5个木材，当前状态:', getGameState());
  };

  const handleRemoveWood = () => {
    testWoodStack(-5);
    console.log('[TestWoodCollection] 移除5个木材，当前状态:', getGameState());
  };

  const handleResetGame = () => {
    resetGame();
    console.log('[TestWoodCollection] 游戏已重置，当前状态:', getGameState());
  };

  const handleShowGameState = () => {
    const state = getGameState();
    console.log('[TestWoodCollection] 当前游戏状态:', state);
    alert(`游戏状态:\n金币: ${state.coins}\n木材: ${state.wood}\n客户: ${state.customers}\n工人: ${state.workers}`);
  };

  return (
    <div className="w-full h-screen flex flex-col">
      <div className="bg-gray-800 text-white p-4">
        <h1 className="text-2xl font-bold mb-4">木材收集系统测试</h1>
        <div className="flex gap-4 flex-wrap">
          <button
            onClick={handleAddWood}
            className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded"
          >
            添加5个木材
          </button>
          <button
            onClick={handleRemoveWood}
            className="bg-red-600 hover:bg-red-700 px-4 py-2 rounded"
          >
            移除5个木材
          </button>
          <button
            onClick={handleResetGame}
            className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded"
          >
            重置游戏
          </button>
          <button
            onClick={handleShowGameState}
            className="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded"
          >
            显示游戏状态
          </button>
        </div>
        <div className="mt-4 text-sm text-gray-300">
          <p>• 使用WASD或点击屏幕拖拽虚拟摇杆控制角色移动</p>
          <p>• 解锁工厂后，靠近工厂区域会自动收集木材</p>
          <p>• 木材会堆叠在角色背后（稍微偏左，避免与金币重叠）</p>
          <p>• 每100毫秒收集1个木材，使用贝塞尔曲线飞行动画</p>
        </div>
      </div>
      <canvas
        ref={canvasRef}
        className="flex-1 w-full"
        style={{ outline: 'none' }}
        tabIndex={0}
      />
    </div>
  );
}
