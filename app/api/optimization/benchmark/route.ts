import { NextRequest, NextResponse } from 'next/server'
import { PlayableGenAPI } from '@/src/agents/PlayableGenAPI'

// 创建PlayableGenAPI实例
const playableGenAPI = new PlayableGenAPI({
  outputDir: './generated-games',
  enableLogging: true,
  codeGeneration: {
    framework: "babylon.js",
    outputFormat: "html",
    includeAssets: true,
    minify: false
  },
  optimization: {
    enableCaching: true,
    enableTemplateSelection: true,
    enableAccuracyValidation: true,
    enablePerformanceOptimization: true,
    maxIterations: 3,
    qualityThreshold: 75,
    tokenLimit: 20000
  }
})

export async function POST(request: NextRequest) {
  try {
    console.log('开始运行基准测试...')

    // 运行基准测试
    const benchmarkResult = await playableGenAPI.runBenchmark()

    console.log('基准测试完成:', benchmarkResult)

    return NextResponse.json(benchmarkResult)

  } catch (error) {
    console.error('基准测试API错误:', error)
    
    return NextResponse.json(
      { 
        error: '基准测试失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // 获取基准测试状态或历史结果
    return NextResponse.json({
      status: 'ready',
      message: '基准测试系统就绪'
    })

  } catch (error) {
    console.error('获取基准测试状态错误:', error)
    
    return NextResponse.json(
      { 
        error: '获取状态失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
