import { NextRequest, NextResponse } from 'next/server'
import { PlayableGenAPI } from '@/src/agents/PlayableGenAPI'

// 创建PlayableGenAPI实例
const playableGenAPI = new PlayableGenAPI({
  outputDir: './generated-games',
  enableLogging: true,
  codeGeneration: {
    framework: "babylon.js",
    outputFormat: "html",
    includeAssets: true,
    minify: false
  },
  optimization: {
    enableCaching: true,
    enableTemplateSelection: true,
    enableAccuracyValidation: true,
    enablePerformanceOptimization: true,
    maxIterations: 3,
    qualityThreshold: 75,
    tokenLimit: 20000
  }
})

export async function POST(request: NextRequest) {
  try {
    console.log('重置优化统计数据...')

    // 重置优化统计
    playableGenAPI.resetOptimizationStats()

    console.log('优化统计数据已重置')

    return NextResponse.json({
      success: true,
      message: '优化统计数据已重置'
    })

  } catch (error) {
    console.error('重置优化统计API错误:', error)
    
    return NextResponse.json(
      { 
        error: '重置统计数据失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // 获取重置状态
    return NextResponse.json({
      status: 'ready',
      message: '重置功能就绪'
    })

  } catch (error) {
    console.error('获取重置状态错误:', error)
    
    return NextResponse.json(
      { 
        error: '获取状态失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
