import { NextRequest, NextResponse } from 'next/server'
import { PlayableGenAPI } from '@/src/agents/PlayableGenAPI'

// 创建PlayableGenAPI实例
const playableGenAPI = new PlayableGenAPI({
  outputDir: './generated-games',
  enableLogging: true,
  codeGeneration: {
    framework: "babylon.js",
    outputFormat: "html",
    includeAssets: true,
    minify: false
  },
  optimization: {
    enableCaching: true,
    enableTemplateSelection: true,
    enableAccuracyValidation: true,
    enablePerformanceOptimization: true,
    maxIterations: 3,
    qualityThreshold: 75,
    tokenLimit: 20000
  }
})

export async function GET(request: NextRequest) {
  try {
    console.log('获取优化统计数据...')

    // 获取优化性能统计
    const optimizationStats = playableGenAPI.getOptimizationStats()

    console.log('优化统计数据:', optimizationStats)

    return NextResponse.json(optimizationStats)

  } catch (error) {
    console.error('获取优化统计API错误:', error)
    
    return NextResponse.json(
      { 
        error: '获取统计数据失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { config } = body

    if (config) {
      // 更新优化配置
      playableGenAPI.updateOptimizationConfig(config)
      
      return NextResponse.json({
        success: true,
        message: '优化配置已更新',
        config: playableGenAPI.getOptimizationConfig()
      })
    }

    return NextResponse.json(
      { error: '缺少配置参数' },
      { status: 400 }
    )

  } catch (error) {
    console.error('更新优化配置API错误:', error)
    
    return NextResponse.json(
      { 
        error: '更新配置失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
