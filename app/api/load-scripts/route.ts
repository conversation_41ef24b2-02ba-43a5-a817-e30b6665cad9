import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    const scriptsDir = path.join(process.cwd(), 'generated-scripts');
    
    // 检查目录是否存在
    if (!fs.existsSync(scriptsDir)) {
      return NextResponse.json({
        success: true,
        data: {
          scripts: []
        }
      });
    }

    const files = fs.readdirSync(scriptsDir);
    const scripts = [];

    // 处理每个脚本文件
    for (const file of files) {
      if (file.endsWith('.js')) {
        const scriptPath = path.join(scriptsDir, file);
        const metadataFile = file.replace('.js', '_metadata.json');
        const metadataPath = path.join(scriptsDir, metadataFile);

        try {
          // 读取脚本内容
          const content = fs.readFileSync(scriptPath, 'utf-8');
          
          // 读取元数据
          let metadata = {
            description: '场景节点脚本',
            targetNodeTypes: ['mesh'],
            dependencies: ['NodeRegistry', 'NodeCommunicationService'],
            functionType: 'animation' as const,
            userRequirement: '',
            generatedAt: new Date().toISOString(),
            sceneContext: null,
            type: 'scene_script'
          };

          if (fs.existsSync(metadataPath)) {
            const metadataContent = fs.readFileSync(metadataPath, 'utf-8');
            const parsedMetadata = JSON.parse(metadataContent);
            metadata = { ...metadata, ...parsedMetadata };
          }

          // 从文件名提取脚本ID和名称
          const fileNameParts = file.replace('.js', '').split('_');
          const scriptId = fileNameParts.length >= 2 ? fileNameParts[1] : Date.now().toString();
          const scriptName = fileNameParts.length >= 3 ? fileNameParts.slice(2).join('_') : '未命名脚本';

          // 获取文件时间
          const stats = fs.statSync(scriptPath);

          const script = {
            id: `script_${scriptId}`,
            name: scriptName,
            content,
            createdAt: stats.birthtime.toISOString(),
            lastModified: stats.mtime.toISOString(),
            isActive: false,
            metadata: {
              description: metadata.description,
              targetNodeTypes: metadata.targetNodeTypes,
              dependencies: metadata.dependencies,
              functionType: metadata.functionType
            }
          };

          scripts.push(script);
        } catch (error) {
          console.error(`加载脚本文件失败: ${file}`, error);
          // 继续处理其他文件，不因单个文件错误而失败
        }
      }
    }

    // 按创建时间倒序排列
    scripts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    console.log(`[API] 成功加载 ${scripts.length} 个脚本文件`);

    return NextResponse.json({
      success: true,
      data: {
        scripts,
        count: scripts.length
      }
    });

  } catch (error) {
    console.error('[API] 加载脚本文件失败:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '加载脚本文件失败'
    }, { status: 500 });
  }
} 