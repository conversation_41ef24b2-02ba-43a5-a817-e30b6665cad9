import { NextRequest, NextResponse } from 'next/server';
import { ScriptIterationAgent, IterationRequest, IterationResult } from '../../../src/agents/ScriptIterationAgent';
import fs from 'fs/promises';
import path from 'path';

// 全局脚本迭代代理实例
let scriptIterationAgent: ScriptIterationAgent | null = null;

function getScriptIterationAgent(): ScriptIterationAgent {
  if (!scriptIterationAgent) {
    scriptIterationAgent = new ScriptIterationAgent();
  }
  return scriptIterationAgent;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, ...params } = body;

    console.log(`[ScriptIteration] 接收到请求: ${action}`);

    const agent = getScriptIterationAgent();

    switch (action) {
      case 'initialize':
        return await handleInitialize(agent, params);
      case 'iterate':
        return await handleIterate(agent, params);
      case 'getContext':
        return await handleGetContext(agent, params);
      default:
        return NextResponse.json({
          success: false,
          error: `未知的操作类型: ${action}`
        }, { status: 400 });
    }
  } catch (error) {
    console.error('[ScriptIteration] API错误:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

/**
 * 初始化脚本上下文
 */
async function handleInitialize(
  agent: ScriptIterationAgent, 
  params: { scriptId: string; scriptContent: string; metadata: Record<string, unknown> }
) {
  const { scriptId, scriptContent, metadata } = params;

  if (!scriptId || !scriptContent) {
    return NextResponse.json({
      success: false,
      error: '缺少必要参数：scriptId或scriptContent'
    }, { status: 400 });
  }

  try {
    await agent.initializeContext(scriptId, scriptContent, metadata);

    console.log(`[ScriptIteration] 脚本上下文初始化完成: ${scriptId}`);

    return NextResponse.json({
      success: true,
      message: '脚本上下文初始化成功',
      data: {
        scriptId,
        initialized: true,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('[ScriptIteration] 初始化失败:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '初始化失败'
    }, { status: 500 });
  }
}

/**
 * 执行脚本迭代
 */
async function handleIterate(
  agent: ScriptIterationAgent, 
  params: IterationRequest
) {
  const { userRequest, scriptId, iterationType, priority, expectedBehavior } = params;

  if (!userRequest || !scriptId || !iterationType) {
    return NextResponse.json({
      success: false,
      error: '缺少必要参数：userRequest、scriptId或iterationType'
    }, { status: 400 });
  }

  try {
    const startTime = Date.now();

    console.log(`[ScriptIteration] 开始迭代脚本: ${scriptId}, 类型: ${iterationType}`);

    // 执行迭代
    const result = await agent.iterateScript({
      userRequest,
      scriptId,
      iterationType: iterationType as 'enhance' | 'fix' | 'refactor' | 'extend',
      priority: priority as 'low' | 'medium' | 'high' || 'medium',
      expectedBehavior
    });

    const duration = Date.now() - startTime;

    if (result.success) {
      // 保存更新的脚本
      await saveUpdatedScript(scriptId, result);
      
      console.log(`[ScriptIteration] 脚本迭代成功: v${result.newVersion}, 耗时: ${duration}ms`);
    } else {
      console.log(`[ScriptIteration] 脚本迭代失败: ${result.errorMessage}`);
    }

    return NextResponse.json({
      success: result.success,
      data: result,
      duration,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[ScriptIteration] 迭代失败:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '迭代失败'
    }, { status: 500 });
  }
}

/**
 * 获取脚本上下文
 */
async function handleGetContext(
  agent: ScriptIterationAgent, 
  params: { scriptId: string }
) {
  const { scriptId } = params;

  if (!scriptId) {
    return NextResponse.json({
      success: false,
      error: '缺少scriptId参数'
    }, { status: 400 });
  }

  try {
    const context = await agent.getScriptContext(scriptId);

    if (!context) {
      return NextResponse.json({
        success: false,
        error: '脚本上下文不存在'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: context
    });

  } catch (error) {
    console.error('[ScriptIteration] 获取上下文失败:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取上下文失败'
    }, { status: 500 });
  }
}

/**
 * 保存更新的脚本
 */
async function saveUpdatedScript(scriptId: string, result: IterationResult): Promise<void> {
  try {
    const scriptsDir = path.join(process.cwd(), 'generated-scripts');
    await fs.mkdir(scriptsDir, { recursive: true });

    // 查找现有脚本文件
    const files = await fs.readdir(scriptsDir);
    const scriptFile = files.find(file => file.startsWith(scriptId) && file.endsWith('.js'));
    
    if (!scriptFile) {
      console.warn(`[ScriptIteration] 未找到脚本文件: ${scriptId}`);
      return;
    }

    // 更新脚本文件
    const scriptPath = path.join(scriptsDir, scriptFile);
    const updatedContent = `/**
 * 脚本版本: v${result.newVersion}
 * 最后更新: ${new Date().toLocaleString()}
 * 变更说明: ${result.updatedScript.changes}
 * 修改原因: ${result.updatedScript.reasoning}
 */

${result.updatedScript.content}`;

    await fs.writeFile(scriptPath, updatedContent, 'utf-8');

    // 更新元数据文件
    const metadataFile = scriptFile.replace('.js', '_metadata.json');
    const metadataPath = path.join(scriptsDir, metadataFile);
    
    try {
      const existingMetadata = await fs.readFile(metadataPath, 'utf-8');
      const metadata = JSON.parse(existingMetadata);
      
      metadata.version = result.newVersion;
      metadata.lastUpdated = new Date().toISOString();
      metadata.iterationHistory = metadata.iterationHistory || [];
      metadata.iterationHistory.push({
        version: result.newVersion,
        timestamp: new Date().toISOString(),
        changes: result.updatedScript.changes,
        reasoning: result.updatedScript.reasoning
      });

      await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2), 'utf-8');
    } catch {
      // 如果元数据文件不存在，创建新的
      const metadata = {
        scriptId,
        version: result.newVersion,
        lastUpdated: new Date().toISOString(),
        iterationHistory: [{
          version: result.newVersion,
          timestamp: new Date().toISOString(),
          changes: result.updatedScript.changes,
          reasoning: result.updatedScript.reasoning
        }]
      };

      await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2), 'utf-8');
    }

    console.log(`[ScriptIteration] 脚本已保存: ${scriptPath}`);

  } catch (error) {
    console.error('[ScriptIteration] 保存脚本失败:', error);
    throw error;
  }
} 