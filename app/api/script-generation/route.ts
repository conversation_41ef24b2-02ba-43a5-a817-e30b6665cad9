import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { userRequirement, sceneContext, streamMode = false } = await request.json();

    if (!userRequirement || !userRequirement.trim()) {
      return NextResponse.json({
        success: false,
        error: '用户需求不能为空'
      }, { status: 400 });
    }

    console.log(`[API] 脚本生成请求: ${userRequirement.substring(0, 100)}...`);
    console.log(`[API] 场景上下文:`, sceneContext ? '有' : '无');
    console.log(`[API] 流式模式: ${streamMode}`);

    // 动态导入避免构建时包含到客户端bundle
    const { scriptGenerationGraph } = await import('../../../src/langgraph/agents/script-generation');

    const startTime = Date.now();

    if (streamMode) {
      // 流式响应模式
      const encoder = new TextEncoder();
      
      const stream = new ReadableStream({
        async start(controller) {
          try {
            // 构建初始状态
            const initialState = {
              userRequirement,
              sceneContext,
              taskType: 'script_generation',
              currentStep: 'analysis' as const,
              status: 'idle' as const
            };

            console.log('[API] 开始流式脚本生成...');

            // 流式执行工作流
            const threadId = `script_gen_${Date.now()}`;
            let finalState: Record<string, unknown> = {};
            
            const workflowStream = await scriptGenerationGraph.stream(
              initialState,
              {
                configurable: { thread_id: threadId }
              }
            );

            for await (const chunk of workflowStream) {
              // LangGraph流式数据格式：{ "node_name": node_output }
              // 保存最新状态 - 合并所有节点的输出
              if (chunk && typeof chunk === 'object') {
                const chunkObj = chunk as Record<string, unknown>;
                const nodeNames = Object.keys(chunkObj);
                for (const nodeName of nodeNames) {
                  const nodeOutput = chunkObj[nodeName];
                  if (nodeOutput && typeof nodeOutput === 'object' && nodeOutput !== null) {
                    finalState = { 
                      ...finalState, 
                      ...(nodeOutput as Record<string, unknown>) 
                    };
                  }
                }
              }
              
              // 发送流式更新
              const data = JSON.stringify({
                type: 'update',
                data: chunk
              });
              controller.enqueue(encoder.encode(`data: ${data}\n\n`));
            }

            const duration = Date.now() - startTime;

            // 调试：输出最终状态
            console.log('[API] 最终状态:', JSON.stringify(finalState, null, 2));

            // 发送完成信号
            const completeData = JSON.stringify({
              type: 'complete',
              data: {
                success: (finalState.status as string) === 'completed',
                generatedScript: finalState.generatedScript || null,
                status: (finalState.status as string) || 'error',
                duration,
                metadata: finalState.metadata || {}
              }
            });
            controller.enqueue(encoder.encode(`data: ${completeData}\n\n`));
            controller.close();

          } catch (error) {
            console.error('[API] 流式脚本生成失败:', error);
            
            const errorData = JSON.stringify({
              type: 'error',
              data: {
                success: false,
                error: error instanceof Error ? error.message : '未知错误'
              }
            });
            controller.enqueue(encoder.encode(`data: ${errorData}\n\n`));
            controller.close();
          }
        }
      });

      return new Response(stream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      });

    } else {
      // 普通响应模式
      const initialState = {
        userRequirement,
        sceneContext,
        taskType: 'script_generation',
        currentStep: 'analysis' as const,
        status: 'idle' as const
      };

      const result = await scriptGenerationGraph.invoke(initialState, {
        configurable: { thread_id: `script_gen_${Date.now()}` }
      });

      const duration = Date.now() - startTime;
      console.log(`[API] 脚本生成完成，耗时: ${duration}ms`);

      return NextResponse.json({
        success: result.status === 'completed',
        data: result.generatedScript,
        status: result.status,
        duration,
        metadata: result.metadata,
        messages: result.messages?.length || 0
      });
    }

  } catch (error) {
    console.error('[API] 脚本生成失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    return NextResponse.json({
      service: 'script-generation',
      status: 'running',
      version: '1.0.0',
      capabilities: [
        'script_generation',
        'scene_context_analysis',
        'streaming_output',
        'multiple_function_types'
      ]
    });

  } catch (error) {
    console.error('[API] 获取服务状态失败:', error);
    
    return NextResponse.json({
      error: '获取服务状态失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
} 