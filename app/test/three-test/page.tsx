/**
 * Three.js测试页面
 * 验证Three.js组件和FBX加载器是否正常工作
 */

'use client';

import React, { useCallback, useState } from 'react';
import * as THREE from 'three';
import dynamic from 'next/dynamic';

// 动态导入ThreeCanvas以避免SSR问题
const ThreeCanvas = dynamic(
  () => import('../../../src/three/components/ThreeCanvas').then(mod => ({ default: mod.ThreeCanvas })),
  {
    ssr: false,
    loading: () => <div className="flex items-center justify-center h-full bg-gray-100">
      <div className="text-center">
        <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
        <p className="text-sm text-gray-600">加载Three.js场景...</p>
      </div>
    </div>
  }
);

import type { ThreeCanvasContext } from '../../../src/three/components/ThreeCanvas';
import { ThreeUtils } from '../../../src/three/core/ThreeUtils';
import { fbxModelLoader } from '../../../src/three/loaders/FBXModelLoader';

export default function ThreeTestPage(): React.JSX.Element {
  const [info, setInfo] = useState<string>('Three.js测试页面已加载');
  const [modelCount, setModelCount] = useState<number>(0);

  // Three.js初始化回调
  const handleThreeInitialized = useCallback((context: ThreeCanvasContext) => {
    console.log('[ThreeTest] Three.js初始化完成');
    setInfo('Three.js场景初始化完成');

    const { scene, camera, renderer } = context;

    // 创建一些测试对象
    const geometries = ThreeUtils.createBasicGeometries();
    
    // 添加立方体
    const boxGeometry = geometries.box(1, 1, 1);
    const boxMaterial = ThreeUtils.createStandardMaterial({ color: 0x00ff00 });
    const boxMesh = ThreeUtils.createMesh(boxGeometry, boxMaterial, {
      name: 'TestBox',
      position: new THREE.Vector3(-2, 0.5, 0)
    });
    scene.add(boxMesh);

    // 添加球体
    const sphereGeometry = geometries.sphere(0.5);
    const sphereMaterial = ThreeUtils.createStandardMaterial({ color: 0xff0000 });
    const sphereMesh = ThreeUtils.createMesh(sphereGeometry, sphereMaterial, {
      name: 'TestSphere',
      position: new THREE.Vector3(2, 0.5, 0)
    });
    scene.add(sphereMesh);

    // 添加地面
    const ground = ThreeUtils.createGround(10, 0x808080);
    scene.add(ground);

    // 添加网格辅助线
    const gridHelper = ThreeUtils.createGridHelper(10, 10);
    scene.add(gridHelper);

    setInfo('测试对象创建完成：立方体、球体、地面');
  }, []);

  // 渲染循环回调
  const handleRender = useCallback((context: ThreeCanvasContext, deltaTime: number) => {
    // 旋转测试对象
    const box = context.scene.getObjectByName('TestBox');
    const sphere = context.scene.getObjectByName('TestSphere');
    
    if (box) {
      box.rotation.y += deltaTime;
    }
    
    if (sphere) {
      sphere.rotation.x += deltaTime * 0.5;
      sphere.rotation.z += deltaTime * 0.3;
    }
  }, []);

  // 添加随机对象
  const addRandomObject = useCallback(() => {
    // 这里需要访问Three.js上下文，实际实现中可以通过ref或context获取
    console.log('[ThreeTest] 添加随机对象');
    setInfo('添加随机对象功能需要在实际场景中实现');
  }, []);

  // 测试FBX加载
  const testFBXLoader = useCallback(async () => {
    try {
      setInfo('FBX加载器测试：准备中...');
      
      // 创建一个简单的测试用FBX文件URL（实际项目中需要真实的FBX文件）
      const testUrl = '/models/test.fbx'; // 这个文件需要存在
      
      const model = await fbxModelLoader.loadModel({
        url: testUrl,
        autoPlayAnimation: true,
        enableShadows: true,
        onProgress: (progress) => {
          console.log('[ThreeTest] FBX加载进度:', progress);
        },
        onError: (error) => {
          console.error('[ThreeTest] FBX加载失败:', error);
          setInfo('FBX加载失败：文件不存在或格式错误');
        }
      });

      setModelCount(prev => prev + 1);
      setInfo(`FBX模型加载成功！已加载 ${modelCount + 1} 个模型`);
      
    } catch (error) {
      console.error('[ThreeTest] FBX测试失败:', error);
      setInfo('FBX加载器测试失败：' + (error instanceof Error ? error.message : '未知错误'));
    }
  }, [modelCount]);

  // 错误处理
  const handleError = useCallback((error: Error) => {
    console.error('[ThreeTest] Three.js错误:', error);
    setInfo('Three.js错误：' + error.message);
  }, []);

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col">
      {/* 头部信息 */}
      <div className="bg-white shadow-sm border-b p-4">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Three.js 技术栈测试</h1>
        <p className="text-sm text-gray-600 mb-2">验证Three.js组件、工具函数和FBX加载器</p>
        <div className="bg-blue-50 border border-blue-200 rounded p-2">
          <p className="text-sm text-blue-800">{info}</p>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex">
        {/* 左侧控制面板 */}
        <div className="w-80 bg-white border-r border-gray-200 p-4 space-y-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">测试控制</h3>
            
            <div className="space-y-2">
              <button
                onClick={addRandomObject}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors"
              >
                添加随机对象
              </button>
              
              <button
                onClick={testFBXLoader}
                className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors"
              >
                测试FBX加载器
              </button>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">技术信息</h3>
            <div className="space-y-2 text-sm">
              <div className="bg-gray-50 p-2 rounded">
                <strong>Three.js版本:</strong> 0.178.0
              </div>
              <div className="bg-gray-50 p-2 rounded">
                <strong>渲染器:</strong> WebGL
              </div>
              <div className="bg-gray-50 p-2 rounded">
                <strong>已加载模型:</strong> {modelCount}
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">测试项目</h3>
            <div className="space-y-1 text-sm">
              <div className="flex items-center">
                <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                Three.js引擎初始化
              </div>
              <div className="flex items-center">
                <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                基础几何体创建
              </div>
              <div className="flex items-center">
                <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                材质和光照系统
              </div>
              <div className="flex items-center">
                <span className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                FBX模型加载
              </div>
              <div className="flex items-center">
                <span className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                动画系统
              </div>
            </div>
          </div>
        </div>

        {/* 右侧Three.js画布 */}
        <div className="flex-1 relative">
          <ThreeCanvas
            className="w-full h-full"
            engineId="three-test"
            debugMode={true}
            backgroundColor={0x87CEEB}
            enableShadows={true}
            enableFog={true}
            onInitialized={handleThreeInitialized}
            onRender={handleRender}
            onError={handleError}
          />
          
          {/* 画布覆盖信息 */}
          <div className="absolute top-4 right-4 bg-black bg-opacity-50 text-white px-3 py-2 rounded text-sm">
            <div>引擎: Three.js 0.178.0</div>
            <div>状态: 运行中</div>
            <div>对象: 旋转测试</div>
          </div>
        </div>
      </div>
    </div>
  );
}
