/**
 * NodeSystemContext - 节点系统状态管理
 * 提供全局状态管理和共享逻辑
 */

'use client';

import React, { createContext, useContext, useState, useRef, useCallback, ReactNode } from 'react';
import * as THREE from 'three';
import { ThreeCanvasContext } from '../../../../src/three/components/ThreeCanvas';
import { FBXModel } from '../../../../src/three/loaders/FBXModelLoader';
import { GameNodeProperties, GameNodeType } from '../../../../src/types/NodeTypes';
import { NodeCommunicationService } from '../../../../src/utils/NodeCommunicationService';

// 类型定义
export interface AgentMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
}

export interface ScriptFile {
  id: string;
  name: string;
  content: string;
  nodeId?: string;
  createdAt: Date;
  lastModified: Date;
  isActive: boolean;
  metadata: {
    description: string;
    targetNodeTypes: string[];
    dependencies: string[];
  };
}

export interface NodeSystemState {
  // 节点系统状态
  nodes: GameNodeProperties[];
  selectedNode: GameNodeProperties | null;
  nodeStats: { total: number; byType: Record<string, number> };
  
  // Agent对话状态
  agentMessages: AgentMessage[];
  agentInput: string;
  isAgentProcessing: boolean;
  isLoadingHistory: boolean;
  currentSessionId: string;
  
  // 脚本管理状态
  scriptFiles: ScriptFile[];
  selectedScript: ScriptFile | null;
  editingScriptId: string | null;
  editingScriptName: string;
  
  // 模型上传状态
  isUploadingModel: boolean;
  uploadError: string | null;
  isDragOver: boolean;
  
  // Three.js相关状态
  threeContext: ThreeCanvasContext | null;
  loadedModels: Map<string, FBXModel>;
  
  // 临时输入状态
  tempInputValues: {
    positionX?: string;
    positionY?: string;
    positionZ?: string;
    rotationX?: string;
    rotationY?: string;
    rotationZ?: string;
    scaleX?: string;
    scaleY?: string;
    scaleZ?: string;
  };
}

export interface NodeSystemActions {
  // 节点操作
  setNodes: (nodes: GameNodeProperties[]) => void;
  setSelectedNode: (node: GameNodeProperties | null) => void;
  setNodeStats: (stats: { total: number; byType: Record<string, number> }) => void;
  
  // Agent操作
  setAgentMessages: (messages: AgentMessage[]) => void;
  setAgentInput: (input: string) => void;
  setIsAgentProcessing: (processing: boolean) => void;
  setIsLoadingHistory: (loading: boolean) => void;
  setCurrentSessionId: (sessionId: string) => void;
  addAgentMessage: (message: AgentMessage) => void;
  
  // 脚本操作
  setScriptFiles: (scripts: ScriptFile[]) => void;
  setSelectedScript: (script: ScriptFile | null) => void;
  setEditingScriptId: (id: string | null) => void;
  setEditingScriptName: (name: string) => void;
  addScriptFile: (script: ScriptFile) => void;
  updateScriptFile: (id: string, updates: Partial<ScriptFile>) => void;
  removeScriptFile: (id: string) => void;
  
  // 模型上传操作
  setIsUploadingModel: (uploading: boolean) => void;
  setUploadError: (error: string | null) => void;
  setIsDragOver: (dragOver: boolean) => void;
  
  // Three.js操作
  setThreeContext: (context: ThreeCanvasContext | null) => void;
  addLoadedModel: (id: string, model: FBXModel) => void;
  removeLoadedModel: (id: string) => void;
  
  // 临时输入操作
  setTempInputValues: (values: Partial<NodeSystemState['tempInputValues']>) => void;
  
  // 工具方法
  generateSessionId: () => string;
  scrollToBottom: () => void;
}

const NodeSystemContext = createContext<(NodeSystemState & NodeSystemActions) | null>(null);

export const useNodeSystem = () => {
  const context = useContext(NodeSystemContext);
  if (!context) {
    throw new Error('useNodeSystem must be used within a NodeSystemProvider');
  }
  return context;
};

interface NodeSystemProviderProps {
  children: ReactNode;
}

export const NodeSystemProvider: React.FC<NodeSystemProviderProps> = ({ children }) => {
  // 状态定义
  const [nodes, setNodes] = useState<GameNodeProperties[]>([]);
  const [selectedNode, setSelectedNode] = useState<GameNodeProperties | null>(null);
  const [nodeStats, setNodeStats] = useState<{ total: number; byType: Record<string, number> }>({ 
    total: 0, 
    byType: {} 
  });
  
  const [agentMessages, setAgentMessages] = useState<AgentMessage[]>([]);
  const [agentInput, setAgentInput] = useState<string>('');
  const [isAgentProcessing, setIsAgentProcessing] = useState<boolean>(false);
  const [isLoadingHistory, setIsLoadingHistory] = useState<boolean>(true);
  const [currentSessionId, setCurrentSessionId] = useState<string>('');
  
  const [scriptFiles, setScriptFiles] = useState<ScriptFile[]>([]);
  const [selectedScript, setSelectedScript] = useState<ScriptFile | null>(null);
  const [editingScriptId, setEditingScriptId] = useState<string | null>(null);
  const [editingScriptName, setEditingScriptName] = useState<string>('');
  
  const [isUploadingModel, setIsUploadingModel] = useState<boolean>(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [isDragOver, setIsDragOver] = useState<boolean>(false);
  
  const [threeContext, setThreeContext] = useState<ThreeCanvasContext | null>(null);
  const [loadedModels] = useState<Map<string, FBXModel>>(new Map());
  
  const [tempInputValues, setTempInputValues] = useState<NodeSystemState['tempInputValues']>({});
  
  // 引用
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const commServiceRef = useRef<NodeCommunicationService | null>(null);
  
  // 工具方法
  const generateSessionId = useCallback((): string => {
    const today = new Date().toISOString().split('T')[0];
    return `session_${today}`;
  }, []);
  
  const scrollToBottom = useCallback(() => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  }, []);
  
  // Agent消息操作
  const addAgentMessage = useCallback((message: AgentMessage) => {
    setAgentMessages(prev => [...prev, message]);
  }, []);
  
  // 脚本操作
  const addScriptFile = useCallback((script: ScriptFile) => {
    setScriptFiles(prev => [...prev, script]);
  }, []);
  
  const updateScriptFile = useCallback((id: string, updates: Partial<ScriptFile>) => {
    setScriptFiles(prev => prev.map(script => 
      script.id === id ? { ...script, ...updates } : script
    ));
  }, []);
  
  const removeScriptFile = useCallback((id: string) => {
    setScriptFiles(prev => prev.filter(script => script.id !== id));
    if (selectedScript?.id === id) {
      setSelectedScript(null);
    }
  }, [selectedScript]);
  
  // 模型操作
  const addLoadedModel = useCallback((id: string, model: FBXModel) => {
    loadedModels.set(id, model);
  }, [loadedModels]);
  
  const removeLoadedModel = useCallback((id: string) => {
    const model = loadedModels.get(id);
    if (model) {
      // 清理模型资源
      loadedModels.delete(id);
    }
  }, [loadedModels]);
  
  // 临时输入值更新
  const updateTempInputValues = useCallback((values: Partial<NodeSystemState['tempInputValues']>) => {
    setTempInputValues(prev => ({ ...prev, ...values }));
  }, []);
  
  const contextValue: NodeSystemState & NodeSystemActions = {
    // 状态
    nodes,
    selectedNode,
    nodeStats,
    agentMessages,
    agentInput,
    isAgentProcessing,
    isLoadingHistory,
    currentSessionId,
    scriptFiles,
    selectedScript,
    editingScriptId,
    editingScriptName,
    isUploadingModel,
    uploadError,
    isDragOver,
    threeContext,
    loadedModels,
    tempInputValues,
    
    // 操作
    setNodes,
    setSelectedNode,
    setNodeStats,
    setAgentMessages,
    setAgentInput,
    setIsAgentProcessing,
    setIsLoadingHistory,
    setCurrentSessionId,
    addAgentMessage,
    setScriptFiles,
    setSelectedScript,
    setEditingScriptId,
    setEditingScriptName,
    addScriptFile,
    updateScriptFile,
    removeScriptFile,
    setIsUploadingModel,
    setUploadError,
    setIsDragOver,
    setThreeContext,
    addLoadedModel,
    removeLoadedModel,
    setTempInputValues: updateTempInputValues,
    generateSessionId,
    scrollToBottom
  };
  
  return (
    <NodeSystemContext.Provider value={contextValue}>
      <div ref={messagesContainerRef}>
        {children}
      </div>
    </NodeSystemContext.Provider>
  );
};

export default NodeSystemContext;
