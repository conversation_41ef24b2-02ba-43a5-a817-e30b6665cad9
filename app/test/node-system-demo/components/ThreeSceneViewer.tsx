/**
 * ThreeSceneViewer - Three.js场景查看器组件
 * 替代原来的Babylon.js场景，提供3D场景渲染和交互功能
 */

'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import dynamic from 'next/dynamic';

// 动态导入ThreeCanvas以避免SSR问题
const ThreeCanvas = dynamic(
  () => import('../../../../src/three/components/ThreeCanvas').then(mod => ({ default: mod.ThreeCanvas })),
  {
    ssr: false,
    loading: () => <div className="flex items-center justify-center h-full bg-gray-100">
      <div className="text-center">
        <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
        <p className="text-sm text-gray-600">加载Three.js场景...</p>
      </div>
    </div>
  }
);

import type { ThreeCanvasContext } from '../../../../src/three/components/ThreeCanvas';
import { ThreeUtils } from '../../../../src/three/core/ThreeUtils';
import { useNodeSystem } from '../contexts/NodeSystemContext';
import { ThreeNodeFactory } from '../../../../src/utils/ThreeNodeFactory';
import { NodeRegistry } from '../../../../src/utils/NodeRegistry';

interface ThreeSceneViewerProps {
  className?: string;
}

export const ThreeSceneViewer: React.FC<ThreeSceneViewerProps> = ({ className = '' }) => {
  const {
    nodes,
    selectedNode,
    setNodes,
    setNodeStats,
    setSelectedNode,
    setThreeContext,
    threeContext,
  } = useNodeSystem();

  const controlsRef = useRef<OrbitControls | null>(null);
  const nodeFactoryRef = useRef<ThreeNodeFactory | null>(null);
  const nodeRegistryRef = useRef<NodeRegistry | null>(null);

  // 加载已保存的节点属性
  const loadSavedNodeProperties = useCallback(async (context?: ThreeCanvasContext) => {
    const targetContext = context || threeContext;
    if (!targetContext) {
      console.warn('[ThreeSceneViewer] threeContext 不可用，无法加载节点属性');
      return;
    }

    try {
      const response = await fetch('/api/node-properties');
      const result = await response.json();

      if (result.success && result.data.nodes.length > 0) {
        console.log('[ThreeSceneViewer] 加载已保存的节点属性:', result.data.nodes);
        console.log('[ThreeSceneViewer] 使用的context:', targetContext);

        const { scene } = targetContext;

        // 应用已保存的属性到Three.js对象
        result.data.nodes.forEach((savedNode: {
          id: string;
          name: string;
          position?: { x: number; y: number; z: number };
          rotation?: { x: number; y: number; z: number };
          scaling?: { x: number; y: number; z: number };
          color?: { r: number; g: number; b: number };
        }) => {
          const object = scene.getObjectByName(savedNode.id);
          if (object) {
            // 应用位置
            if (savedNode.position) {
              object.position.set(
                savedNode.position.x || 0,
                savedNode.position.y || 0,
                savedNode.position.z || 0
              );
            }

            // 应用旋转
            if (savedNode.rotation) {
              object.rotation.set(
                savedNode.rotation.x || 0,
                savedNode.rotation.y || 0,
                savedNode.rotation.z || 0
              );
            }

            // 应用缩放
            if (savedNode.scaling) {
              object.scale.set(
                savedNode.scaling.x || 1,
                savedNode.scaling.y || 1,
                savedNode.scaling.z || 1
              );
            }

            // 应用颜色（如果是Mesh对象）
            if (savedNode.color && object instanceof THREE.Mesh && object.material instanceof THREE.MeshStandardMaterial) {
              object.material.color.setRGB(
                savedNode.color.r || 1,
                savedNode.color.g || 1,
                savedNode.color.b || 1
              );
            }

            // 同时更新NodeRegistry中的节点属性
            if (nodeRegistryRef.current) {
              const existingNode = nodeRegistryRef.current.getNode(savedNode.id);
              if (existingNode) {
                const updatedNode = {
                  ...existingNode,
                  name: savedNode.name || existingNode.name,
                  position: savedNode.position ? new THREE.Vector3(
                    savedNode.position.x,
                    savedNode.position.y,
                    savedNode.position.z
                  ) : existingNode.position,
                  rotation: savedNode.rotation ? new THREE.Vector3(
                    savedNode.rotation.x,
                    savedNode.rotation.y,
                    savedNode.rotation.z
                  ) : existingNode.rotation,
                  scaling: savedNode.scaling ? new THREE.Vector3(
                    savedNode.scaling.x,
                    savedNode.scaling.y,
                    savedNode.scaling.z
                  ) : existingNode.scaling
                };

                // 如果是MeshNodeProperties，更新材质颜色
                if (savedNode.color && 'material' in existingNode && 'material' in updatedNode) {
                  const meshNode = updatedNode as any; // 临时使用any，因为类型系统复杂
                  meshNode.material = {
                    ...existingNode.material,
                    diffuseColor: new THREE.Color(savedNode.color.r, savedNode.color.g, savedNode.color.b)
                  };
                }

                nodeRegistryRef.current.updateNode(savedNode.id, updatedNode);
                console.log(`[ThreeSceneViewer] 已更新NodeRegistry中的节点: ${savedNode.id}`);
              }
            }

            console.log(`[ThreeSceneViewer] 已应用属性到节点: ${savedNode.id}`);
          } else {
            console.warn(`[ThreeSceneViewer] 未找到节点对象: ${savedNode.id}`);
          }
        });

        // 更新节点状态以反映加载的属性
        if (nodeRegistryRef.current) {
          setNodes(nodeRegistryRef.current.getAllNodes());
        }

        console.log('[ThreeSceneViewer] 所有已保存属性已应用');
      }
    } catch (error) {
      console.error('[ThreeSceneViewer] 加载节点属性失败:', error);
    }
  }, [threeContext, setNodes]);

  // 初始化Three.js场景
  const handleThreeInitialized = useCallback((context: ThreeCanvasContext) => {
    console.log('[ThreeSceneViewer] Three.js场景初始化');
    
    setThreeContext(context);
    
    // 创建轨道控制器
    const controls = new OrbitControls(context.camera, context.renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.screenSpacePanning = false;
    controls.minDistance = 1;
    controls.maxDistance = 100;
    controls.maxPolarAngle = Math.PI / 2;
    controlsRef.current = controls;

    // 初始化节点系统
    nodeFactoryRef.current = ThreeNodeFactory.getInstance();
    nodeRegistryRef.current = NodeRegistry.getInstance();

    // 清理现有节点
    nodeFactoryRef.current.clearAllNodes();

    // 创建基础3D场景（需要适配Three.js）
    createBasicThreeScene(context);

    // 注册节点变化监听器
    nodeRegistryRef.current.addObserver((updatedNodes) => {
      setNodes([...updatedNodes]);
      setNodeStats(nodeFactoryRef.current?.getNodeStats() || { total: 0, byType: {} });
    });

    // 更新节点状态
    setNodes(nodeRegistryRef.current.getAllNodes());
    setNodeStats(nodeFactoryRef.current.getNodeStats());

  }, [setThreeContext, setNodes, setNodeStats]);

  // 创建基础Three.js场景
  const createBasicThreeScene = useCallback((context: ThreeCanvasContext) => {
    if (!nodeFactoryRef.current) return;

    const { scene } = context;

    // 使用ThreeNodeFactory创建基础3D场景
    const sceneObjects = nodeFactoryRef.current.createBasic3DScene(scene);

    // 添加网格辅助线
    const gridHelper = ThreeUtils.createGridHelper(20, 20);
    scene.add(gridHelper);

    console.log('[ThreeSceneViewer] 基础场景创建完成', sceneObjects);

    // 场景创建完成后，加载已保存的节点属性
    setTimeout(() => {
      console.log('[ThreeSceneViewer] 开始加载已保存的节点属性...');
      loadSavedNodeProperties(context);
    }, 100); // 稍微延迟确保所有对象都已添加到场景中
  }, [loadSavedNodeProperties]);

  // 渲染循环回调
  const handleRender = useCallback((_context: ThreeCanvasContext, deltaTime: number) => {
    // 更新控制器
    if (controlsRef.current) {
      controlsRef.current.update();
    }

    // 更新所有动画混合器
    // 注意：这里我们需要从全局状态或其他方式获取动画混合器
    // 由于架构限制，我们将在NodePropertiesPanel中直接处理动画更新

    // 遍历场景中的所有对象，查找并更新动画混合器
    if (_context.scene) {
      _context.scene.traverse((object) => {
        if (object.userData.animationMixer) {
          object.userData.animationMixer.update(deltaTime);
        }
      });
    }
  }, []);

  // 添加随机方块
  const addRandomBox = useCallback(() => {
    if (!threeContext || !nodeFactoryRef.current) return;

    const { scene } = threeContext;
    const id = `box_${Date.now()}`;

    // 使用ThreeNodeFactory创建方块
    const box = nodeFactoryRef.current.createBoxMesh(scene, {
      id,
      name: `随机方块_${Math.floor(Math.random() * 1000)}`,
      size: 0.5 + Math.random() * 1.5,
      position: new THREE.Vector3(
        (Math.random() - 0.5) * 10,
        Math.random() * 3 + 0.5,
        (Math.random() - 0.5) * 10
      ),
      color: new THREE.Color(Math.random(), Math.random(), Math.random())
    });

    // 更新节点状态
    if (nodeRegistryRef.current) {
      setNodes(nodeRegistryRef.current.getAllNodes());
      setNodeStats(nodeFactoryRef.current.getNodeStats());
    }

    console.log('[ThreeSceneViewer] 添加随机方块:', box.name);
  }, [threeContext, setNodes, setNodeStats]);

  // 移除选中节点
  const removeSelectedNode = useCallback(() => {
    if (!selectedNode || !threeContext) return;

    const { scene } = threeContext;
    const object = scene.getObjectByName(selectedNode.id);
    
    if (object) {
      scene.remove(object);
      ThreeUtils.disposeObject(object);
    }

    if (nodeRegistryRef.current) {
      nodeRegistryRef.current.unregister(selectedNode.id);
    }

    setSelectedNode(null);
    console.log('[ThreeSceneViewer] 移除节点:', selectedNode.id);
  }, [selectedNode, threeContext, setSelectedNode]);

  // 重置场景
  const resetScene = useCallback(() => {
    if (!threeContext || !nodeFactoryRef.current || !nodeRegistryRef.current) return;

    const { scene } = threeContext;

    // 清理场景中的所有对象（除了光照和相机）
    const objectsToRemove: THREE.Object3D[] = [];
    scene.traverse((child) => {
      if (child !== scene && !(child instanceof THREE.Light) && !(child instanceof THREE.Camera)) {
        objectsToRemove.push(child);
      }
    });

    objectsToRemove.forEach(obj => {
      scene.remove(obj);
      ThreeUtils.disposeObject(obj);
    });

    // 清理节点系统
    nodeFactoryRef.current.clearAllNodes();
    
    // 重新创建基础场景
    createBasicThreeScene(threeContext);

    // 更新状态
    setNodes(nodeRegistryRef.current.getAllNodes());
    setNodeStats(nodeFactoryRef.current.getNodeStats());
    setSelectedNode(null);

    console.log('[ThreeSceneViewer] 场景已重置');
  }, [threeContext, createBasicThreeScene, setNodes, setNodeStats, setSelectedNode]);



  // 清理资源
  useEffect(() => {
    return () => {
      if (controlsRef.current) {
        controlsRef.current.dispose();
      }
    };
  }, []);

  return (
    <div className={`flex-1 flex flex-col ${className}`}>
      {/* 3D Canvas区域 */}
      <div className="flex-1 relative bg-gray-100">
        <ThreeCanvas
          className="w-full h-full"
          engineId="node-system-demo"
          debugMode={process.env.NODE_ENV === 'development'}
          backgroundColor={0xa0a0a0}
          enableShadows={true}
          enableFog={true}
          onInitialized={handleThreeInitialized}
          onRender={handleRender}
        />
        
        {/* 场景控制按钮 */}
        <div className="absolute top-4 left-4 space-y-2">
          <button
            onClick={addRandomBox}
            className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm font-medium transition-colors shadow-sm"
          >
            ➕ 添加方块
          </button>
          
          <button
            onClick={removeSelectedNode}
            disabled={!selectedNode}
            className="bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-3 py-2 rounded text-sm font-medium transition-colors shadow-sm"
          >
            🗑️ 删除选中
          </button>
          
          <button
            onClick={resetScene}
            className="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-2 rounded text-sm font-medium transition-colors shadow-sm"
          >
            🔄 重置场景
          </button>
        </div>

        {/* 场景信息显示 */}
        <div className="absolute top-4 right-4 bg-black bg-opacity-50 text-white px-3 py-2 rounded text-sm">
          <div>节点总数: {nodes.length}</div>
          <div>选中: {selectedNode ? selectedNode.name : '无'}</div>
          <div>引擎: Three.js</div>
        </div>
      </div>
    </div>
  );
};

export default ThreeSceneViewer;
