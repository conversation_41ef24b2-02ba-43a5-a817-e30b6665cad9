import React, { useCallback, useState } from 'react';
import * as THREE from 'three';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { AnimationFileMetadata } from '../../../../src/types/NodeTypes';

interface AnimationManagerProps {
  nodeId: string;
  animations: AnimationFileMetadata[];
  currentAnimation?: string;
  onAnimationsUpdate: (animations: AnimationFileMetadata[]) => void;
  onAnimationPlay: (animationId: string) => void;
  onAnimationStop: () => void;
  threeContext: any;
}

export const AnimationManager: React.FC<AnimationManagerProps> = ({
  nodeId,
  animations,
  currentAnimation,
  onAnimationsUpdate,
  onAnimationPlay,
  onAnimationStop,
  threeContext
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [editingAnimationId, setEditingAnimationId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');

  // 处理动画文件上传
  const handleAnimationUpload = useCallback(async (file: File) => {
    setIsUploading(true);
    setUploadError(null);

    try {
      // 验证文件格式
      const validExtensions = ['.fbx'];
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      
      if (!validExtensions.includes(fileExtension)) {
        throw new Error('动画文件仅支持 .fbx 格式');
      }

      console.log('[动画管理] 开始上传动画文件:', file.name);
      
      // 1. 上传文件到服务器
      const formData = new FormData();
      formData.append('file', file);
      formData.append('nodeId', nodeId);
      formData.append('type', 'animation');
      
      const uploadResponse = await fetch('/api/upload-model', {
        method: 'POST',
        body: formData
      });
      
      const uploadResult = await uploadResponse.json();
      
      if (!uploadResult.success) {
        throw new Error(uploadResult.error || '动画文件上传失败');
      }
      
      console.log('[动画管理] 动画文件上传成功:', uploadResult.data.filePath);
      
      // 2. 分析动画文件获取元数据
      const animationMetadata = await analyzeAnimationFile(uploadResult.data.filePath, file);
      
      // 3. 更新动画列表
      const newAnimations = [...animations, animationMetadata];
      onAnimationsUpdate(newAnimations);
      
      console.log('[动画管理] 动画添加成功:', animationMetadata.name);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '动画上传失败';
      setUploadError(errorMessage);
      console.error('[动画管理] 动画上传失败:', error);
    } finally {
      setIsUploading(false);
    }
  }, [nodeId, animations, onAnimationsUpdate]);

  // 分析动画文件获取元数据
  const analyzeAnimationFile = useCallback(async (filePath: string, file: File): Promise<AnimationFileMetadata> => {
    return new Promise((resolve, reject) => {
      const fbxLoader = new FBXLoader();
      
      fbxLoader.load(
        filePath,
        (object: THREE.Group) => {
          const animationId = `anim_${Date.now()}`;
          const clipNames = object.animations.map(clip => clip.name);
          const totalDuration = Math.max(...object.animations.map(clip => clip.duration));
          
          const metadata: AnimationFileMetadata = {
            id: animationId,
            name: file.name.replace(/\.[^/.]+$/, ""), // 移除文件扩展名
            fileName: file.name,
            filePath: filePath,
            fileSize: file.size,
            duration: totalDuration,
            clipCount: object.animations.length,
            clipNames: clipNames,
            uploadedAt: new Date().toISOString(),
            isActive: false,
            isLooping: true,
            playbackSpeed: 1.0
          };
          
          console.log('[动画分析] 动画元数据:', metadata);
          resolve(metadata);
        },
        undefined,
        (error: any) => {
          console.error('[动画分析] 分析失败:', error);
          reject(new Error('动画文件分析失败'));
        }
      );
    });
  }, []);

  // 预览动画
  const handlePreviewAnimation = useCallback((animationId: string) => {
    console.log('[动画管理] 预览动画:', animationId);
    onAnimationPlay(animationId);
  }, [onAnimationPlay]);

  // 停止动画
  const handleStopAnimation = useCallback(() => {
    console.log('[动画管理] 停止动画');
    onAnimationStop();
  }, [onAnimationStop]);

  // 删除动画
  const handleDeleteAnimation = useCallback((animationId: string) => {
    if (confirm('确定要删除这个动画吗？')) {
      const newAnimations = animations.filter(anim => anim.id !== animationId);
      onAnimationsUpdate(newAnimations);
      
      // 如果删除的是当前播放的动画，停止播放
      if (currentAnimation === animationId) {
        handleStopAnimation();
      }
      
      console.log('[动画管理] 动画已删除:', animationId);
    }
  }, [animations, currentAnimation, onAnimationsUpdate, handleStopAnimation]);

  // 编辑动画名称
  const handleEditAnimationName = useCallback((animationId: string, newName: string) => {
    const newAnimations = animations.map(anim => 
      anim.id === animationId ? { ...anim, name: newName } : anim
    );
    onAnimationsUpdate(newAnimations);
    setEditingAnimationId(null);
    setEditingName('');
    console.log('[动画管理] 动画名称已更新:', animationId, '->', newName);
  }, [animations, onAnimationsUpdate]);

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化时长
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-4">
      {/* 动画上传区域 */}
      <div>
        <label className="block text-sm font-medium mb-2 text-gray-900">上传动画文件</label>
        <div
          className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors ${
            isDragOver
              ? 'border-green-500 bg-green-50'
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDragOver={(e) => {
            e.preventDefault();
            setIsDragOver(true);
          }}
          onDragLeave={(e) => {
            e.preventDefault();
            setIsDragOver(false);
          }}
          onDrop={(e) => {
            e.preventDefault();
            setIsDragOver(false);
            const files = Array.from(e.dataTransfer.files);
            const file = files[0];
            if (file) {
              handleAnimationUpload(file);
            }
          }}
        >
          <input
            type="file"
            accept=".fbx"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) {
                handleAnimationUpload(file);
              }
            }}
            className="hidden"
            id="animation-upload"
            disabled={isUploading}
          />
          <label
            htmlFor="animation-upload"
            className={`flex flex-col items-center justify-center cursor-pointer ${
              isUploading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <div className="w-8 h-8 mb-2 text-green-600">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z" />
              </svg>
            </div>
            {isUploading ? (
              <div className="flex flex-col items-center">
                <div className="w-6 h-6 border-2 border-green-500 border-t-transparent rounded-full animate-spin mb-2"></div>
                <p className="text-sm text-gray-600">正在处理动画...</p>
              </div>
            ) : (
              <>
                <p className="text-sm text-gray-700 mb-1">
                  {isDragOver ? '松开以上传动画' : '点击或拖拽上传动画文件'}
                </p>
                <p className="text-xs text-gray-600">支持 .fbx 格式</p>
              </>
            )}
          </label>
        </div>
        
        {uploadError && (
          <div className="mt-2 p-2 bg-red-100 border border-red-300 rounded text-sm text-red-800">
            {uploadError}
          </div>
        )}
      </div>

      {/* 动画列表 */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-900">动画列表 ({animations.length})</h3>
          {currentAnimation && (
            <button
              onClick={handleStopAnimation}
              className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
            >
              停止播放
            </button>
          )}
        </div>
        
        {animations.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <div className="w-12 h-12 mx-auto mb-3 text-gray-400">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4z" />
              </svg>
            </div>
            <p className="text-sm">暂无动画文件</p>
            <p className="text-xs text-gray-400 mt-1">上传FBX动画文件开始使用</p>
          </div>
        ) : (
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {animations.map((animation) => (
              <div
                key={animation.id}
                className={`p-3 border rounded-lg transition-colors ${
                  currentAnimation === animation.id
                    ? 'border-green-500 bg-green-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    {editingAnimationId === animation.id ? (
                      <input
                        type="text"
                        value={editingName}
                        onChange={(e) => setEditingName(e.target.value)}
                        onBlur={() => {
                          if (editingName.trim()) {
                            handleEditAnimationName(animation.id, editingName.trim());
                          } else {
                            setEditingAnimationId(null);
                            setEditingName('');
                          }
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.currentTarget.blur();
                          } else if (e.key === 'Escape') {
                            setEditingAnimationId(null);
                            setEditingName('');
                          }
                        }}
                        className="text-sm font-medium text-gray-900 bg-white border border-gray-300 rounded px-2 py-1 w-full"
                        autoFocus
                      />
                    ) : (
                      <h4
                        className="text-sm font-medium text-gray-900 truncate cursor-pointer hover:text-blue-600"
                        onClick={() => {
                          setEditingAnimationId(animation.id);
                          setEditingName(animation.name);
                        }}
                        title="点击编辑名称"
                      >
                        {animation.name}
                        {currentAnimation === animation.id && (
                          <span className="ml-2 text-xs text-green-600">(播放中)</span>
                        )}
                      </h4>
                    )}
                    <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                      <span>时长: {formatDuration(animation.duration)}</span>
                      <span>大小: {formatFileSize(animation.fileSize)}</span>
                      <span>剪辑: {animation.clipCount}</span>
                    </div>
                    {animation.clipNames.length > 0 && (
                      <div className="mt-1 text-xs text-gray-400">
                        剪辑名称: {animation.clipNames.join(', ')}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-1 ml-3">
                    <button
                      onClick={() => handlePreviewAnimation(animation.id)}
                      disabled={currentAnimation === animation.id}
                      className={`px-2 py-1 text-xs rounded transition-colors ${
                        currentAnimation === animation.id
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                      }`}
                      title="预览动画"
                    >
                      播放
                    </button>
                    <button
                      onClick={() => handleDeleteAnimation(animation.id)}
                      className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
                      title="删除动画"
                    >
                      删除
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
