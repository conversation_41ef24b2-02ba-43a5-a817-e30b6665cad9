/**
 * ScriptManager - 脚本管理组件
 * 提供脚本文件的展示、编辑和管理功能
 */

'use client';

import React, { useCallback, useEffect, useRef } from 'react';
import { useNodeSystem } from '../contexts/NodeSystemContext';

interface ScriptManagerProps {
  className?: string;
}

export const ScriptManager: React.FC<ScriptManagerProps> = ({ className = '' }) => {
  const {
    scriptFiles,
    selectedScript,
    editingScriptId,
    editingScriptName,
    setScriptFiles,
    setSelectedScript,
    setEditingScriptId,
    setEditingScriptName,
    addScriptFile,
    updateScriptFile,
    removeScriptFile
  } = useNodeSystem();

  const activeScriptCleanups = useRef<Map<string, (() => void)[]>>(new Map());

  // 加载已保存的脚本
  const loadSavedScripts = useCallback(async () => {
    try {
      const response = await fetch('/api/load-scripts');
      const result = await response.json();
      
      if (result.success && result.data.scripts) {
        setScriptFiles(result.data.scripts);
        console.log(`[脚本管理] 成功加载 ${result.data.scripts.length} 个脚本`);
      } else {
        console.warn('[脚本管理] 加载脚本失败:', result.error);
      }
    } catch (error) {
      console.error('[脚本管理] 加载脚本时发生错误:', error);
    }
  }, [setScriptFiles]);

  // 保存脚本到服务器
  const saveScriptToServer = useCallback(async (script: typeof scriptFiles[0]) => {
    try {
      const response = await fetch('/api/save-script', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(script)
      });

      const result = await response.json();
      if (result.success) {
        console.log('[脚本管理] 脚本保存成功:', script.name);
      } else {
        console.warn('[脚本管理] 脚本保存失败:', result.error);
      }
    } catch (error) {
      console.error('[脚本管理] 保存脚本时发生错误:', error);
    }
  }, []);

  // 删除脚本
  const deleteScript = useCallback(async (scriptId: string) => {
    try {
      const response = await fetch('/api/delete-script', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ scriptId })
      });

      const result = await response.json();
      if (result.success) {
        removeScriptFile(scriptId);
        console.log('[脚本管理] 脚本删除成功:', scriptId);
      } else {
        console.warn('[脚本管理] 脚本删除失败:', result.error);
      }
    } catch (error) {
      console.error('[脚本管理] 删除脚本时发生错误:', error);
    }
  }, [removeScriptFile]);

  // 执行脚本
  const executeScript = useCallback((script: typeof scriptFiles[0]) => {
    try {
      console.log(`[脚本执行] 开始执行脚本: ${script.name}`);
      
      // 停止之前的脚本
      const cleanups = activeScriptCleanups.current.get(script.id) || [];
      cleanups.forEach(cleanup => cleanup());
      activeScriptCleanups.current.delete(script.id);

      // 创建脚本执行环境
      const scriptFunction = new Function('console', 'THREE', 'scene', 'camera', script.content);
      
      // TODO: 传入实际的Three.js对象
      scriptFunction(console, {}, {}, {});

      // 标记脚本为活跃状态
      updateScriptFile(script.id, { isActive: true });
      
      console.log(`[脚本执行] 脚本执行完成: ${script.name}`);
    } catch (error) {
      console.error(`[脚本执行] 脚本执行失败: ${script.name}`, error);
    }
  }, [updateScriptFile]);

  // 停止脚本
  const stopScript = useCallback((script: typeof scriptFiles[0]) => {
    const cleanups = activeScriptCleanups.current.get(script.id) || [];
    cleanups.forEach(cleanup => cleanup());
    activeScriptCleanups.current.delete(script.id);

    updateScriptFile(script.id, { isActive: false });
    console.log(`[脚本管理] 脚本已停止: ${script.name}`);
  }, [updateScriptFile]);

  // 开始重命名脚本
  const startRenameScript = useCallback((script: typeof scriptFiles[0]) => {
    setEditingScriptId(script.id);
    setEditingScriptName(script.name);
  }, [setEditingScriptId, setEditingScriptName]);

  // 确认重命名脚本
  const confirmRenameScript = useCallback(async () => {
    if (!editingScriptId || !editingScriptName.trim()) return;

    const updatedScript = {
      ...scriptFiles.find(s => s.id === editingScriptId)!,
      name: editingScriptName.trim(),
      lastModified: new Date()
    };

    updateScriptFile(editingScriptId, updatedScript);
    await saveScriptToServer(updatedScript);

    setEditingScriptId(null);
    setEditingScriptName('');
  }, [editingScriptId, editingScriptName, scriptFiles, updateScriptFile, saveScriptToServer, setEditingScriptId, setEditingScriptName]);

  // 取消重命名
  const cancelRenameScript = useCallback(() => {
    setEditingScriptId(null);
    setEditingScriptName('');
  }, [setEditingScriptId, setEditingScriptName]);

  // 处理重命名输入的键盘事件
  const handleRenameKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      confirmRenameScript();
    } else if (e.key === 'Escape') {
      cancelRenameScript();
    }
  }, [confirmRenameScript, cancelRenameScript]);

  // 组件挂载时加载脚本
  useEffect(() => {
    loadSavedScripts();
  }, [loadSavedScripts]);

  // 组件卸载时清理所有活跃脚本
  useEffect(() => {
    return () => {
      activeScriptCleanups.current.forEach((cleanups) => {
        cleanups.forEach(cleanup => cleanup());
      });
      activeScriptCleanups.current.clear();
    };
  }, []);

  return (
    <div className={`h-80 bg-white border-t border-gray-200 flex ${className}`}>
      {/* 左侧：脚本文件列表 */}
      <div className="w-80 border-r border-gray-200 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-lg font-bold text-gray-900">📄 脚本管理</h3>
          <p className="text-sm text-gray-600">生成的脚本文件</p>
        </div>
        
        <div className="flex-1 overflow-y-auto p-4 space-y-2">
          {scriptFiles.length === 0 ? (
            <div className="text-center text-gray-500 mt-8">
              <p className="text-sm">还没有脚本文件</p>
              <p className="text-xs mt-1">通过Agent对话生成脚本</p>
            </div>
          ) : (
            scriptFiles.map((script) => (
              <div
                key={script.id}
                className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                  selectedScript?.id === script.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
                onClick={() => setSelectedScript(script)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    {editingScriptId === script.id ? (
                      <input
                        type="text"
                        value={editingScriptName}
                        onChange={(e) => setEditingScriptName(e.target.value)}
                        onKeyPress={handleRenameKeyPress}
                        onBlur={confirmRenameScript}
                        className="w-full text-sm font-medium bg-white border border-blue-500 rounded px-2 py-1 focus:outline-none"
                        autoFocus
                      />
                    ) : (
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        {script.name}
                      </h4>
                    )}
                    <p className="text-xs text-gray-600 mt-1 truncate">
                      {script.metadata.description}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                        script.isActive 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {script.isActive ? '运行中' : '已停止'}
                      </span>
                      <span className="text-xs text-gray-500">
                        {script.lastModified.toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-1 ml-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (script.isActive) {
                          stopScript(script);
                        } else {
                          executeScript(script);
                        }
                      }}
                      className={`p-1 rounded text-xs ${
                        script.isActive
                          ? 'text-red-600 hover:bg-red-100'
                          : 'text-green-600 hover:bg-green-100'
                      }`}
                      title={script.isActive ? '停止脚本' : '执行脚本'}
                    >
                      {script.isActive ? '⏹️' : '▶️'}
                    </button>
                    
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        startRenameScript(script);
                      }}
                      className="p-1 rounded text-xs text-blue-600 hover:bg-blue-100"
                      title="重命名脚本"
                    >
                      ✏️
                    </button>
                    
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (confirm(`确定要删除脚本 "${script.name}" 吗？`)) {
                          deleteScript(script.id);
                        }
                      }}
                      className="p-1 rounded text-xs text-red-600 hover:bg-red-100"
                      title="删除脚本"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* 右侧：脚本内容展示 */}
      <div className="flex-1 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-lg font-bold text-gray-900">
            {selectedScript ? `📝 ${selectedScript.name}` : '📝 脚本内容'}
          </h3>
          <p className="text-sm text-gray-600">
            {selectedScript ? selectedScript.metadata.description : '选择脚本查看内容'}
          </p>
        </div>
        
        <div className="flex-1 overflow-hidden">
          {selectedScript ? (
            <div className="h-full flex flex-col">
              <div className="flex-1 overflow-y-auto">
                <pre className="p-4 text-xs font-mono bg-gray-50 text-gray-900 whitespace-pre-wrap break-words h-full">
                  {selectedScript.content}
                </pre>
              </div>
              
              <div className="p-4 border-t border-gray-200 bg-gray-50">
                <div className="flex items-center justify-between text-xs text-gray-600">
                  <div>
                    创建时间: {selectedScript.createdAt.toLocaleString()}
                  </div>
                  <div>
                    修改时间: {selectedScript.lastModified.toLocaleString()}
                  </div>
                </div>
                
                {selectedScript.metadata.targetNodeTypes.length > 0 && (
                  <div className="mt-2">
                    <span className="text-xs text-gray-600">目标节点类型: </span>
                    {selectedScript.metadata.targetNodeTypes.map((type, index) => (
                      <span
                        key={type}
                        className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 ml-1"
                      >
                        {type}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full text-gray-500">
              <div className="text-center">
                <p className="text-sm">选择脚本查看内容</p>
                <p className="text-xs mt-1">或通过Agent对话生成新脚本</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ScriptManager;
