/**
 * AgentChatPanel - AI Agent对话面板组件
 * 处理与AI Agent的对话交互功能
 */

'use client';

import React, { useEffect, useCallback, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import { useNodeSystem } from '../contexts/NodeSystemContext';

interface AgentChatPanelProps {
  className?: string;
}

export const AgentChatPanel: React.FC<AgentChatPanelProps> = ({ className = '' }) => {
  const {
    agentMessages,
    agentInput,
    isAgentProcessing,
    isLoadingHistory,
    currentSessionId,
    setAgentInput,
    setIsAgentProcessing,
    addAgentMessage,
    setAgentMessages,
    setIsLoadingHistory,
    setCurrentSessionId,
    generateSessionId,
    scrollToBottom
  } = useNodeSystem();

  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const saveHistoryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 加载对话历史
  const loadChatHistory = useCallback(async () => {
    try {
      setIsLoadingHistory(true);
      const sessionId = generateSessionId();
      setCurrentSessionId(sessionId);
      
      console.log(`[对话历史] 加载会话: ${sessionId}`);
      
      const response = await fetch(`/api/chat-history?sessionId=${sessionId}`);
      const result = await response.json();
      
      if (result.success && result.data.messages) {
        const messagesWithDates = result.data.messages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }));
        
        setAgentMessages(messagesWithDates);
        console.log(`[对话历史] 成功加载 ${messagesWithDates.length} 条消息`);
      } else {
        console.warn('[对话历史] 加载失败:', result.error);
      }
    } catch (error) {
      console.error('[对话历史] 加载时发生错误:', error);
    } finally {
      setIsLoadingHistory(false);
    }
  }, [generateSessionId, setCurrentSessionId, setAgentMessages, setIsLoadingHistory]);

  // 保存对话历史
  const saveChatHistory = useCallback(async () => {
    if (agentMessages.length === 0 || !currentSessionId) return;
    
    try {
      const response = await fetch('/api/chat-history', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId: currentSessionId,
          messages: agentMessages
        })
      });
      
      const result = await response.json();
      if (!result.success) {
        console.warn('[对话历史] 保存失败:', result.error);
      }
    } catch (error) {
      console.error('[对话历史] 保存时发生错误:', error);
    }
  }, [agentMessages, currentSessionId]);

  // 清空对话历史
  const clearChatHistory = useCallback(async () => {
    if (!currentSessionId) return;
    
    try {
      const response = await fetch('/api/chat-history', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId: currentSessionId })
      });
      
      const result = await response.json();
      if (result.success) {
        setAgentMessages([]);
        console.log('[对话历史] 历史已清空');
      } else {
        console.warn('[对话历史] 清空失败:', result.error);
      }
    } catch (error) {
      console.error('[对话历史] 清空时发生错误:', error);
    }
  }, [currentSessionId, setAgentMessages]);

  // 发送消息给Agent
  const sendMessageToAgent = useCallback(async () => {
    if (!agentInput.trim() || isAgentProcessing) return;

    const userMessage = {
      id: `user_${Date.now()}`,
      role: 'user' as const,
      content: agentInput.trim(),
      timestamp: new Date()
    };

    addAgentMessage(userMessage);
    setAgentInput('');
    setIsAgentProcessing(true);

    try {
      const response = await fetch('/api/agent-chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: userMessage.content,
          sessionId: currentSessionId,
          history: agentMessages.slice(-10) // 只发送最近10条消息作为上下文
        })
      });

      const result = await response.json();

      if (result.success && result.data.response) {
        const assistantMessage = {
          id: `assistant_${Date.now()}`,
          role: 'assistant' as const,
          content: result.data.response,
          timestamp: new Date()
        };
        
        addAgentMessage(assistantMessage);
      } else {
        const errorMessage = {
          id: `error_${Date.now()}`,
          role: 'assistant' as const,
          content: `抱歉，处理您的请求时出现了错误：${result.error || '未知错误'}`,
          timestamp: new Date()
        };
        
        addAgentMessage(errorMessage);
      }
    } catch (error) {
      console.error('[Agent对话] 发送消息失败:', error);
      
      const errorMessage = {
        id: `error_${Date.now()}`,
        role: 'assistant' as const,
        content: '抱歉，网络连接出现问题，请稍后重试。',
        timestamp: new Date()
      };
      
      addAgentMessage(errorMessage);
    } finally {
      setIsAgentProcessing(false);
    }
  }, [agentInput, isAgentProcessing, currentSessionId, agentMessages, addAgentMessage, setAgentInput, setIsAgentProcessing]);

  // 处理键盘事件
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessageToAgent();
    }
  }, [sendMessageToAgent]);

  // 组件挂载时加载历史
  useEffect(() => {
    loadChatHistory();
  }, [loadChatHistory]);

  // 监听消息变化并自动保存
  useEffect(() => {
    if (agentMessages.length > 0 && !isLoadingHistory) {
      // 延迟保存，避免频繁调用
      if (saveHistoryTimeoutRef.current) {
        clearTimeout(saveHistoryTimeoutRef.current);
      }
      
      saveHistoryTimeoutRef.current = setTimeout(() => {
        saveChatHistory();
      }, 1000);
    }
    
    return () => {
      if (saveHistoryTimeoutRef.current) {
        clearTimeout(saveHistoryTimeoutRef.current);
      }
    };
  }, [agentMessages, isLoadingHistory, saveChatHistory]);

  // 监听消息变化并自动滚动
  useEffect(() => {
    if (agentMessages.length > 0) {
      setTimeout(scrollToBottom, 50);
    }
  }, [agentMessages, scrollToBottom]);

  return (
    <div className={`w-[480px] bg-white border-r border-gray-200 flex flex-col ${className}`}>
      {/* 头部 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-xl font-bold mb-2 text-gray-900">🤖 Ignis Agent</h2>
            <p className="text-sm text-gray-600">Enjoy creating games with vibe coding</p>
          </div>
          {currentSessionId && (
            <div className="text-right">
              <div className="text-xs text-gray-500">
                会话: {currentSessionId.split('_')[1]}
              </div>
              <div className="text-xs text-gray-500">
                消息: {agentMessages.length}
              </div>
              <button
                onClick={clearChatHistory}
                className="text-xs text-red-500 hover:text-red-700 mt-1"
                title="清空对话历史"
              >
                清空历史
              </button>
            </div>
          )}
        </div>
      </div>
      
      {/* 消息列表 */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4"
      >
        {isLoadingHistory ? (
          <div className="flex justify-center items-center h-32">
            <div className="flex items-center space-x-2 text-gray-500">
              <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <span className="text-sm">加载对话历史...</span>
            </div>
          </div>
        ) : agentMessages.length === 0 ? (
          <div className="text-center text-gray-500 mt-8">
            <p className="text-sm">还没有对话记录</p>
            <p className="text-xs mt-1">开始与Agent对话吧！</p>
          </div>
        ) : (
          agentMessages.map((message) => (
            <div key={message.id} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`max-w-[80%] rounded-lg px-3 py-2 ${
                message.role === 'user' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-100 text-gray-900'
              }`}>
                {message.role === 'assistant' ? (
                  <div className="prose prose-sm max-w-none overflow-hidden">
                    <ReactMarkdown
                      components={{
                        code: ({ children, className, ...props }) => {
                          if (!className) {
                            return (
                              <code className="bg-gray-200 px-1 py-0.5 rounded text-xs font-mono break-all" {...props}>
                                {children}
                              </code>
                            )
                          }
                          return (
                            <pre className="bg-gray-50 text-gray-900 p-3 rounded-lg overflow-x-auto text-xs border">
                              <code className="break-all whitespace-pre-wrap">{children}</code>
                            </pre>
                          )
                        }
                      }}
                    >
                      {message.content}
                    </ReactMarkdown>
                  </div>
                ) : (
                  <div className="text-sm whitespace-pre-wrap break-words">
                    {message.content}
                  </div>
                )}
                <div className="text-xs opacity-70 mt-1">
                  {message.timestamp.toLocaleTimeString()}
                </div>
              </div>
            </div>
          ))
        )}
        
        {isAgentProcessing && (
          <div className="flex justify-start">
            <div className="bg-gray-100 rounded-lg px-3 py-2 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-100"></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-200"></div>
                <span className="text-gray-700">Agent正在思考...</span>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* 输入区域 */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <textarea
            value={agentInput}
            onChange={(e) => setAgentInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="输入消息... (Enter发送，Shift+Enter换行)"
            className="flex-1 resize-none border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows={2}
            disabled={isAgentProcessing}
          />
          <button
            onClick={sendMessageToAgent}
            disabled={!agentInput.trim() || isAgentProcessing}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
          >
            {isAgentProcessing ? '发送中...' : '发送'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AgentChatPanel;
