"use client";

/**
 * 边界限制测试页面
 * 用于测试和调试WoodCollectScene3D的边界碰撞功能
 */

import React, { useEffect, useRef, useState } from 'react';
import { Engine, Scene } from '@babylonjs/core';
import {
  onSceneReady,
  onRender,
  updatePlayerMovement,
  toggleBoundaryVisualization,
  debugPlayerPosition,
  resetGame,
  getDeckAnalysis
} from '../../components/babylon/scenes/WoodCollectScene3D';

export default function BoundaryTestPage() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [engine, setEngine] = useState<Engine | null>(null);
  const [scene, setScene] = useState<Scene | null>(null);
  const [showBoundaries, setShowBoundaries] = useState(false);

  useEffect(() => {
    if (!canvasRef.current) return;

    // 创建Babylon.js引擎和场景
    const babylonEngine = new Engine(canvasRef.current, true);
    const babylonScene = new Scene(babylonEngine);

    setEngine(babylonEngine);
    setScene(babylonScene);

    // 初始化场景
    onSceneReady(babylonScene).then(() => {
      console.log('[BoundaryTest] 场景初始化完成');
    });

    // 启动渲染循环
    babylonEngine.runRenderLoop(() => {
      onRender();
      babylonScene.render();
    });

    // 处理窗口大小变化
    const handleResize = () => {
      babylonEngine.resize();
    };
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      babylonEngine.dispose();
    };
  }, []);

  // 键盘控制
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const moveSpeed = 1.0;
      let direction = { x: 0, y: 0 };

      switch (event.key.toLowerCase()) {
        case 'w':
        case 'arrowup':
          direction.y = moveSpeed;
          break;
        case 's':
        case 'arrowdown':
          direction.y = -moveSpeed;
          break;
        case 'a':
        case 'arrowleft':
          direction.x = -moveSpeed;
          break;
        case 'd':
        case 'arrowright':
          direction.x = moveSpeed;
          break;
      }

      if (direction.x !== 0 || direction.y !== 0) {
        updatePlayerMovement(direction);
      }
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      if (['w', 's', 'a', 'd', 'arrowup', 'arrowdown', 'arrowleft', 'arrowright'].includes(event.key.toLowerCase())) {
        updatePlayerMovement({ x: 0, y: 0 });
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, []);

  const handleToggleBoundaries = () => {
    const newShowBoundaries = !showBoundaries;
    setShowBoundaries(newShowBoundaries);
    toggleBoundaryVisualization(newShowBoundaries);
  };

  const handleDebugPosition = () => {
    debugPlayerPosition();
  };

  const handleResetGame = () => {
    resetGame();
  };

  const handleAnalyzeDeck = () => {
    getDeckAnalysis();
  };

  return (
    <div style={{ width: '100vw', height: '100vh', position: 'relative' }}>
      <canvas
        ref={canvasRef}
        style={{ width: '100%', height: '100%', display: 'block' }}
      />
      
      {/* 控制面板 */}
      <div style={{
        position: 'absolute',
        top: '20px',
        left: '20px',
        background: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '20px',
        borderRadius: '8px',
        fontFamily: 'monospace',
        fontSize: '14px',
        zIndex: 1000
      }}>
        <h3 style={{ margin: '0 0 15px 0' }}>边界限制测试</h3>
        
        <div style={{ marginBottom: '15px' }}>
          <strong>控制说明：</strong><br />
          WASD 或 方向键：移动角色<br />
          尝试移动到夹板边缘测试碰撞
        </div>

        <div style={{ marginBottom: '15px' }}>
          <button 
            onClick={handleToggleBoundaries}
            style={{
              padding: '8px 16px',
              marginRight: '10px',
              backgroundColor: showBoundaries ? '#ff4444' : '#4444ff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            {showBoundaries ? '隐藏边界' : '显示边界'}
          </button>

          <button 
            onClick={handleDebugPosition}
            style={{
              padding: '8px 16px',
              marginRight: '10px',
              backgroundColor: '#44aa44',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            调试位置
          </button>

          <button
            onClick={handleResetGame}
            style={{
              padding: '8px 16px',
              marginRight: '10px',
              backgroundColor: '#aa4444',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            重置位置
          </button>

          <button
            onClick={handleAnalyzeDeck}
            style={{
              padding: '8px 16px',
              backgroundColor: '#aa44aa',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            分析夹板
          </button>
        </div>

        <div style={{ fontSize: '12px', color: '#cccccc' }}>
          <strong>测试要点：</strong><br />
          1. 角色应该无法走出夹板区域<br />
          2. 碰撞检测应该平滑，无卡顿<br />
          3. 边界范围：X轴(-5 到 5), Z轴(-5.5 到 5.5)<br />
          4. Z轴方向边界应紧贴夹板边缘，无海水间隙<br />
          5. 点击"分析夹板"查看实际模型尺寸<br />
          6. 点击"显示边界"可视化碰撞墙体<br />
          7. 查看控制台输出调试信息
        </div>
      </div>
    </div>
  );
}
