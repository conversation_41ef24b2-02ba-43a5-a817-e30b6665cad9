[{"id": "script_1753357318301", "name": "generated_script", "description": "我需要玩家方块原地旋转", "scriptContent": "function executeScript(scene) {\n  try {\n    const nodeRegistry = NodeRegistry.getInstance();\n    const targetNode = nodeRegistry.getNode('玩家方块');\n    if (!targetNode) return;\n    \n    const mesh = targetNode.babylonNode || targetNode;\n    if (!mesh) return;\n    \n    // 创建Y轴旋转动画\n    BABYLON.Animation.CreateAndStartAnimation(\n      \"playerRotation\",\n      mesh,\n      \"rotation.y\",\n      30,\n      120,\n      mesh.rotation.y,\n      mesh.rotation.y + Math.PI * 2,\n      BABYLON.Animation.ANIMATIONLOOPMODE_CYCLE\n    );\n    \n    if (typeof registerCleanup === 'function') {\n      registerCleanup(() => {\n        scene.stopAnimation(mesh);\n        console.log('玩家方块旋转动画已停止');\n      });\n    }\n    \n  } catch (error) {\n    console.error('脚本执行失败:', error);\n  }\n}", "addedAt": "2025-07-24T11:42:07.606Z", "isActive": true}]